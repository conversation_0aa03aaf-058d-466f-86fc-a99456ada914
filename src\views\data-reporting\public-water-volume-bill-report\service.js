import request from '@/utils/request'

// 新增公管渠水量水费填报
export function addPublicWaterVolumeBill(data) {
  return request({
    url: '/custom/waterQuantityFee/add',
    method: 'post',
    data
  })
}

// 更新公管渠水量水费填报
export function updatePublicWaterVolumeBill(data) {
  return request({
    url: '/custom/waterQuantityFee/update',
    method: 'post',
    data
  })
}

// 删除公管渠水量水费填报
export function deletePublicWaterVolumeBill(id) {
  return request({
    url: '/custom/waterQuantityFee/delete',
    method: 'post',
    params: { id }
  })
}

// 获取公管渠水量水费填报详情
export function getPublicWaterVolumeBillById(id) {
  return request({
    url: '/custom/waterQuantityFee/get',
    method: 'post',
    params: { id }
  })
}

// 获取公管渠水量水费填报列表
export function getPublicWaterVolumeBillList(data) {
  return request({
    url: '/custom/waterQuantityFee/page',
    method: 'post',
    data
  })
}

//获取灌溉轮次日期范围
export function getByIrrigationRound(fillYear, irrigationRound) {
  return request({
    url: '/custom/irrigationSetting/getByIrrigationRound',
    method: 'post',
    params: { fillYear, irrigationRound }
  })
}

// 获取所有灌溉轮次的日期范围
export function getAllIrrigationRounds(fillYear) {
  const promises = []
  // 调用三次接口获取三个灌溉轮次的数据
  for (let i = 1; i <= 3; i++) {
    promises.push(getByIrrigationRound(fillYear, i))
  }
  return Promise.all(promises)
}