<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="事件名称" prop="eventName">
              <a-input v-model="form.eventName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="事件类型" prop="eventType">
              <!-- <a-select show-search placeholder="请输入" v-model="form.emergencyPlanType" option-filter-prop="children">
                <a-select-option v-for="item in emergencyTypeOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select> -->
              <a-select show-search placeholder="请输入" v-model="form.eventType" option-filter-prop="children">
                <a-select-option v-for="item in eventTypeOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="告警级别" prop="warnLevel">
              <a-select show-search placeholder="请输入" v-model="form.warnLevel" option-filter-prop="children">
                <a-select-option v-for="item in warnLevelOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="异常点" prop="cameraId">
              <a-select
                show-search
                placeholder="请输入"
                v-model="form.cameraId"
                option-filter-prop="children"
                @change="cameraChange"
              >
                <a-select-option v-for="item in cameraOptions" :key="item.cameraId" :value="item.cameraId">
                  {{ item.cameraName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="异常描述">
              <a-textarea v-model="form.content" placeholder="请输入" allowClear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="详细位置" prop="location">
              <a-input v-model="form.location" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <!-- <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="批复时间" prop="replyTime">
              <a-date-picker v-model="form.replyTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col> -->

          <!-- <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="负责人" prop="chargeName">
              <a-input v-model="form.chargeName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId"></a-form-model-item>
          </a-col> -->
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="附件">
              <UploadFile :fileUrl.sync="form.attaches" :multiple="true" listType="text" folderName="projectCover" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addEvent, disposeEvent } from '../services'
  // import { addPlan, editPlan, getPlanById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['eventTypeOptions', 'warnLevelOptions', 'cameraOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        tabKey: '1',
        tabPosition: 'left',
        tabAttachKey: '1',
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        form: {
          // approvalAttaches: [],
          // chargeName: '',
          // emergencyPlanId: null,
          // emergencyPlanName: '',
          // emergencyPlanType: null,
          // planAttaches: [],
          // projectId: null,
          // replyTime: '',

          attaches: [],
          cameraId: null,
          content: '',
          eventName: '',
          eventType: '',
          location: '',
          taskId: null,
          warnLevel: '',
        },
        open: false,
        rules: {
          eventName: [{ required: true, message: '事件名称不能为空', trigger: 'blur' }],
          eventType: [{ required: true, message: '事件类型不能为空', trigger: 'change' }],
          warnLevel: [{ required: true, message: '告警级别不能为空', trigger: 'change' }],
          cameraId: [{ required: true, message: '异常点不能为空', trigger: 'change' }],
          location: [{ required: true, message: '详细位置不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.form.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      cameraChange(val) {
        let location = this.cameraOptions.find(item => item.cameraId == val)
        // location = this.cameras[val]?.location
        console.log('66666666666 cameraChange', location)

        this.form.location = location.location
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        // if (row != undefined) {
        // this.formTitle = '修改'
        // this.modalLoading = true
        // getPlanById({ emergencyPlanId: row.emergencyPlanId }).then(res => {
        //   if (res.code == 200) {
        //     this.form = {
        //       ...res.data,
        //       approvalAttaches: res.data.approvalAttaches?.map(el => el.attachUrl),
        //       planAttaches: res.data.planAttaches?.map(el => el.attachUrl),
        //     }
        //     this.form.emergencyPlanType = String(this.form.emergencyPlanType)
        //     this.modalLoading = false
        //   }
        // })
        // }
      },

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.replyTime = moment(this.form.replyTime).format('YYYY-MM-DD')
            if (this.form.emergencyPlanId == null) {
              addEvent(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    // this.reset()
                    this.loading = false
                    this.$emit('close')
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              disposeEvent(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.loading = false
                    this.$message.success('修改成功', 3)
                    this.reset()
                    this.$emit('close')
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }

  ::v-deep .ant-tabs-tabpane {
    // height: calc(100vh - 160px);
  }

  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }

  ::v-deep .attaches {
    // margin-top: 100px;
  }
</style>
