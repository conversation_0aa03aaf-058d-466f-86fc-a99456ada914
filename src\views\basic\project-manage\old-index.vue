<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
        <a-tab-pane key="1" tab="按类别">
          <TreeGeneral
            v-if="treeTabKey === '1'"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="treeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'category')"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="行政区划">
          <TreeGeneral
            v-if="treeTabKey === '2'"
            :key="2"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="districtTreeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'district')"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <a-card :bordered="false" style="margin-bottom: 10px">
        <div class="table-page-search-wrapper">
          <a-form :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-row>
              <a-col :md="8" :sm="24">
                <a-form-item label="工程编码">
                  <a-input
                    v-model="queryParam.projectCode"
                    placeholder="请输入工程编码"
                    allow-clear
                    @keyup.enter.native="handleQuery"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="工程名称">
                  <a-input
                    v-model="queryParam.projectName"
                    placeholder="请输入工程名称"
                    allow-clear
                    @keyup.enter.native="handleQuery"
                  />
                </a-form-item>
              </a-col>
              <a-col>
                <span class="table-page-search-submitButtons" style="float: right">
                  <a-button type="primary" @click="handleQuery">
                    <a-icon type="search" />
                    查询
                  </a-button>
                  <a-button style="margin-left: 8px" @click="resetQuery">
                    <a-icon type="redo" />
                    重置
                  </a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <advance-table
        ref="advanceTableRef"
        :loading="loading"
        :title="tableTitle"
        :columns="columns"
        :data-source="list"
        @refresh="getList"
        rowKey="projectId"
        :expandIcon="expandIcon"
        :expandIconColumnIndex="1"
        :expandedRowKeys="expandedRowKeys"
        @expand="expandNode"
        tableKey="project-manage-table"
        size="middle"
        :pagination="false"
        @change="handleTableChange"
        :format-conditions="true"
        :indentSize="16"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, columnTitle: ' ' }"
      >
        <div class="table-operations" slot="button">
          <a-button type="primary" @click="handleAdd()">
            <a-icon type="plus" />
            新增
          </a-button>
          <a-button type="danger" v-if="!multiple" @click="handleDelete">
            <a-icon type="delete" />
            删除
          </a-button>
        </div>
      </advance-table>
    </div>

    <FormDrawer
      v-if="showFormDrawer"
      :districtOptions="districtTreeOptions.dataSource"
      :projectOptions="treeOptions.dataSource"
      ref="formDrawerRef"
      @ok="onOperationComplete"
      params
      @close="showFormDrawer = false"
    />
  </div>
</template>

<script lang="jsx">
  import { getTreeByCode, deleteProject } from './services'
  import { getDistrictTree, getProjectTree } from '@/api/common'
  import FormDrawer from './modules/FormDrawer'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import TreeGeneral from '@/components/TreeGeneral'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'ProjectManage',
    components: {
      TreeGeneral,
      AdvanceTable,
      FormDrawer,
    },
    data() {
      return {
        treeTabKey: '1',
        treeOptions: {
          getDataApi: getTreeByCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
            value: 'objectCategoryId',
          },
        },
        districtTreeOptions: {
          getDataApi: getDistrictTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'districtName',
            key: 'districtCode',
            value: 'districtCode',
          },
        },
        // 新增编辑
        showFormDrawer: false,

        list: [],
        tableTitle: '',
        selectedRowKeys: [],
        selectedRows: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 树表展开相关
        isExpandIcon: 1,
        isExpandId: null,
        expandedRowKeys: [],

        projectId: '',

        ids: [],
        names: [],
        loading: false,
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        queryParam: {
          projectCode: undefined,
          projectName: undefined,
          objectCategoryId: undefined,
          districtCode: undefined,
        },
        districtTypes: {}, // 行政区划枚举(临时)
        columns: [
          {
            title: '工程编码',
            dataIndex: 'projectCode',
            minWidth: 180,
          },
          {
            title: '工程名称',
            dataIndex: 'projectName',
            minWidth: 180,
          },
          {
            title: '工程类别',
            dataIndex: 'objectCategoryName',
            minWidth: 100,
          },
          {
            title: '行政区划',
            dataIndex: 'districtCode',
            minWidth: 140,
            customRender: (_, r, i) => {
              return this.districtTypes[r.districtCode]?.districtName || ''
            },
          },
          {
            title: '排序号',
            dataIndex: 'sort',
            minWidth: 70,
          },
          {
            title: '备注',
            dataIndex: 'remark',
            minWidth: 100,
          },
          {
            title: '操作',
            dataIndex: 'operation',
            width: 180,
            fixed: 'right',
            customRender: (_, r, i) => {
              return (
                <span>
                  <a onClick={() => this.handleUpdate(r)}>修改</a>
                  <a-divider type='vertical' />
                  <a onClick={() => this.handleAdd(r)}>添加子工程</a>
                  <a-divider type='vertical' />
                  <a onClick={() => this.handleDelete(r)}>删除</a>
                </span>
              )
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      getDistrictTree().then(res => {
        this.districtTreeOptions.dataSource = res.data
        this.districtTypes = getFlatTreeMap(res.data, 'districtCode')
      })
    },
    mounted() {
      this.$refs.advanceTableRef.setTableHeight(285)
    },
    methods: {
      // 获取当前节点下的子节点数据
      expandNode(expanded, record) {
        // 展开收缩时需要动态修改展开行集合
        if (expanded) {
          this.expandedRowKeys.push(record.projectId)
        } else {
          this.expandedRowKeys = this.expandedRowKeys.filter(function (item) {
            return item !== record.projectId
          })
        }

        // if (expanded && !record.children) {
        //   this.loading = true
        // getMenuOfParent(record.menuId).then(res => {
        //   // 拼接懒加载数据
        //   record.children = res.data
        //   this.loading = false
        // })
        // }
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        getProjectTree(this.queryParam).then(response => {
          this.list = response.data
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          projectCode: undefined,
          projectName: undefined,
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete(params) {
        if (params) {
          this.expandedRowKeys.push(params.parentId)
        }
        this.getList()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        this.ids = this.selectedRows.map(item => item.projectId)
        this.names = this.selectedRows.map(item => item.projectName)
        this.single = selectedRowKeys.length !== 1
        this.multiple = !selectedRowKeys.length
      },

      // 树加载完成后
      onTreeMounted(data) {
        if (this.treeTabKey === '1') {
          this.queryParam.objectCategoryId = data[0].objectCategoryId
          this.queryParam.districtCode = ''
          this.tableTitle = data[0].objectCategoryName
          this.treeOptions.dataSource = data
        }
        if (this.treeTabKey === '2') {
          this.queryParam.objectCategoryId = ''
          this.queryParam.districtCode = data[0].districtCode
          this.tableTitle = data[0].districtName

          // if (!Object.keys(this.districtTypes).length) {
          //   this.districtTreeOptions.dataSource = data
          //   this.districtTypes = getFlatTreeMap(data, 'districtCode')
          // }
        }
        this.getList()
      },
      clickTreeNode(node, type) {
        if (type === 'category') {
          this.queryParam.objectCategoryId = node.$options.propsData.eventKey
        }
        if (type === 'district') {
          this.queryParam.districtCode = node.$options.propsData.eventKey
        }

        this.tableTitle = node.$options.propsData.dataRef.title

        this.getList()
      },

      /* 新增 */
      handleAdd(record) {
        this.showFormDrawer = true
        const type = record ? 'leaf' : 'root'
        if (this.treeTabKey === '1') {
          const r = record ? record : { objectCategoryId: this.queryParam.objectCategoryId }
          this.$nextTick(() => this.$refs.formDrawerRef.handleAdd(type, r, '1'))
        }
        if (this.treeTabKey === '2') {
          const r = record ? record : { districtCode: this.queryParam.districtCode }
          this.$nextTick(() => this.$refs.formDrawerRef.handleAdd(type, r, '2'))
        }
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const projectIds = row.projectId ? [row.projectId] : this.ids
        const names = row.projectName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteProject({ projectIds: projectIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.onSelectChange([], [])
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },

      expandIcon(props) {
        let buttonType = '4'
        if (props.record.type == buttonType) {
          return <span style='margin-right:22px'></span>
        } else if (props.record.isLeaf == true) {
          return <span style='margin-right:22px'></span>
        } else {
          if (props.expanded) {
            return (
              <a
                style="color: 'black',margin-right:0px"
                onClick={e => {
                  props.onExpand(props.record, e)
                }}
              >
                <a-icon type='caret-down' />
              </a>
            )
          } else {
            return (
              <a
                style="color: 'black' ,margin-right:0px"
                onClick={e => {
                  props.onExpand(props.record, e)
                }}
              >
                <a-icon type='caret-right' />
              </a>
            )
          }
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
