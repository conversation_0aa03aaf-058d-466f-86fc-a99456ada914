<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="900"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content">
      <Word :wordUrl="wordUrl" />
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="releaseForm" :loading="loading" v-if="reportId">发布</a-button>
      <!-- <a-button v-if="reportId" type="primary" @click="releaseForm">发布</a-button> -->
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { releaseManage } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import Word from '@/components/word/Word'

  export default {
    name: 'FormManage',
    components: { AntModal, Word },
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '预览',
        open: false,
        reportId: null,
        wordUrl: '',
      }
    },
    methods: {
      handleManage(row) {
        this.wordUrl = row.docUrl
        if (row.reportId) {
          this.reportId = row.reportId
          this.formTitle = '确认发布'
        }
        this.open = true
      },
      releaseForm() {
        let that = this
        that.loading = true
        releaseManage({ reportId: this.reportId })
          .then(res => {
            if (res.code == 200) {
              that.$message.success(`发布成功`, 3)
              that.open = false
              that.$emit('previewClose')
            }
          })
          .catch(() => (that.loading = false))
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('previewClose')
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  ::v-deep .modal-content {
    overflow-x: auto;
    height: 100%;
  }
</style>
