<template>
  <div class="tree-table-page">
    <!-- 左侧树 水文，设备时选择对象接口不同-->
    <div class="tree-table-tree-panel">
      <TreePatrolCategory
        style="width: 240px"
        ref="treeGeneralRef"
        :treeOptions="treeOptions"
        :categoryType="categoryType"
        @onTreeMounted="onTreeMounted"
        @select="clickTreeNode"
        v-if="treeOptions.dataSource.length"
      />
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="姓名">
          <a-input v-model="queryParam.name" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
        </a-form-item>
        <a-form-item label="手机号">
          <a-input v-model="queryParam.mobile" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
        </a-form-item>
        <!-- <a-form-item label="预警级别">
          <a-select
            show-search
            placeholder="请输入"
            v-model="queryParam.createdUserId"
            option-filter-prop="children"
            @change="handleWarnLevel"
          >
            <a-select-option v-for="item in warnLevelList" :key="item.userId" :value="item.userId">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item> -->
        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            :rowConfig="{ isCurrent: true, isHover: true }"
            :tablePage="{
              pageNum: queryParam.pageNum,
              pageSize: queryParam.pageSize,
              total,
            }"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="primary" @click="handleSync()">
                <a-icon type="sync" />
                同步
              </a-button>
              <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
    </div>
    <FormDrawer v-if="showForm" ref="formRef" :sexOptions="sexOptions" @ok="onOperation" @close="showForm = false" />
    <select-user selectModel="multi" v-model="selectedUser" v-show="false" @ok="onOperation" ref="selectUserRef" />
  </div>
</template>

<script lang="jsx">
  import { getBaseCategory, getProjectTree } from '@/api/common'
  import { getContactPage, deleteContact, syncOrgUser } from './services'
  import TreePatrolCategory from '@/components/TreePatrolCategory/commonTree.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormDrawer from './modules/FormDrawer.vue'
  import SelectUser from '@/components/pt/selectUser/SelectOrgUser' //选择人员

  export default {
    name: 'maintenance',
    components: {
      VxeTable,
      VxeTableForm,
      TreePatrolCategory,
      SelectUser,
      FormDrawer,
    },
    data() {
      return {
        selectedUser: '',
        treeTabKey: '1',
        categoryType: 'structureCode',
        currentKeys: [],
        sexOptions: [
          { dictKey: '1', dictValue: '男' },
          { dictKey: '2', dictValue: '女' },
        ],
        treeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'categoryName',
            key: 'categoryId',
            value: 'categoryId',
          },
        },
        showForm: false,
        showConfigRule: false,
        configTreeTypes: [],
        configTreeOptions: [],
        list: [],
        tableTitle: '组织架构',
        isChecked: false,
        selectIds: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          categoryId: null,
          categoryType: 'structureCode',
          mobile: '',
          name: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
          // categoryId: 0,
          // msgTplName: '',
          // pageNum: 1,
          // pageSize: 10,
          // sort: []
        },
        columns: [
          { type: 'checkbox', width: 40 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '姓名',
            field: 'name',
          },
          {
            title: '性别',
            field: 'gender',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.sexOptions.find(el => row.gender == el.dictKey)?.dictValue || ''
              },
            },
          },
          {
            title: '手机号',
            field: 'mobile',
          },
          {
            title: '邮件地址',
            field: 'email',
          },
          {
            title: '分组名称',
            field: 'categoryName',
            showOverflow: 'tooltip',
            minWidth: 240,
          },

          {
            title: '操作',
            field: 'operate',
            align: 'left',
            width: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete('one', row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    watch: {
      selectedUser(val) {
        // console.log('sync ', this.queryParam.categoryId, val)
        let userIds = val.ids
        let params = {
          categoryId: this.queryParam.categoryId,
          userIds: userIds.split(','),
        }
        syncOrgUser(params).then(response => {
          this.$message.success('同步成功', 3)
          this.onOperation()
        })
      },
    },
    created() {
      getBaseCategory({ categoryType: 'structureCode' }).then(res => {
        this.treeOptions.dataSource = res?.data
      })
      // this.getList()
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        //treeNodeId
        this.queryParam.pageSize = pageSize
        if (this.queryParam.categoryId) {
          this.getList()
        }
      },
      /** 查询列表 */
      getList() {
        this.loading = true
        // this.list = []
        // this.total = 0
        getContactPage(this.queryParam).then(res => {
          this.list = res?.data?.data
          this.total = res?.data?.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.contactId)
      },
      // 树加载完成后
      onTreeMounted(data) {
        this.queryParam.categoryId = data[0].id
        this.getList()
      },
      clickTreeNode(node) {
        this.queryParam.categoryId = node.$options.propsData.eventKey
        this.queryParam.pageNum = 1
        this.getList()
      },
      handleWarnLevel() {
        //
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          categoryId: 0,
          categoryType: 'structureCode',
          name: null,
          mobile: null,
          pageNum: 1,
        }
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /* 同步 */
      handleSync() {
        if (this.queryParam.categoryId != 0) {
          this.$nextTick(() => this.$refs.selectUserRef.showSelectUser())
        } else {
          this.$message.warning('根节点无法同步，请选择对应节点！')
        }
      },
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle('add', [this.queryParam.categoryId]))
      },
      /* 修改 */
      handleEdit(row) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle('edit', row.contactId))
      },
      /** 删除按钮操作 */
      handleDelete(type, row) {
        let ids
        if (type == 'one') {
          ids = row.contactId
        } else {
          ids = this.selectIds.join(',')
        }
        var that = this
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中名称的数据',
          onOk() {
            deleteContact({ contactIds: ids }).then(res => {
              if (res.code == 200) {
                that.$message.success(`删除成功`, 3)
                that.selectIds = []
                that.onOperation()
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperation() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;

      .ant-tabs-nav-container {
        height: auto;

        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
