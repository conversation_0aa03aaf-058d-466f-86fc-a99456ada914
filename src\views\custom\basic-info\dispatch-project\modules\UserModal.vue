<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="500"
    @cancel="cancel"
    modalHeight="300"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules" layout="inline">
        <a-form-model-item label="姓名" prop="userIds">
          <a-select
            show-search
            mode="multiple"
            option-filter-prop="children"
            v-model="form.userIds"
            :options="userOptions"
            placeholder="请选择"
            style="width: 350px"
          />
        </a-form-model-item>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addUser, getUserOption } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormTemplate',
    components: { AntModal },
    props: ['row'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        open: false,
        userOptions: [],
        form: {
          userIds: [],
        },
        rules: {
          userIds: [{ required: true, message: '请选择人员', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        this.modalLoading = true
        getUserOption({ dispatchProjectId: this.row.dispatchProjectId }).then(res => {
          this.modalLoading = false
          this.userOptions = res.data.map(el => ({ label: el.name, value: `${el.userId}` }))
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            addUser({ dispatchProjectId: this.row.dispatchProjectId, userIds: this.form.userIds.join(',') })
              .then(res => {
                if (res.code == 200) {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                }
              })
              .finally(() => (this.loading = false))
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
</style>
