import request from '@/utils/request'

// 我的简报-列表
export function getMyBriefingList(data) {
  return request({
    url: '/war/user/report/user/page',
    method: 'post',
    data
  })
}
// 我的简报新增---保存
export function addMyBriefingSave(data) {
  return request({
    url: '/war/user/report/conserve/add',
    method: 'post',
    data
  })
}
// 我的简报新增---生成
export function addBriefingGenerate(data) {
  return request({
    url: '/war/user/report/generation/add',
    method: 'post',
    data
  })
}
// 我的简报新增---预览
export function addBriefingPreview(data) {
  return request({
    url: '/war/user/report/preview',
    method: 'post',
    data
  })
}
// 我的简报草稿修改中(保存)--发布状态草稿状态
export function editBriefingSave(data) {
  return request({
    url: '/war/user/report/conserve/update',
    method: 'post',
    data
  })
}
//我的简报草稿修改中(发布)--发布状态草稿状态
export function editBriefingRelease(data) {
  return request({
    url: '/war/user/report/release/update',
    method: 'post',
    data
  })
}
// 删除
export function deleteMyBriefing(params) {
  return request({
    url: '/war/user/report/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}