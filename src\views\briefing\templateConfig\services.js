import request from '@/utils/request'

// 监测站点-列表分页查询
export function getSitePage(data) {
  return request({
    url: '/base/site/page',
    method: 'post',
    data
  })
}

// 模板配置--列表
export function getTemplateList(data) {
  return request({
    url: '/war/reportTemplate/page',
    method: 'post',
    data
  })
}
// 增加
export function addTemplate(data) {
  return request({
    url: '/war/reportTemplate/add',
    method: 'post',
    data
  })
}
export function getTemplateById(params) {
  return request({
    url: '/war/reportTemplate/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 更新
export function editTemplate(data) {
  return request({
    url: '/war/reportTemplate/update',
    method: 'post',
    data
  })
}
// 删除
export function deleteTemplate(params) {
  return request({
    url: '/war/reportTemplate/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
//
export function getTemplateSite(data) {
  return request({
    url: '/war/reportTemplate/site/page',
    method: 'post',
    data
  })
}