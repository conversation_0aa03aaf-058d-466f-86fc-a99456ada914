import request from '@/utils/request'

// 组织架构-新增
export function addContact(data) {
  return request({
    url: '/base/contact/add',
    method: 'post',
    data
  })
}
// 组织架构-更新
export function editContact(data) {
  return request({
    url: '/base/contact/update',
    method: 'post',
    data
  })
}
// 组织架构-删除
export function deleteContact(params) {
  return request({
    url: '/base/contact/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 组织架构-详情
export function getContactDetail(params) {
  return request({
    url: '/base/contact/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 组织架构-查询
export function getContactPage(data) {
  return request({
    url: '/base/contact/page',
    method: 'post',
    data
  })
}

// 组织架构-人员选择同步
export function syncOrgUser(data) {
  return request({
    url: '/base/contact/insertBaseContact',
    method: 'post',
    data
  })
}
