<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="简报名称">
        <a-input v-model="queryParam.reportName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="发布人">
        <a-select
          allow-clear
          show-search
          placeholder="请输入"
          v-model="queryParam.publisherId"
          option-filter-prop="children"
          :filter-option="filterOptionUser"
          @change="handleChangeUser"
        >
          <a-select-option v-for="item in createPersonList" :key="item.userId" :value="item.userId">
            {{ item.name }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="发布状态">
        <a-select
          allow-clear
          show-search
          placeholder="请输入"
          v-model="queryParam.status"
          option-filter-prop="children"
          :filter-option="filterOptionUser"
          :options="statusOptions"
        ></a-select>
      </a-form-item>
      <a-form-item label="创建时间">
        <a-range-picker
          allow-clear
          :value="takeEffect"
          format="YYYY-MM-DD"
          formatValue="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete('all')">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormManage
          v-if="showFormManage"
          ref="formManageRef"
          @ok="onOperationComplete"
          @previewClose="onOperationComplete"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getComUserList } from '@/api/common'
  import { getManageList, deleteManage } from './services'
  import FormManage from './modules/FormManage.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'maintenance',
    components: {
      VxeTable,
      VxeTableForm,
      FormManage,
    },
    data() {
      return {
        showFormManage: false,
        createPersonList: [],
        selectIds: [],
        takeEffect: [],
        list: [],
        tableTitle: '简报审核',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,
        workShiftList: [],
        workGroupList: [],
        lineOptions: [],
        statusOptions: [
          { value: '2', label: '待发布' },
          { value: '3', label: '已发布' },
        ],
        queryParam: {
          endTime: null,
          pageNum: 1,
          pageSize: 10,
          publisherId: undefined,
          status: undefined,
          reportName: '',
          startTime: null,
        },
        columns: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '简报名称',
            field: 'reportName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '发布人',
            field: 'publisherName',
            minWidth: 100,
          },
          {
            title: '发布时间',
            field: 'publishTime',
            minWidth: 180,
          },
          {
            title: '创建人',
            field: 'generateName',
            minWidth: 100,
          },
          {
            title: '创建时间',
            field: 'genTime',
            minWidth: 180,
          },
          {
            title: '发布状态', //状态(1-草稿 2-待发布 3-已发布)
            field: 'status',
            minWidth: 120,
            slots: {
              default: ({ row }) => {
                return (
                  <div class='common-status-box'>
                    <i
                      class={[
                        'common-status-icon',
                        row.status == 2 ? 'common-status-waiting' : 'common-status-completed',
                      ]}
                    ></i>
                    <span>{this.statusOptions.find(el => el.value == row.status)?.label}</span>
                  </div>
                )
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            align: 'left',
            minWidth: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                if (row.status === 2) {
                  if (row.docUrl) {
                    return (
                      <span>
                        <a onClick={() => this.handlePreview(row)}>预览</a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleRelease(row)}>发布</a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDownload(row)}>下载</a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDelete('one', row)}>删除</a>
                      </span>
                    )
                  } else {
                    return (
                      <span>
                        <a onClick={() => this.handlePreview(row)} disabled>
                          预览
                        </a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleRelease(row)} disabled>
                          发布
                        </a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDownload(row)} disabled>
                          下载
                        </a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDelete('one', row)}>删除</a>
                      </span>
                    )
                  }
                } else if (row.status === 3) {
                  if (row.docUrl) {
                    return (
                      <span>
                        <a onClick={() => this.handlePreview(row)}>预览</a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDownload(row)}>下载</a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDelete('one', row)}>删除</a>
                      </span>
                    )
                  } else {
                    return (
                      <span>
                        <a onClick={() => this.handlePreview(row)} disabled>
                          预览
                        </a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDownload(row)} disabled>
                          下载
                        </a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDelete('one', row)}>删除</a>
                      </span>
                    )
                  }
                }
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {
      getComUserList({}).then(res => {
        if (res?.code == 200) {
          this.createPersonList = res?.data
        }
      })
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.startTime = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') + ' 00:00:00' : null
        this.queryParam.endTime = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') + ' 23:59:59' : null
      },
      /** 查询列表 */
      getList() {
        this.showFormManage = false
        this.loading = true
        getManageList(this.queryParam).then(res => {
          this.loading = false
          if (res.code == 200) {
            this.list = res?.data?.data
            this.total = res?.data?.total
          }
        })
      },
      handleChangeUser(value) {
        this.queryParam.publisherId = value
      },
      filterOptionUser(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.reportId)
        // this.names = valObj.records.map(item => item.itemName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          endTime: null,
          pageNum: 1,
          publisherId: undefined,
          status: undefined,
          reportName: '',
          startTime: null,
        }
        this.takeEffect = []
        this.handleQuery()
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.reportId)
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      handlePreview(record) {
        const previewData = {
          docUrl: record.docUrl,
        }
        this.showFormManage = true
        this.$nextTick(() => this.$refs.formManageRef.handleManage(previewData))
      },
      /* 发布releaseManage */
      handleRelease(record) {
        const releaseData = {
          reportId: record.reportId,
          docUrl: record.docUrl,
        }
        this.showFormManage = true
        this.$nextTick(() => this.$refs.formManageRef.handleManage(releaseData))
      },
      handleDownload(record) {
        window.location.href = record.docUrl
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const manageIds = row?.reportId ? [row?.reportId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteManage({ reportIds: manageIds.join(',') }).then(res => {
              that.$message.success(`删除成功`, 3)
              that.onOperationComplete()
            })
          },
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
