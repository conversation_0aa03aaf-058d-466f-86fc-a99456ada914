<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    :adjust-size="true"
    modalWidth="1000"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content">
      <a-form-model ref="templateForm" :model="templateForm" :rules="rules" :labelCol="labelCol">
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="模板名称" prop="reportTemplateName" class="form-item">
              <a-input allowClear v-model="templateForm.reportTemplateName" :maxLength="20" placeholder="请输入" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="简报标题" prop="reportTitle" class="form-item">
              <a-input allowClear v-model="templateForm.reportTitle" :maxLength="20" placeholder="请输入" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="简报报送单位" prop="reportUnit" class="form-item" :labelCol="{ span: 8 }">
              <a-input allowClear v-model="templateForm.reportUnit" :maxLength="20" placeholder="请输入" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <div class="form-item-title">配置模板内容</div>
        <a-row>
          <a-transfer
            ref="temFormTableRef"
            :titles="['选择项', '已选项']"
            :data-source="configList"
            :rowKey="record => record.reportItemId.toString()"
            :target-keys="targetKeys"
            :show-search="showSearch"
            :filter-option="(inputValue, item) => item.title.indexOf(inputValue) !== -1"
            :show-select-all="false"
            @change="onChangeConfig"
          >
            <template
              slot="children"
              slot-scope="{ props: { direction, filteredItems, selectedKeys }, on: { itemSelectAll, itemSelect } }"
            >
              <a-table
                :row-selection="getRowSelectionConfig({ selectedKeys, itemSelectAll, itemSelect })"
                :columns="direction === 'left' ? configLeftColumns : configRightColumns"
                :data-source="filteredItems"
                class="tem-table"
                size="small"
                :pagination="false"
                :custom-row="
                  ({ reportItemId }) => ({
                    on: {
                      click: () => {
                        // itemSelect(reportItemId, !selectedKeys.includes(reportItemId));
                      },
                    },
                  })
                "
              />
            </template>
          </a-transfer>
        </a-row>
        <div class="form-item-title">分配统计站点</div>
        <a-row>
          <a-col :span="7" class="form-item">
            <label>行政区划：</label>
            <a-tree-select
              allowClear
              v-model="querySite.districtCode"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :replaceFields="{
                children: 'children',
                title: 'districtName',
                key: 'districtCode',
                value: 'districtCode',
              }"
              :tree-data="districtTreeOptions"
              placeholder="请选择"
              tree-default-expand-all
              @change="siteSearch"
            ></a-tree-select>
          </a-col>
          <a-col :span="7" class="form-item marL10">
            <label>所在流域：</label>
            <a-tree-select
              allowClear
              v-model="querySite.riverSystemCategoryId"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :replaceFields="{
                children: 'children',
                title: 'objectCategoryName',
                key: 'objectCategoryId',
                value: 'objectCategoryId',
              }"
              :tree-data="riverTreeOptions"
              placeholder="请选择"
              tree-default-expand-all
              @change="siteSearch"
            ></a-tree-select>
          </a-col>
          <a-col :span="7" class="form-item marL10">
            <label>站点类型：</label>
            <a-tree-select
              allowClear
              v-model="querySite.objectCategoryId"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :replaceFields="{
                children: 'children',
                title: 'objectCategoryName',
                key: 'objectCategoryId',
                value: 'objectCategoryId',
              }"
              :tree-data="sideTreeData"
              placeholder="请选择"
              tree-default-expand-all
              @change="siteSearch"
            ></a-tree-select>
          </a-col>
        </a-row>
        <a-row class="site-transfer">
          <a-transfer
            ref="siteFormTableRef"
            :titles="['选择项', '已选项']"
            :data-source="monitoringStation"
            :rowKey="record => record.siteId.toString()"
            :target-keys="siteTargetKeys"
            :show-search="showSearch"
            :filter-option="(inputValue, item) => item.title.indexOf(inputValue) !== -1"
            :show-select-all="false"
            @change="onChangeSite"
          >
            <template
              slot="children"
              slot-scope="{ props: { direction, filteredItems, selectedKeys }, on: { itemSelectAll, itemSelect } }"
            >
              <a-table
                :row-selection="getRowSelectionSite({ selectedKeys, itemSelectAll, itemSelect })"
                :columns="direction === 'left' ? siteLeftColumns : siteRightColumns"
                :data-source="filteredItems"
                size="small"
                class="tem-table"
                :pagination="false"
                :custom-row="
                  ({ siteId }) => ({
                    on: {
                      click: () => {
                        itemSelect(siteId, !selectedKeys.includes(siteId))
                      },
                    },
                  })
                "
              />
            </template>
          </a-transfer>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <!-- <a-button type="primary" @click="submitPreview">预览</a-button> -->
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addTemplate, editTemplate, getTemplateById, getTemplateSite } from '../services'
  import { getSitePage } from '@/views/basic/site-manage/services'
  import { getOptions, getDistrictTree, getSideTreeByCode } from '@/api/common'
  import { getTreeByCode } from '@/views/basic/river-system/services'
  import { getMaintenanceList } from '@/views/briefing/maintenance/services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import TreeGeneral from '@/components/TreeGeneral'
  import difference from 'lodash/difference'
  import 'ant-design-vue/es/transfer/style'
  import * as _ from 'lodash'
  import ATransfer from 'ant-design-vue/es/transfer'
  import Sortable from 'sortablejs' // 列交换第三方插件

  export default {
    name: 'FormTemplate',
    components: { AntModal, ATransfer, TreeGeneral },
    data() {
      return {
        loading: false,
        modalLoading: false,
        labelCol: { span: 6 },
        districtTreeOptions: [],
        riverTreeOptions: [],
        sideTreeData: [],
        configList: [],
        briefingTypeList: [],
        querySite: {
          objectCategoryId: undefined, //监测站点
          districtCode: undefined, //行政区划代码
          riverSystemCategoryId: null, //(流域)水利对象分类id
          pageNum: 1,
          pageSize: 9999,
        },
        monitoringStation: [],
        formTitle: '',
        templateForm: {
          reportTemplateName: null,
          reportTitle: null,
          reportUnit: null,
        },
        open: false,
        rules: {
          reportTemplateName: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
          reportTitle: [{ required: true, message: '简报标题不能为空', trigger: 'blur' }],
          reportUnit: [{ required: true, message: '简报报送单位不能为空', trigger: 'blur' }],
        },
        targetKeys: [],
        siteTargetKeys: [],
        showSearch: true,
        configLeftColumns: [
          {
            title: '序号',
            customRender: (_, row, rowIndex) => {
              return rowIndex + 1
            },
          },
          {
            dataIndex: 'reportItemName',
            title: '发布项名称',
            key: 'reportItemName',
          },
          {
            title: '类型',
            dataIndex: 'reportItemType',
            scopedSlots: { customRender: 'reportItemType' },
            customRender: (_, row, i) => {
              return this.getTypeNameById(row.reportItemType)
            },
          },
        ],
        configRightColumns: [
          {
            title: '序号',
            customRender: (_, row, rowIndex) => {
              return rowIndex + 1
            },
          },
          {
            dataIndex: 'reportItemName',
            title: '发布项名称',
            key: 'reportItemName',
          },
          {
            title: '类型',
            dataIndex: 'reportItemType',
            scopedSlots: { customRender: 'reportItemType' },
            customRender: (_, row, i) => {
              return this.getTypeNameById(row.reportItemType)
            },
          },
        ],
        siteLeftColumns: [
          {
            title: '序号',
            width: '40px',
            customRender: (_, row, rowIndex) => {
              return rowIndex + 1
            },
          },
          {
            dataIndex: 'siteName',
            title: '站点名称',
            width: '110px',
            key: 'siteName',
          },
          {
            dataIndex: 'objectCategoryName',
            title: '站点类型',
            key: 'objectCategoryName',
          },
        ],
        siteRightColumns: [
          {
            title: '序号',
            width: '40px',
            customRender: (_, row, rowIndex) => {
              return rowIndex + 1
            },
          },
          {
            dataIndex: 'siteName',
            title: '站点名称',
            width: '110px',
            key: 'siteName',
          },
          {
            dataIndex: 'objectCategoryName',
            title: '站点类型',
            key: 'objectCategoryName',
          },
          {
            dataIndex: 'isImportant',
            title: '是否重点',
            scopedSlots: { customRender: 'isImportant' },
            customRender: (_, row, i) => {
              return <a-switch size='small' v-model={row.isImportant} onClick={() => this.switchChange(row)}></a-switch>
            },
          },
        ],
      }
    },
    created() {
      getDistrictTree().then(res => {
        this.districtTreeOptions = res.data
      })
      //流域
      getTreeByCode().then(res => {
        this.riverTreeOptions = res.data
      })
      getSideTreeByCode().then(res => {
        this.sideTreeData = res.data
      })
      getOptions('publicationItem').then(res => {
        this.briefingTypeList = res?.data || []
      })
      getMaintenanceList({ pageNum: 1, pageSize: 9999 }).then(res => {
        if (res.code == 200) {
          this.configList = res?.data?.data
          this.configList.forEach(item => {
            item.key = item.reportItemId.toString()
            item.title = item.reportItemName
          })
        }
      })
      //监测站点
      this.siteSearch()
    },
    mounted() {
      setTimeout(() => {
        this.sortableTableTop()
        this.sortableTableBottom()
      }, 1500)
    },
    methods: {
      siteSearch() {
        getTemplateSite(this.querySite).then(res => {
          if (res.code == 200 && res?.data?.data) {
            this.monitoringStation = res?.data?.data
            this.monitoringStation.forEach(item => {
              item.key = item.siteId.toString()
              ;(item.title = item.siteName), (item.isImportant = false)
            })
          }
        })
      },
      onChangeConfig(nextTargetKeys) {
        this.targetKeys = nextTargetKeys
      },
      getRowSelectionConfig({ selectedKeys, itemSelectAll, itemSelect }) {
        return {
          onSelectAll(selected, selectedRows) {
            const treeSelectedKeys = selectedRows.map(({ key }) => key)
            const diffKeys = selected
              ? difference(treeSelectedKeys, selectedKeys)
              : difference(selectedKeys, treeSelectedKeys)
            itemSelectAll(diffKeys, selected)
          },
          onSelect({ key }, selected) {
            itemSelect(key, selected)
          },
          selectedRowKeys: selectedKeys,
        }
      },
      onChangeSite(nextTargetKeys) {
        this.siteTargetKeys = nextTargetKeys
      },
      getRowSelectionSite({ selectedKeys, itemSelectAll, itemSelect }) {
        return {
          onSelectAll(selected, selectedRows) {
            const treeSelectedKeys = selectedRows.map(({ key }) => key)
            const diffKeys = selected
              ? difference(treeSelectedKeys, selectedKeys)
              : difference(selectedKeys, treeSelectedKeys)
            itemSelectAll(diffKeys, selected)
          },
          onSelect({ key }, selected) {
            itemSelect(key, selected)
          },
          selectedRowKeys: selectedKeys,
        }
      },
      switchChange(row) {
        console.log(row)
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      /** 新增按钮操作 */
      handleTemplate(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.formTitle = '修改'
          this.modalLoading = true
          getTemplateById({ reportTemplateId: row.reportTemplateId }).then(res => {
            this.modalLoading = false
            if (res.code == 200) {
              this.templateForm.reportTemplateName = res.data.reportTemplateName
              this.templateForm.reportTitle = res.data.reportTitle
              this.templateForm.reportUnit = res.data.reportUnit
              this.templateForm.reportTemplateId = res.data.reportTemplateId
              if (res.data.reportItemIds.length != 0) {
                this.targetKeys = res.data?.reportItemIds?.map(val => val.toString())
              }
              if (res.data?.sites?.length != 0) {
                for (let i = 0; i < res.data?.sites.length; i++) {
                  this.siteTargetKeys.push(res.data?.sites[i].siteId.toString())
                  for (let j = 0; j < this.monitoringStation.length; j++) {
                    if (
                      res.data?.sites[i].siteId == this.monitoringStation[j].key &&
                      res.data?.sites[i].isImportantSite == 1
                    ) {
                      this.monitoringStation[j].isImportant = true
                    }
                  }
                }
              }
            }
          })
        }
      },
      submitPreview() {
        this.$refs.templateForm.validate(valid => {
          if (valid) {
          }
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.templateForm.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.targetKeys.length != 0) {
              this.templateForm.reportItemIds = this.targetKeys.map(val => parseInt(val))
            }
            let stationList = []
            if (this.siteTargetKeys.length != 0) {
              for (let i = 0; i < this.siteTargetKeys.length; i++) {
                for (let j = 0; j < this.monitoringStation.length; j++) {
                  if (Number(this.siteTargetKeys[i]) == this.monitoringStation[j].siteId) {
                    if (this.monitoringStation[j].isImportant) {
                      this.monitoringStation[j].isImportant = 1
                    } else {
                      this.monitoringStation[j].isImportant = 0
                    }
                    stationList.push({
                      isImportantSite: this.monitoringStation[j].isImportant,
                      siteId: Number(this.siteTargetKeys[i]),
                    })
                  }
                }
              }
              this.templateForm.sites = stationList
            }
            if (this.templateForm.reportTemplateId == null) {
              addTemplate(this.templateForm)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editTemplate(this.templateForm)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
      // 列拖拽
      sortableTableTop() {
        this.$nextTick(() => {
          const $table = this.$refs.temFormTableRef
          this.sortableIns = Sortable.create(
            $table.$el.querySelector('.ant-transfer .ant-transfer-list:last-child .ant-table-tbody'),
            {
              animation: 300,
              delay: 100,
              // 禁用html5原生拖拽行为
              forceFallback: true,
              onEnd: ({ item, newIndex, oldIndex }) => {
                const sortArr = [...this.targetKeys]
                const currRow = sortArr.splice(oldIndex, 1)[0]
                sortArr.splice(newIndex, 0, currRow)
                this.targetKeys = [...sortArr]
              },
            },
          )
        })
      },
      sortableTableBottom() {
        this.$nextTick(() => {
          const $table = this.$refs.siteFormTableRef
          this.sortableIns = Sortable.create(
            $table.$el.querySelector('.ant-transfer .ant-transfer-list:last-child .ant-table-tbody'),
            {
              animation: 300,
              delay: 100,
              // 禁用html5原生拖拽行为
              forceFallback: true,
              onEnd: ({ item, newIndex, oldIndex }) => {
                const sortArr = [...this.siteTargetKeys]
                const currRow = sortArr.splice(oldIndex, 1)[0]
                sortArr.splice(newIndex, 0, currRow)
                this.siteTargetKeys = [...sortArr]
              },
            },
          )
        })
      },
      getTypeNameById(type) {
        let row = this.briefingTypeList.find(item => item.key == type)
        return row?.value
      },
      getDistrictNameByCode(code) {
        let row = this.districtTreeOptions.find(item => item.districtCode == code)
        return row?.value
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  ::v-deep .ant-transfer-list-header {
    background: rgba(170, 196, 255, 0.25);
    padding: 3px 10px;
  }
  ::v-deep .ant-transfer-list-body-search-wrapper {
    padding: 0 12px;
    .ant-transfer-list-search-action {
      top: -3px;
    }
    .ant-input {
      height: 26px;
    }
  }
  ::v-deep .ant-transfer-list {
    width: 300px;
  }
  .modal-content {
    .form-item-title {
      color: #1890ff;
      line-height: 30px;
    }
    .form-item {
      display: flex;
      margin-bottom: 0;
      label {
        white-space: nowrap;
        line-height: 30px;
      }
      ::v-deep .ant-form-item-control-wrapper {
        width: 450px;
      }
    }
    .ant-transfer ::v-deep .ant-table {
      max-height: 170px;
      overflow-x: auto;
    }
    .site-transfer {
      margin-top: 10px;
    }
  }
  .marL10 {
    margin-left: 10px;
  }
  ::v-deep .tem-sortable-ghost {
    background-color: #dfecfb;
  }

  ::v-deep .tem-sortable-drag {
    background-color: #fbfbfb;
    border-radius: 5px;
    box-shadow: 0 0 10px #000;
    width: auto !important;
  }
</style>
