<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <!-- style="max-width: 240px; min-width: 220px" -->
    <div class="tree-table-tree-panel">
      <TreeOrg
        v-if="treeOrgTabKey == '0'"
        style="min-width: 130px; max-width: 160px"
        ref="treeOrgRef"
        :treeOptions="orgTreeOptions"
        @select="node => clickTreeNodeOrg(node, 'org')"
        @onTreeMountedOrg="onTreeMountedOrg"
      />
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <div class="table-panel" layout="vertical">
        <!-- 基础类别 -->
        <div class="tree-panel">
          <a-tabs type="card" v-model="treeTabKey" @change="key => (treeTabKey = key)">
            <a-tab-pane key="1" tab="工程" class="tab-pane">
              <TreeGeneral
                v-if="treeTabKey === '1'"
                :key="1"
                hasTab
                class="type-tree-panel"
                ref="treeGeneralRef"
                :treeOptions="projectTreeOptions"
                @onTreeMounted="onTreeMounted"
                @select="node => clickTreeNode(node, 'HP')"
              />
            </a-tab-pane>
            <a-tab-pane key="2" tab="站点" class="tab-pane">
              <TreeGeneral
                v-if="treeTabKey == '2'"
                :key="2"
                hasTab
                class="type-tree-panel"
                ref="treeGeneralRef"
                :treeOptions="treeOptions"
                @onTreeMounted="onTreeMounted"
                @select="node => clickTreeNode(node, 'MS')"
              />
            </a-tab-pane>
            <a-tab-pane key="3" tab="水系" class="tab-pane">
              <TreeGeneral
                v-if="treeTabKey === '3'"
                :key="3"
                hasTab
                class="type-tree-panel"
                ref="treeGeneralRef"
                :treeOptions="riverTreeOptions"
                @onTreeMounted="onTreeMounted"
                @select="node => clickTreeNode(node, 'RL')"
              />
            </a-tab-pane>
            <a-tab-pane key="4" tab="其他" class="tab-pane">
              <TreeGeneral
                v-if="treeTabKey === '4'"
                :key="4"
                hasTab
                class="type-tree-panel"
                ref="treeGeneralRef"
                :treeOptions="otherTreeOptions"
                @onTreeMounted="onTreeMounted"
                @select="node => clickTreeNode(node, 'EX')"
              />
            </a-tab-pane>
          </a-tabs>
        </div>
        <!-- 数据授权 工程类 -->
        <div class="transfer-panel" v-if="isShowTransferHP" :title="tableTitle">
          <div class="left-table">
            <div class="tab-title">
              <div class="left-span">选择项</div>
              <div class="right-span">共{{ leftDataTotal }}项</div>
            </div>
            <div class="tab-item">
              <div class="item">
                编码：
                <a-input-search
                  class="tab-input"
                  v-model="leftParamHP.projectCode"
                  placeholder="请输入编码"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
              <div class="item">
                名称：
                <a-input-search
                  class="tab-input"
                  v-model="leftParamHP.projectName"
                  placeholder="请输入名称"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
            </div>

            <VxeTable
              ref="vxeTableRef"
              class="vxetab-table"
              :otherHeight="40"
              :isShowTableHeader="false"
              :columns="leftColumnsHP"
              :tableData="leftDataHP"
              :loading="leftLoading"
              :isAdaptPageSize="true"
              @adaptPageSizeChange="adaptPageSizeChange"
              @selectChange="leftSelectChange"
              :tablePage="{ pageNum: leftParamHP.pageNum, pageSize: leftParamHP.pageSize, total: leftDataTotal }"
              @handlePageChange="handlePageChangeHP"
            ></VxeTable>
          </div>
          <div class="table-button">
            <div class="tab-group-div">
              <div class="tab-group">
                <a-button size="small" class="tab-btn" type="primary" :disabled="!hasSelected" @click="add">
                  <a-icon type="right" />
                </a-button>
                <a-button size="small" class="tab-btn" type="primary" :disabled="!hasObjSelected" @click="del">
                  <a-icon type="left" />
                </a-button>
              </div>
            </div>
          </div>
          <div class="right-table">
            <div class="tab-title">
              <div class="left-span">已选项</div>
              <div class="right-span">共{{ rightDataTotal }}项</div>
            </div>

            <div class="tab-item">
              <div class="item">
                编码：
                <a-input-search
                  class="tab-input"
                  v-model="paramObject.resourceCode"
                  placeholder="请输入编码"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
              <div class="item">
                名称：
                <a-input-search
                  class="tab-input"
                  v-model="paramObject.resourceName"
                  placeholder="请输入名称"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
            </div>

            <VxeTable
              ref="vxeTableRef"
              class="vxetab-table"
              :otherHeight="40"
              :isShowTableHeader="false"
              :columns="rightColumns"
              :tableData="rightDataHP"
              :loading="rightLoading"
              :isAdaptPageSize="true"
              @adaptPageSizeChange="adaptPageSizeChange"
              @selectChange="onSelectObjChange"
              :tablePage="{ pageNum: paramObject.pageNum, pageSize: paramObject.pageSize, total: rightDataTotal }"
              @handlePageChange="handleChange"
            ></VxeTable>
          </div>
        </div>
        <!-- 数据授权 站点类 -->
        <div class="transfer-panel" v-if="isShowTransferMS" :title="tableTitle">
          <!-- 左侧 选择项 -->
          <div class="left-table">
            <div class="tab-title">
              <div class="left-span">选择项</div>
              <div class="right-span">共{{ leftDataTotal }}项</div>
            </div>

            <div class="tab-item">
              <div class="item">
                编码：
                <a-input-search
                  class="tab-input"
                  v-model="leftParamMS.siteCode"
                  placeholder="请输入编码"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
              <div class="item">
                名称：
                <a-input-search
                  class="tab-input"
                  v-model="leftParamMS.siteName"
                  placeholder="请输入名称"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
            </div>

            <VxeTable
              ref="vxeTableRef"
              class="vxetab-table"
              :otherHeight="40"
              :isShowTableHeader="false"
              :columns="leftColumnsMS"
              :tableData="leftDataMS"
              :loading="leftLoading"
              :isAdaptPageSize="true"
              @adaptPageSizeChange="adaptPageSizeChange"
              @selectChange="leftSelectChange"
              :tablePage="{ pageNum: leftParamMS.pageNum, pageSize: leftParamMS.pageSize, total: leftDataTotal }"
              @handlePageChange="handlePageChangeMS"
            ></VxeTable>
          </div>
          <!-- 新增、删除按钮 -->
          <div class="table-button">
            <div class="tab-group-div">
              <div class="tab-group">
                <a-button size="small" class="tab-btn" type="primary" :disabled="!hasSelected" @click="add">
                  <a-icon type="right" />
                </a-button>
                <a-button size="small" class="tab-btn" type="primary" :disabled="!hasObjSelected" @click="del">
                  <a-icon type="left" />
                </a-button>
              </div>
            </div>
          </div>
          <!-- 右侧 已选项 -->
          <div class="right-table">
            <div class="tab-title">
              <div class="left-span">已选项</div>
              <div class="right-span">共{{ rightDataTotal }}项</div>
            </div>

            <div class="tab-item">
              <div class="item">
                编码：
                <a-input-search
                  class="tab-input"
                  v-model="paramObject.resourceCode"
                  placeholder="请输入编码"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
              <div class="item">
                名称：
                <a-input-search
                  class="tab-input"
                  v-model="paramObject.resourceName"
                  placeholder="请输入名称"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
            </div>

            <VxeTable
              ref="vxeTableRef"
              class="vxetab-table"
              :otherHeight="40"
              :isShowTableHeader="false"
              :columns="rightColumns"
              :tableData="rightDataMS"
              :loading="rightLoading"
              :isAdaptPageSize="true"
              @adaptPageSizeChange="adaptPageSizeChange"
              @selectChange="onSelectObjChange"
              :tablePage="{ pageNum: paramObject.pageNum, pageSize: paramObject.pageSize, total: rightDataTotal }"
              @handlePageChange="handleChange"
            ></VxeTable>
          </div>
        </div>
        <!-- 数据授权 水系类 -->
        <div class="transfer-panel" v-if="isShowTransferRL" :title="tableTitle">
          <div class="left-table">
            <div class="tab-title">
              <div class="left-span">选择项</div>
              <div class="right-span">共{{ leftDataTotal }}项</div>
            </div>

            <div class="tab-item">
              <div class="item">
                编码：
                <a-input-search
                  class="tab-input"
                  v-model="leftParamRL.riverSystemCode"
                  placeholder="请输入编码"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
              <div class="item">
                名称：
                <a-input-search
                  class="tab-input"
                  v-model="leftParamRL.riverSystemName"
                  placeholder="请输入名称"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
            </div>

            <VxeTable
              ref="vxeTableRef"
              class="vxetab-table"
              :otherHeight="40"
              :isShowTableHeader="false"
              :columns="leftColumnsRL"
              :tableData="leftDataRL"
              :loading="leftLoading"
              :isAdaptPageSize="true"
              @adaptPageSizeChange="adaptPageSizeChange"
              @selectChange="leftSelectChange"
              :tablePage="{ pageNum: leftParamRL.pageNum, pageSize: leftParamRL.pageSize, total: leftDataTotal }"
              @handlePageChange="handlePageChangeRL"
            ></VxeTable>
          </div>
          <div class="table-button">
            <div class="tab-group-div">
              <div class="tab-group">
                <a-button size="small" class="tab-btn" type="primary" :disabled="!hasSelected" @click="add">
                  <a-icon type="right" />
                </a-button>
                <a-button size="small" class="tab-btn" type="primary" :disabled="!hasObjSelected" @click="del">
                  <a-icon type="left" />
                </a-button>
              </div>
            </div>
          </div>
          <div class="right-table">
            <div class="tab-title">
              <div class="left-span">已选项</div>
              <div class="right-span">共{{ rightDataTotal }}项</div>
            </div>

            <div class="tab-item">
              <div class="item">
                编码：
                <a-input-search
                  class="tab-input"
                  v-model="paramObject.resourceCode"
                  placeholder="请输入编码"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
              <div class="item">
                名称：
                <a-input-search
                  class="tab-input"
                  v-model="paramObject.resourceName"
                  placeholder="请输入名称"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
            </div>

            <VxeTable
              ref="vxeTableRef"
              class="vxetab-table"
              :otherHeight="40"
              :isShowTableHeader="false"
              :columns="rightColumns"
              :tableData="rightDataRL"
              :loading="rightLoading"
              :isAdaptPageSize="true"
              @adaptPageSizeChange="adaptPageSizeChange"
              @selectChange="onSelectObjChange"
              :tablePage="{ pageNum: paramObject.pageNum, pageSize: paramObject.pageSize, total: rightDataTotal }"
              @handlePageChange="handleChange"
            ></VxeTable>
          </div>
        </div>
        <!-- 数据授权 工程类 -->
        <div class="transfer-panel" v-if="isShowTransferEX" :title="tableTitle">
          <div class="left-table">
            <div class="tab-title">
              <div class="left-span">选择项</div>
              <div class="right-span">共{{ leftDataTotal }}项</div>
            </div>
            <div class="tab-item">
              <div class="item">
                编码：
                <a-input-search
                  class="tab-input"
                  v-model="leftParamEX.otherObjectCode"
                  placeholder="请输入编码"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
              <div class="item">
                名称：
                <a-input-search
                  class="tab-input"
                  v-model="leftParamEX.otherObjectName"
                  placeholder="请输入名称"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
            </div>

            <VxeTable
              ref="vxeTableRef"
              class="vxetab-table"
              :otherHeight="40"
              :isShowTableHeader="false"
              :columns="leftColumnsEX"
              :tableData="leftDataEX"
              :loading="leftLoading"
              :isAdaptPageSize="true"
              @adaptPageSizeChange="adaptPageSizeChange"
              @selectChange="leftSelectChange"
              :tablePage="{ pageNum: leftParamEX.pageNum, pageSize: leftParamEX.pageSize, total: leftDataTotal }"
              @handlePageChange="handlePageChangeEX"
            ></VxeTable>
          </div>
          <div class="table-button">
            <div class="tab-group-div">
              <div class="tab-group">
                <a-button size="small" class="tab-btn" type="primary" :disabled="!hasSelected" @click="add">
                  <a-icon type="right" />
                </a-button>
                <a-button size="small" class="tab-btn" type="primary" :disabled="!hasObjSelected" @click="del">
                  <a-icon type="left" />
                </a-button>
              </div>
            </div>
          </div>
          <div class="right-table">
            <div class="tab-title">
              <div class="left-span">已选项</div>
              <div class="right-span">共{{ rightDataTotal }}项</div>
            </div>

            <div class="tab-item">
              <div class="item">
                编码：
                <a-input-search
                  class="tab-input"
                  v-model="paramObject.resourceCode"
                  placeholder="请输入编码"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
              <div class="item">
                名称：
                <a-input-search
                  class="tab-input"
                  v-model="paramObject.resourceName"
                  placeholder="请输入名称"
                  allow-clear
                  @search="handleQuery"
                  @keyup.enter.native="handleQuery"
                />
              </div>
            </div>

            <VxeTable
              ref="vxeTableRef"
              class="vxetab-table"
              :otherHeight="40"
              :isShowTableHeader="false"
              :columns="rightColumns"
              :tableData="rightDataEX"
              :loading="rightLoading"
              :isAdaptPageSize="true"
              @adaptPageSizeChange="adaptPageSizeChange"
              @selectChange="onSelectObjChange"
              :tablePage="{ pageNum: paramObject.pageNum, pageSize: paramObject.pageSize, total: rightDataTotal }"
              @handlePageChange="handleChange"
            ></VxeTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import {
    getConfigTree,
    dataAuthAdd,
    dataAuthDel,
    getPatrolObjectSiteList,
    getPatrolObjectRiverList,
    getPatrolObjectProjectList,
    getPatrolObjectOtherList,
    getTreeByCode,
    getTreeByRiverCode,
    getTreeByOtherCode,
    getTreeByProjectCode,
    getPatrolObjectPage,
  } from './services'
  import { getDistrictTree, getProjectTree } from '@/api/common'
  import TreeGeneral from '@/components/TreeGeneral'
  import TreeOrg from '@/components/TreeOrg'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  // import VxeTable from '@/components/VxeTable'
  import VxeTable from '@/components/VxeTable/permission'
  // import VxeTable from '@/components/VxeTable/DataPermission'
  import VxeTableForm from '@/components/VxeTableForm'
  import PermissionTransfer from '@/components/PermissionTransfer'
  import 'ant-design-vue/es/transfer/style'
  import * as _ from 'lodash'
  import difference from 'lodash/difference'

  export default {
    name: 'DataPermission',
    components: {
      PermissionTransfer,
      TreeGeneral,
      TreeOrg,
      VxeTable,
      VxeTableForm,
    },
    data() {
      return {
        orgTreeOptions: {
          getDataApi: getConfigTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'deptName',
            key: 'deptId',
            value: 'deptId',
          },
        },
        treeOptions: {
          getDataApi: getTreeByCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
          },
        },
        riverTreeOptions: {
          getDataApi: getTreeByRiverCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
          },
        },
        projectTreeOptions: {
          getDataApi: getTreeByProjectCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
          },
        },
        otherTreeOptions: {
          getDataApi: getTreeByOtherCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
          },
        },
        treeTabKey: '1',
        treeOrgTabKey: '0',
        isShowTransferMS: false,
        isShowTransferHP: false,
        isShowTransferRL: false,
        isShowTransferEX: false,
        propsTargetKeys: [],
        objectCategoryId: null, //当前选中类型id
        objectOrgId: null, //当前机构id

        tableTitle: '',
        isChecked: false,
        selectedRows: [],
        ids: [],
        names: [],
        leftLoading: false,
        rightLoading: false,
        total: 0,

        queryParam: {
          siteCode: undefined,
          siteName: undefined,
          objectCategoryId: undefined,
          districtCode: undefined,
          pageNum: 1,
          pageSize: 14,
          sort: [],
        },
        districtTypes: {},
        siteTypes: {},

        selectedObjRowKeys: [],
        selectedRowKeys: [], // Check here to configure the default column
        leftDataMS: [],
        rightDataMS: [],
        leftDataRL: [],
        rightDataRL: [],
        leftDataHP: [],
        rightDataHP: [],
        leftDataEX: [],
        rightDataEX: [],
        leftDataTotal: 0,
        rightDataTotal: 0,

        leftParamHP: {
          districtCode: '',
          objectCategoryId: this.objectCategoryId, //1
          orgId: this.objectOrgId,
          pageNum: 1,
          pageSize: 14,
          projectCode: '',
          projectIds: [],
          projectName: '',
          sort: [],
        },
        leftParamMS: {
          districtCode: '',
          objectCategoryId: this.objectCategoryId,
          orgId: this.objectOrgId,
          pageNum: 1,
          pageSize: 14,
          siteCode: '',
          siteIds: [],
          siteName: '',
          sort: [],
        },
        leftParamRL: {
          districtCode: '',
          objectCategoryId: this.objectCategoryId,
          orgId: this.objectOrgId,
          pageNum: 1,
          pageSize: 14,
          riverIds: [],
          riverSystemCode: '',
          riverSystemName: '',
          sort: [],
        },
        leftParamEX: {
          districtCode: '',
          excludeIds: undefined,
          objectCategoryId: this.objectCategoryId,
          orgId: this.objectOrgId,
          otherObjectCode: '',
          otherObjectName: '',
          pageNum: 1,
          pageSize: 14,
          sort: [],
        },
        paramObject: {
          orgId: this.objectOrgId,
          pageNum: 1,
          pageSize: 14,
          resourceCode: '',
          resourceName: '',
          resourceType: null,
          sort: [],
        },
        leftColumnsMS: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '编码',
            field: 'siteCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '名称',
            field: 'siteName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
        ],

        leftColumnsRL: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '编码',
            field: 'riverSystemCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '名称',
            field: 'riverSystemName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
        ],

        leftColumnsHP: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '编码',
            field: 'projectCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '名称',
            field: 'projectName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
        ],
        leftColumnsEX: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '编码',
            field: 'otherObjectCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '名称',
            field: 'otherObjectName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
        ],
        rightColumns: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '编码',
            field: 'resourceCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '名称',
            field: 'resourceName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
        ],
      }
    },
    created() {},
    computed: {
      hasSelected() {
        return this.selectedRowKeys.length > 0
      },
      hasObjSelected() {
        return this.selectedObjRowKeys.length > 0
      },
    },
    mounted() {},
    beforeDestroy() {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.leftParamHP.pageSize = pageSize
        this.paramObject.pageSize = pageSize
        // 根据树默认选中id来判断
        if (this.leftParamHP.objectCategoryId || this.leftParamHP.districtCode) {
          this.getDataList()
        }
      },
      //右侧
      handleChange({ currentPage, pageSize }) {
        this.paramObject.pageNum = currentPage
        this.paramObject.pageSize = pageSize
        this.getDataList()
      },
      //左侧 工程
      handlePageChangeHP({ currentPage, pageSize }) {
        this.leftParamHP.pageNum = currentPage
        this.leftParamHP.pageSize = pageSize
        this.getDataList()
      },
      //左侧 站点
      handlePageChangeMS({ currentPage, pageSize }) {
        this.leftParamMS.pageNum = currentPage
        this.leftParamMS.pageSize = pageSize
        this.getDataList()
      },
      //左侧 水系
      handlePageChangeRL({ currentPage, pageSize }) {
        this.leftParamRL.pageNum = currentPage
        this.leftParamRL.pageSize = pageSize
        this.getDataList()
      },
      //左侧 其他
      handlePageChangeEX({ currentPage, pageSize }) {
        this.leftParamEX.pageNum = currentPage
        this.leftParamEX.pageSize = pageSize
        this.getDataList()
      },

      // 已选项新增
      add() {
        //拼接新增参数param
        let param = {
          details: [],
          orgId: this.objectOrgId,
        }
        this.selectedRowKeys.forEach(keyItem => {
          param.details.push({
            resourceId: this.isShowTransferMS
              ? keyItem
              : this.isShowTransferHP
                ? keyItem
                : this.isShowTransferRL
                  ? keyItem
                  : this.isShowTransferEX
                    ? keyItem
                    : '',
            resourceType: this.isShowTransferMS
              ? 2
              : this.isShowTransferHP
                ? 1
                : this.isShowTransferRL
                  ? 3
                  : this.isShowTransferEX
                    ? 4
                    : 0,
          })
        })
        //调用新增接口
        dataAuthAdd(param).then(res => {
          this.selectedRowKeys = []
          this.getDataList()
        })
      },
      // 已选项删除
      del() {
        if (!this.selectedObjRowKeys) {
          return
        }

        dataAuthDel({ dataAuthIds: this.selectedObjRowKeys.join(',') }).then(res => {
          this.selectedObjRowKeys = []
          this.getDataList()
        })
      },
      /** 输入搜索 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getDataList()
      },
      //左侧待选择项 选中项集合
      leftSelectChange(valObj) {
        this.ids = valObj.records.map(item => item.projectId)
        this.names = valObj.records.map(item => item.projectName)
        this.isChecked = !!valObj.records.length
        this.selectedRowKeys = this.isShowTransferHP
          ? valObj.records.map(item => item.projectId)
          : this.isShowTransferMS
            ? valObj.records.map(item => item.siteId)
            : this.isShowTransferRL
              ? valObj.records.map(item => item.riverSystemId)
              : this.isShowTransferEX
                ? valObj.records.map(item => item.otherObjectId)
                : '' //工程待选择项
      },
      // 右侧已选项 选中项集合
      onSelectObjChange(valObj) {
        this.selectedObjRowKeys = valObj.records.map(item => item.dataAuthId)
      },
      /** 查询列表 */
      getList(type) {
        this.leftLoading = true
        this.rightLoading = true
        // this.selectChange({ records: [] })
        switch (type) {
          case 'MS': //监测站点
            this.leftParamMS.orgId = this.objectOrgId
            this.leftParamMS.objectCategoryId = this.objectCategoryId
            getPatrolObjectSiteList(this.leftParamMS).then(res => {
              this.leftLoading = false
              this.leftDataMS = res?.data?.data
              this.leftDataTotal = res?.data?.total
              this.paramObject.resourceType = 2
              this.paramObject.orgId = this.objectOrgId
              getPatrolObjectPage(this.paramObject).then(res => {
                this.rightLoading = false
                this.rightDataMS = res?.data?.data
                this.rightDataTotal = res?.data?.total
              })
              this.isShowTransferMS = true
              this.isShowTransferRL = false
              this.isShowTransferHP = false
              this.isShowTransferEX = false
            })

            break
          case 'RL': //水系
            this.leftParamRL.orgId = this.objectOrgId
            this.leftParamRL.objectCategoryId = this.objectCategoryId
            getPatrolObjectRiverList(this.leftParamRL).then(res => {
              this.leftLoading = false
              this.leftDataRL = res?.data?.data
              this.leftDataTotal = res?.data?.total
              this.paramObject.resourceType = 3
              this.paramObject.orgId = this.objectOrgId
              getPatrolObjectPage(this.paramObject).then(res => {
                this.rightLoading = false
                this.rightDataRL = res?.data?.data
                this.rightDataTotal = res?.data?.total
              })
              this.isShowTransferMS = false
              this.isShowTransferRL = true
              this.isShowTransferHP = false
              this.isShowTransferEX = false
            })
            break
          case 'HP': //工程
            this.leftParamHP.orgId = this.objectOrgId
            this.leftParamHP.objectCategoryId = this.objectCategoryId

            getPatrolObjectProjectList(this.leftParamHP).then(resHP => {
              this.leftLoading = false
              this.leftDataHP = resHP?.data?.data
              this.leftDataTotal = resHP?.data?.total
              this.paramObject.resourceType = 1
              this.paramObject.orgId = this.objectOrgId
              getPatrolObjectPage(this.paramObject).then(res => {
                this.rightLoading = false
                this.rightDataHP = res?.data?.data
                this.rightDataTotal = res?.data?.total
              })
              this.isShowTransferMS = false
              this.isShowTransferRL = false
              this.isShowTransferHP = true
              this.isShowTransferEX = false
            })
            break
          case 'EX': //其他
            this.leftParamEX.orgId = this.objectOrgId
            this.leftParamEX.objectCategoryId = this.objectCategoryId
            this.paramObject.resourceType = 4
            this.paramObject.orgId = this.objectOrgId
            getPatrolObjectPage(this.paramObject).then(res => {
              this.rightLoading = false
              this.rightDataEX = res?.data?.data
              // this.leftParamEX.excludeIds = res?.data?.data?.map(item => item.dataAuthId)
              this.rightDataTotal = res?.data?.total
              getPatrolObjectOtherList(this.leftParamEX).then(resEX => {
                this.leftLoading = false
                this.leftDataEX = resEX?.data?.data
                this.leftDataTotal = resEX?.data?.total

                this.isShowTransferMS = false
                this.isShowTransferRL = false
                this.isShowTransferHP = false
                this.isShowTransferEX = true
              })
            })

            break
          default:
            break
        }
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          siteCode: undefined,
          siteName: undefined,
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },
      //根据站点、工程、水系等类别显示选择项数据
      getDataList() {
        if (this.treeTabKey == '2') {
          this.getList('MS')
        }
        if (this.treeTabKey == '3') {
          this.getList('RL')
        }
        if (this.treeTabKey == '1') {
          this.getList('HP')
        }
        if (this.treeTabKey == '4') {
          this.getList('EX')
        }
      },

      //点击机构树数据
      clickTreeNodeOrg(node, type) {
        this.objectOrgId = node.$options.propsData.eventKey
        this.getDataList()
      },
      // 机构树加载完成后
      onTreeMountedOrg(data) {
        if (this.treeOrgTabKey == '0') {
          this.objectOrgId = data[0].deptId
        }
      },
      // 站点、工程、水系等树加载完成后
      onTreeMounted(data) {
        this.objectCategoryId = data[0].objectCategoryId
        this.getDataList()
      },
      //点击站点、工程、水系等树数据
      clickTreeNode(node, type) {
        this.objectCategoryId = node.$options.propsData.eventKey
        this.tableTitle = node.$options.propsData.dataRef.title

        this.leftParamHP.pageNum = 1
        this.paramObject.pageNum = 1
        this.getDataList()
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';
</style>
<style lang="less" scoped src="./custom.less"></style>
