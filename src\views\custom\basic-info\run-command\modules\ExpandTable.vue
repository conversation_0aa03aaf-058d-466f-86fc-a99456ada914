<template>
  <div style="height: 100%">
    <VxeTable
      ref="vxeTableRef"
      :isShowTableHeader="false"
      :tableKey="tableKey"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isShowSize="false"
      :isShowColumns="false"
      :isShowFull="false"
      :autoHeight="true"
      :rowConfig="{ isHover: false }"
      min-height="82px"
      max-height="280px"
      size="small"
      @refresh="getList"
    ></VxeTable>
    <OptCmdDetail v-if="showForm" ref="formRef" @ok="onOperationComplete" @close="showForm = false" />
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import OptCmdDetail from '../../opt-command/modules/OptCmdDetail.vue'
  import { getOperateCmdPage } from '../../opt-command/services'

  export default {
    name: 'ExpandTable',
    components: { VxeTable, OptCmdDetail },
    props: ['row'],
    data() {
      return {
        showForm: false,
        tableKey: 1,
        loading: true,
        list: [],
        selectIds: [],
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '操作票编号',
            field: 'operateCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                if (row.runCmdId) {
                  return <span>{row.operateCode}</span>
                } else {
                  return <span style='color:red'>{row.operateCode}</span>
                }
              },
            },
          },
          { title: '操作日期', field: 'operateDate', minWidth: 120, showOverflow: 'tooltip' },
          { title: '操作任务', field: 'operateTask', minWidth: 140, showOverflow: 'tooltip' },
          { title: '受令人', field: 'acceptorName', minWidth: 100, showOverflow: 'tooltip' },

          { title: '操作人', field: 'operateName', minWidth: 100, showOverflow: 'tooltip' },
          { title: '监护人', field: 'guardianName', minWidth: 100, showOverflow: 'tooltip' },
          { title: '操作开始时间', field: 'startDate', minWidth: 180, showOverflow: 'tooltip' },
          { title: '操作结束时间', field: 'endDate', minWidth: 180, showOverflow: 'tooltip' },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>详情</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {},
    methods: {
      getList() {
        this.loading = true
        getOperateCmdPage({ cmdCode: this.row.cmdCode, pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(
          response => {
            this.list = response.data.data
            this.loading = false
          },
        )
      },

      handleDetail(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      onOperationComplete() {
        this.isShowModal = false
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .vxe-table-box-content {
    .sortable-column-demo.vxe-grid {
      padding-bottom: 0px !important;
      margin-bottom: 10px;
    }
  }

  ::v-deep .vxe-table--render-default .vxe-table--body-wrapper {
    height: auto !important;
  }
</style>
