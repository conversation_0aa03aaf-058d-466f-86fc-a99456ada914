
import request from '@/utils/request'
import { getProjectChildren } from '@/api/common'

//新增
export function addWaterSituationReport(data) {
    return request({
        url: '/custom/irrigationWaterSituation/add',
        method: 'post',
        data
    })
}

//分页
export function getIrrigationWaterSituationPage(data) {
    return request({
        url: '/custom/irrigationWaterSituation/page',
        method: 'post',
        data
    })
}

//计算
export function calculateIrrigationWaterSituation(data) {
    return request({
        url: '/custom/irrigationWaterSituation/calculate',
        method: 'post',
        data
    })
}

//获取灌溉轮次
export function getIrrigationRound(endDate,startDate) {
    return request({
        url: '/custom/irrigationSetting/getIrrigationRound',
        method: 'post',
        params: { endDate,startDate }
    })
}

//删除
export function deleteIrrigationWaterSituation(id) {
    return request({
        url: '/custom/irrigationWaterSituation/delete',
        method: 'post',
        params: { id }
    })
}

//详情
export function getIrrigationWaterSituationById(id) {
    return request({
        url: '/custom/irrigationWaterSituation/get',
        method: 'post',
        params: { id }
    })
}

//修改
export function updateIrrigationWaterSituation(data) {
    return request({
        url: '/custom/irrigationWaterSituation/update',
        method: 'post',
        data
    })
}

//导出工程子节点接口
export { getProjectChildren }