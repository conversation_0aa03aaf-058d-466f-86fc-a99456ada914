# 选择渠道功能测试用例

## 测试场景

### 场景1：新增页面未选择填报单位
**前置条件：**
- 用户在新增页面，填报单位下拉框为空

**操作步骤：**
1. 点击"选择渠道"按钮
2. 观察选择渠道弹窗中的填报单位下拉框

**预期结果：**
- 填报单位下拉框自动选中当前用户所属部门
- 左侧和右侧的填报单位下拉框值一致
- 自动加载该部门对应的渠道数据

### 场景2：新增页面已选择填报单位
**前置条件：**
- 用户在新增页面选择了特定的填报单位（如：部门A）

**操作步骤：**
1. 点击"选择渠道"按钮
2. 观察选择渠道弹窗中的填报单位下拉框

**预期结果：**
- 填报单位下拉框自动填充为新增页面选择的部门A
- 左侧和右侧的填报单位下拉框都显示部门A
- 自动加载部门A对应的渠道数据

### 场景3：在弹窗中切换填报单位
**前置条件：**
- 选择渠道弹窗已打开

**操作步骤：**
1. 在左侧填报单位下拉框中选择不同的部门（如：部门B）
2. 观察渠道数据变化

**预期结果：**
- 自动重新查询部门B对应的渠道数据
- 左侧渠道列表更新为部门B的数据
- 缓存机制正常工作，不同部门数据独立

### 场景4：重置功能测试
**前置条件：**
- 选择渠道弹窗已打开
- 已修改过查询条件

**操作步骤：**
1. 修改渠道编码、渠道名称等查询条件
2. 点击"重置"按钮

**预期结果：**
- 渠道编码、渠道名称清空
- 填报单位恢复为默认值（当前用户部门或新增页面选择的部门）
- 重新加载对应部门的渠道数据

### 场景5：数据缓存验证
**前置条件：**
- 选择渠道弹窗已打开

**操作步骤：**
1. 选择部门A，观察加载的渠道数据
2. 切换到部门B，观察加载的渠道数据
3. 再次切换回部门A

**预期结果：**
- 第一次选择部门A时，从API加载数据
- 切换到部门B时，从API加载新数据
- 再次切换回部门A时，从缓存加载数据（无需重新调用API）
- 不同部门的数据互不影响

## 验证要点

1. **参数传递正确性**
   - 确认FormDrawer正确传递reportUnit给IrrigationPlantingTable
   - 确认IrrigationPlantingTable正确传递reportUnit给ChannelSelectModal

2. **API调用正确性**
   - 确认API调用时orgId参数为选中的填报单位ID
   - 确认不同填报单位调用不同的API请求

3. **缓存机制正确性**
   - 确认缓存键包含区划代码和填报单位ID
   - 确认不同填报单位的数据独立缓存

4. **UI交互正确性**
   - 确认下拉框选项正确显示
   - 确认默认值设置正确
   - 确认重置功能正常

## 调试信息

在浏览器控制台中可以看到以下调试信息：
- "传递给弹窗的填报单位: [reportUnit值]"
- "当前用户的部门id2: [deptId值]"
- API调用的参数信息

这些信息有助于验证功能是否正常工作。
