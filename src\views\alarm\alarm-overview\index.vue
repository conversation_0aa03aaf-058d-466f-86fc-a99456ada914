<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <div class="top">
      <div class="query-box" v-if="viewType === '1'">
        <span class="name">关键字：</span>
        <a-select
          class="query-input"
          allowClear
          show-search
          :filter-option="filterOption"
          v-model="queryParam.cameraName"
          :options="cameraList"
          placeholder="请输入"
          @change="onChangeCamera"
        ></a-select>
        <a-button @click="handleQuery">
          <a-icon type="search" />
          查询
        </a-button>
        <a-button @click="resetQuery">
          <a-icon type="reload" />
          重置
        </a-button>
      </div>
      <a-radio-group class="query-radio-group" v-model="viewType" button-style="solid" @change="onTabChange">
        <a-radio-button value="1">地图模式</a-radio-button>
        <a-radio-button value="2">表格模式</a-radio-button>
      </a-radio-group>
    </div>
    <MapCamera
      :list="cameraList"
      :queryCameraId="queryCameraId"
      :eventTypeOptions="eventTypeOptions"
      :warnLevelOptions="warnLevelOptions"
      v-if="viewType === '1'"
    />
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery" v-if="viewType == '2'">
      <!-- <a-form-item label="类型">
        <a-select
          allowClear
          v-model="queryParam.cameraType"
          :options="cameraTypeOptions"
          placeholder="请选择"
        ></a-select>
      </a-form-item>

      <a-form-item label="所属渠系">
        <a-select
          allowClear
          v-model="queryParam.cameraType"
          :options="cameraTypeOptions"
          placeholder="请选择"
        ></a-select>
      </a-form-item> -->
      <!-- <a-form-model-item label="所属工程">
        <a-tree-select
          v-model="queryParam.treeNodeId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-model-item> -->

      <!-- <a-form-item label="工程名称">
        <a-input v-model="queryParam.msgName" placeholder="请输入" />
      </a-form-item> -->
      <a-form-item label="事件类型">
        <a-select show-search placeholder="请输入" v-model="queryParam.eventType" option-filter-prop="children">
          <a-select-option v-for="item in eventTypeOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="告警级别">
        <a-select show-search placeholder="请输入" v-model="queryParam.warnLevel" option-filter-prop="children">
          <a-select-option v-for="item in warnLevelOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="处理状态">
        <a-select show-search placeholder="请输入" v-model="queryParam.status" option-filter-prop="children">
          <a-select-option v-for="item in eventStatusOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="告警总览"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
          </div>
        </VxeTable>

        <!-- :emergencyTypeOptions="emergencyTypeOptions"
          :projectOptions="projectOptions" -->
        <FormDrawer
          :eventTypeOptions="eventTypeOptions"
          :warnLevelOptions="warnLevelOptions"
          :cameraOptions="cameraOptions"
          v-if="showForm"
          ref="formRef"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormPublish
          v-if="showFormPublish"
          ref="formPublishRef"
          @ok="onOperationComplete"
          @close="showFormPublish = false"
        />
        <FormDetail
          v-if="showFormDetail"
          ref="formDetailRef"
          :eventTypes="eventTypes"
          :warnLevels="warnLevels"
          @ok="onOperationComplete"
          @close="showFormDetail = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getProjectTree, getOptions } from '@/api/common'
  import { getCameraPage, deleteCamera } from '@/views/basic/camera/services'
  import { getEventPage, deleteEvent, issueEvent, eliminateEvent, getAllCameraList } from './services'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import MapCamera from './components/MapCamera.vue'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import FormDrawer from './components/FormDrawer.vue'
  import FormPublish from './components/FormPublish.vue'
  import FormDetail from './components/FormDetail.vue'

  export default {
    name: 'EquipmentControl',
    components: {
      VxeTableForm,
      VxeTable,
      MapCamera,
      FormDrawer,
      FormPublish,
      FormDetail,
    },
    data() {
      return {
        cameraList: [],
        cameraOptions: [],
        cameras: null,
        showFormDetail: false,
        showForm: false,
        showFormPublish: false,
        eventTypeOptions: [],
        eventTypes: null,
        eventStatusOptions: [],
        eventStatus: null,
        warnLevelOptions: [],
        warnLevels: null,

        loading: false,
        viewType: '1',
        projectOptions: [],
        list: [],
        total: 0,
        queryCameraId: undefined,

        queryParam: {
          eventName: '',
          eventType: '',
          isIssue: undefined,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          sort: [],
          statues: [],
          status: undefined,
          warnLevel: '',

          // cameraCode: '',
          // cameraName: '',
          // cameraType: undefined,
          // districtCode: '',
          // otherTreeNodeId: undefined,
          // pageNum: 1,
          // pageSize: Number.MAX_SAFE_INTEGER,
          // riverTreeNodeId: undefined,
          // siteTreeNodeId: undefined,
          // sort: [],
          // treeNodeId: undefined,
          // treeNodeType: 'category',
        },

        cameraTypeOptions: [
          { label: '枪机', value: 1 },
          { label: '球机', value: 2 },
        ],
        columns: [
          // { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '事件名称',
            field: 'eventName',
            minWidth: 150,
            showOverflow: 'tooltip',
          },

          {
            title: '事件类型',
            field: 'eventType',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.eventTypes[row.eventType]?.value || ''
              },
            },
          },
          {
            title: '异常点名称',
            field: 'cameraName',
            minWidth: 140,
            showOverflow: 'tooltip',
          },
          {
            title: '位置',
            field: 'location',
            minWidth: 100,
            showOverflow: 'tooltip',
          },

          {
            title: '告警级别',
            field: 'warnLevel',
            minWidth: 100,
            sortable: true,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span style={{ color: this.warnLevels[row.warnLevel]?.option1 }}>
                    {this.warnLevels[row.warnLevel]?.value || ''}
                  </span>
                )
              },
            },
          },
          {
            title: '上报人',
            field: 'createdUserName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '上报时间',
            field: 'createdTime',
            minWidth: 120,
            sortable: true,
          },

          {
            title: '处理状态',
            field: 'status',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.eventStatus[row.status]?.value || ''
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 196,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>详情</a>
                    {row.status == 0 && <a-divider type='vertical' />}
                    {row.status == 0 && (
                      <a style={{ color: '#F53F3F' }} onClick={() => this.handleIssue(row)}>
                        事件发布{' '}
                      </a>
                    )}
                    {row.status == 0 && <a-divider type='vertical' />}
                    {row.status == 0 && (
                      <a style={{ color: '#FF9A2E' }} onClick={() => this.handleEliminate(row)}>
                        告警取消
                      </a>
                    )}

                    {/* <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a> */}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    mounted() {
      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        // this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })

      getAllCameraList().then(res => {
        console.log('getAllCameraList 666', res)
        this.cameraOptions = res.data
        this.cameras = getFlatTreeMap(this.cameraOptions, 'cameraId')
      })

      getOptions('eventType').then(res => {
        this.eventTypeOptions = res.data
        this.eventTypes = getFlatTreeMap(this.eventTypeOptions, 'key')
      })

      getOptions('event-status').then(res => {
        this.eventStatusOptions = res.data
        this.eventStatus = getFlatTreeMap(this.eventStatusOptions, 'key')
      })
      getOptions('warnLevel').then(res => {
        this.warnLevelOptions = res.data
        this.warnLevels = getFlatTreeMap(this.warnLevelOptions, 'key')
      })
      this.getList()
    },
    methods: {
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.loading = true
        if (this.viewType === '1') {
          // getCameraPage(this.queryParam).then(response => {
          //   console.log('getCameraPage 666 333', response)
          //   this.cameraList = response?.data?.data
          //   this.total = response?.data?.total
          //   this.loading = false
          // })
          this.queryParam.statues = [0, 1]
          getEventPage(this.queryParam).then(response => {
            console.log('获取告警的视频点', response)
            this.cameraList = response?.data?.data?.map(item => ({
              ...item, // 保留原对象的所有属性
              value: item.cameraId, // 添加 value 属性
              label: item.cameraName, // 添加 label 属性
            }))
            this.total = response?.data?.total
            this.loading = false
          })
        } else if (this.viewType === '2') {
          getEventPage(this.queryParam).then(response => {
            this.list = response?.data?.data
            this.total = response?.data?.total
            this.loading = false
          })
        }
      },
      onChangeCamera(val) {
        this.queryCameraId = val
      },
      /** 导出按钮操作 */
      handleExport() {},
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      // 操作完成后
      onOperationComplete() {
        // this.$emit('ok')
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        if (this.viewType === '1') {
          this.queryCameraId = undefined
        } else if (this.viewType === '2') {
          this.queryParam = {
            ...this.queryParam,
            eventType: undefined,
            status: undefined,
            pageNum: 1,
          }
        }

        this.handleQuery()
      },
      onTabChange() {},
      //点击视频
      handlePlay(row) {},
      //详情
      handleDetails(row) {
        this.showFormDetail = true
        this.$nextTick(() => this.$refs.formDetailRef.handle(row))
      },
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(1))
      },
      //事件发布
      handleIssue(row) {
        this.showFormPublish = true
        this.$nextTick(() => this.$refs.formPublishRef.handle(row))
      },

      /** 告警取消 */
      handleEliminate(row) {
        var that = this
        const ids = row.eventId ? [row.eventId] : this.ids
        const names = row.serialNumber || this.names

        this.$confirm({
          title: '确认取消所选中数据?',
          content: '请确认是否取消当前选中的数据',
          onOk() {
            return eliminateEvent({ evenId: row.eventId }).then(res => {
              that.$message.success(`成功取消数据`, 3)
              // that.selectChange({ records: [] })
              that.getList()
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .common-table-page {
    background: #fff;
    .top {
      padding: 10px 16px;
      display: flex;
      .query-box {
        display: flex;
        .name {
          margin-top: 6px;
        }
        .query-input {
          width: 200px;
        }
        :deep(.ant-input) {
          width: 200px;
          height: 32px;
          border: none;
          border-radius: 0;
          background: #f7f8fa;
        }
        :deep(.ant-btn) {
          width: 84px;
          height: 32px;
          background: #f2f3f5;
          border-radius: 2px;
          border: 0;
          margin-right: 10px;
          &:hover {
            color: #1664ff;
            background: rgba(14, 66, 210, 0.1);
          }
        }
      }
      .query-radio-group {
        margin-left: auto;
        border: none;
        background: #f2f3f5;
        border-radius: 2px 2px 2px 2px;
      }
      .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
        color: #165dff;
        background: #fff;
        height: 26px;
        line-height: 23px;
        margin: 2px;
      }
      .ant-radio-button-wrapper {
        background: #f2f3f5;
        border-radius: 0;
      }
      .ant-radio-button-wrapper,
      .ant-radio-button-wrapper:first-child {
        border: none;
      }
      .ant-radio-button-wrapper:not(:first-child):before {
        background: transparent;
      }
    }
    .vxe-table-form {
      height: calc(100% - 40px);
    }
  }
</style>
