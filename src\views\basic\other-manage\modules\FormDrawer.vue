<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="550"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="对象编码" prop="otherObjectCode">
                <a-input allowClear v-model="form.otherObjectCode" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="对象名称" prop="otherObjectName">
                <a-input allowClear v-model="form.otherObjectName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="对象简称" prop="otherObjectNameAbbr	">
                <a-input allowClear v-model="form.otherObjectNameAbbr" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="对象类别" prop="objectCategoryId">
                <a-tree-select
                  v-model="form.objectCategoryId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="objectCategoryOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'objectCategoryName',
                    key: 'objectCategoryId',
                    value: 'objectCategoryId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="行政区划" prop="districtCode">
                <a-tree-select
                  v-model="form.districtCode"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="districtOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'districtName',
                    key: 'districtCode',
                    value: 'districtCode',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="经度" prop="longitude">
                <a-input-number
                  :min="73.66"
                  :max="135.05"
                  style="width: 100%"
                  v-model="form.longitude"
                  placeholder="请输入"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="纬度" prop="latitude">
                <a-input-number
                  :min="3.86"
                  :max="53.55"
                  style="width: 100%"
                  v-model="form.latitude"
                  placeholder="请输入"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所在位置" prop="location">
                <a-input allowClear v-model="form.location" placeholder="请输入">
                  <a-icon
                    style="font-size: 20px; cursor: pointer"
                    @click="onMapOpen"
                    slot="addonAfter"
                    type="environment"
                    theme="twoTone"
                  />
                </a-input>
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="封面图">
                <UploadFile
                  :fileUrl.sync="form.coverImgUrl"
                  :multiple="false"
                  listType="picture-card"
                  folderName="projectCover"
                  accept=".png,.jpg,.jpeg"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="备注" prop="remark">
                <a-textarea v-model="form.remark" placeholder="请输入" allow-clear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>

        <MapModal ref="mapModalRef" @close="showMapModal = false" @confirm="onMapModalConfirm" />
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addOtherObject, updateOtherObject, getOtherObject } from '../services'
  import UploadFile from '@/components/UploadFile/index.vue'
  import AntModal from '@/components/pt/dialog/AntModal'
  import MapModal from '@/components/MapBox/MapboxModal.vue'

  export default {
    name: 'FormDrawer',
    props: ['districtOptions', 'objectCategoryOptions'],
    components: { MapModal, AntModal, UploadFile },
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        showMapModal: false,
        // 表单参数
        form: {
          otherObjectName: undefined,
          otherObjectCode: undefined,
          otherObjectNameAbbr: undefined,
          objectCategoryId: undefined,
          districtCode: undefined,
          otherObjectId: undefined,
          longitude: undefined,
          latitude: undefined,
          location: undefined,
          remark: undefined,
        },
        open: false,

        rules: {
          otherObjectCode: [{ required: true, message: '对象编码不能为空', trigger: 'blur' }],
          otherObjectName: [{ required: true, message: '对象名称不能为空', trigger: 'blur' }],
          objectCategoryId: [{ required: true, message: '对象类别不能为空', trigger: 'change' }],
          districtCode: [{ required: true, message: '行政区划不能为空', trigger: 'change' }],
        },
      }
    },
    filters: {},
    created() {
      // 字典获取下拉
    },
    computed: {},
    watch: {},
    methods: {
      // 打开地图
      onMapOpen() {
        this.showMapModal = true
        const mapInfo = {
          longitude: this.form.longitude,
          latitude: this.form.latitude,
          location: this.form.location,
        }
        this.$nextTick(() => this.$refs.mapModalRef.handleOpen(mapInfo))
      },
      onMapModalConfirm(mapInfo) {
        this.form.longitude = mapInfo.longitude
        this.form.latitude = mapInfo.latitude
        this.form.location = mapInfo.location
        this.showMapModal = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd(r, treeTabType) {
        this.open = true
        this.formTitle = '新增'
        if (treeTabType === '1') {
          this.form.objectCategoryId = r.objectCategoryId || undefined
        }
        if (treeTabType === '2') {
          this.form.districtCode = r.districtCode || undefined
        }
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'

        this.modalLoading = true
        getOtherObject({ OtherObjectId: record.otherObjectId }).then(res => {
          this.form = res.data
          this.modalLoading = false
        })
        // this.form.otherObjectId = record.otherObjectId
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            const saveForm = JSON.parse(JSON.stringify(this.form))

            if (this.form.otherObjectId !== undefined) {
              updateOtherObject(saveForm)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            } else {
              addOtherObject(saveForm)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
