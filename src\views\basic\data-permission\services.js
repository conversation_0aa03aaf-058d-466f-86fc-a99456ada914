import request from '@/utils/request'

// 水利对象分类-根据编码获取树
export function getTreeByCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'MS' },
  })
}

// 监测站点-列表分页查询
export function getSitePage(data) {
  return request({
    url: '/base/site/page',
    method: 'post',
    data,
  })
}

// 水利对象分类-根据编码获取水系树
export function getTreeByRiverCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'RL' },
  })
}
// 水利对象分类-根据编码获取其他树
export function getTreeByOtherCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'EX' },
  })
}
// 水利对象分类-根据编码获取工程树
export function getTreeByProjectCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'HP' },
  })
}

// 巡检对象工程信息-列表查询 /base/project/page
export function getPatrolObjectProjectList(data) {
  return request({
    url: '/base/dataAuth/project/page',
    method: 'post',
    data,
  })
}
// 巡检对象其他信息-列表查询 /base/project/page
export function getPatrolObjectOtherList(data) {
  return request({
    url: '/base/dataAuth/otherObject/list',
    method: 'post',
    data,
  })
}
// 巡检对象水系-列表查询 /patrol/object/riverSystem/list
export function getPatrolObjectRiverList(data) {
  return request({
    url: '/base/dataAuth/riverSystem/list',
    method: 'post',
    data,
  })
}
// 巡检对象监测站点-列表查询 /patrol/object/site/list
export function getPatrolObjectSiteList(data) {
  return request({
    url: '/base/dataAuth/site/list',
    method: 'post',
    data,
  })
}

// 数据权限-分页列表 已选中
export function getPatrolObjectPage(data) {
  return request({
    url: '/base/dataAuth/page',
    method: 'post',
    data,
  })
}

// 机构树查询
export function getConfigTree(data) {
  return request({
    url: '/sys/dept/tree',
    method: 'post',
    data: { ...data, type: '1' },
  })
}

// 数据权限-新增
export function dataAuthAdd(data) {
  return request({
    url: '/base/dataAuth/add',
    method: 'post',
    data,
  })
}
// 数据权限-删除
export function dataAuthDel(params) {
  return request({
    url: '/base/dataAuth/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
