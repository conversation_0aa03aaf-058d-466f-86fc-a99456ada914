<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="420"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row>
          <a-form-model-item label="发布项名称" prop="reportItemName">
            <a-input allowClear v-model="form.reportItemName" :maxLength="15" placeholder="请输入" />
          </a-form-model-item>
          <a-form-model-item label="类型" prop="reportItemType">
            <a-select allowClear v-model="form.reportItemType" placeholder="请选择">
              <a-select-option v-for="(item, index) in briefingTypeList" :key="index" :value="item.key">
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="内容" prop="content">
            <a-textarea rows="4" v-model="form.content" placeholder="请输入" />
          </a-form-model-item>
          <div class="check-group">
            <a-checkbox v-model="form.isIncludeTable" v-if="form.reportItemType">
              {{ checkboxNameByType(form.reportItemType) }}表
            </a-checkbox>
            <a-checkbox v-model="form.isIncludeChart" v-if="form.reportItemType == '1'">
              {{ checkboxNameByType(form.reportItemType) }}统计图
            </a-checkbox>
          </div>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { addMaintenance, editMaintenance } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormMaintenance',
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        labelCol: { span: 4 },
        wrapperCol: { span: 18 },
        formTitle: '',
        reportItemId: null,
        form: {
          reportItemName: null,
          reportItemType: null,
          content: null,
          isIncludeChart: true,
          isIncludeTable: true,
        },
        open: false,
        briefingTypeList: [],
        rules: {
          reportItemName: [{ required: true, message: '发布项名称不能为空', trigger: 'blur' }],
          reportItemType: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
          content: [{ required: true, message: '内容不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {
      getOptions('publicationItem').then(res => {
        this.briefingTypeList = res?.data || []
      })
    },
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 新增按钮操作 */
      handleMaintenance(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.formTitle = '修改'
          this.form = row
          this.form.reportItemType = row.reportItemType.toString()
          this.form.isIncludeChart = row.isIncludeChart == 1 ? true : false
          this.form.isIncludeTable = row.isIncludeTable == 1 ? true : false
        }
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.isIncludeChart = this.form.isIncludeChart ? 1 : 0
            this.form.isIncludeTable = this.form.isIncludeTable ? 1 : 0
            if (this.form.reportItemType != '1') {
              this.form.isIncludeChart = 0
            }
            if (this.form.reportItemId == null) {
              addMaintenance(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editMaintenance(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
      checkboxNameByType(type) {
        if (type == null || type == 0) return ''
        if (type == '1' || type == 1) {
          return '重点站降雨量'
        } else {
          let row = this.briefingTypeList.find(x => x.key == type)
          return row?.value
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  .check-group {
    margin-left: 17%;
  }
</style>
