<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="500"
    @cancel="cancel"
    modalHeight="400"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="文件内容" prop="archivesName">
              <a-input v-model="form.archivesName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="存放位置" prop="archivesAddress">
              <a-input v-model="form.archivesAddress" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="存放地点" prop="archivesPlace">
              <a-input v-model="form.archivesPlace" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <!--  -->

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="管理人员" prop="userId">
              <a-select show-search placeholder="请输入" v-model="form.userId" option-filter-prop="children">
                <a-select-option v-for="(d, index) in userOptions" :key="index" :value="d.userId">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addArchives, editArchives, getArchivesById, getProjectListByPropertyId } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'
  import { validEmail } from '@/utils/validate'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['userOptions'],
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭

        formTitle: '',
        loading: false,
        modalLoading: false,
        form: {
          paperArchivesId: null,
          archivesAddress: '',
          archivesPlace: '',
          archivesName: '',
          userId: null,
        },
        open: false,
        propertyInProject: [],
        rules: {
          archivesName: [{ required: true, message: '文件内容不能为空', trigger: 'blur' }],
          archivesAddress: [{ required: true, message: '存放位置不能为空', trigger: 'blur' }],
          archivesPlace: [{ required: true, message: '存放地点不能为空', trigger: 'blur' }],
          userId: [{ required: true, message: '管理人员不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getArchivesById({
            paperArchivesId: row.paperArchivesId,
          }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                // phoneAttaches: res.data.phoneAttaches?.map(el => el.attachUrl),
                // positionAttaches: res.data.positionAttaches?.map(el => el.attachUrl),
              }
              // this.form.propertyId = this.propertyList.find(el => el.propertyId == this.form.propertyId)?.propertyId
              // propertyId: this.form.propertyId
              // this.form.year = moment(`${res.data.year}-01-01`)
              // getProjectListByPropertyId({ propertyId: 1 }).then(res => {
              //   this.propertyInProject = res?.data
              // })
            }
            this.modalLoading = false
          })
        }
      },
      //

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.startTime = moment(this.form.startTime).format('YYYY-MM-DD')
            this.form.endTime = moment(this.form.endTime).format('YYYY-MM-DD')
            this.form.isProperty = this.$route.meta.query?.isProperty ? 'true' : ''
            if (this.form.paperArchivesId == null) {
              addArchives(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editArchives(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
