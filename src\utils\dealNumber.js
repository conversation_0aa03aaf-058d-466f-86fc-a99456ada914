// n: 保留几位有效数字
export function dealNumber(number, n) {
  if (number === null) return null
  if (number === 0 || number === '0') return 0
  if (!number) return ''

  let num = +number
  if (num >= n * 10) {
    return +num.toFixed(0)
  }

  return +num.toPrecision(n)
}

export function getFixedNum(num, count) {
  if (num === null) return null
  if (num === 0 || num === '0') return 0

  if (!num) return ''

  return +num.toFixed(count)
}

/**
 * 截取当前数据到小数点后n位
 * @param {*} num 源数据
 * @param {*} decimals 保留的小数位数
 * */
export const twoDecimalFilter = (num, n) => {
  if (num === null) {
    return '--'
  } else if (num === 0) {
    return 0
  } else {
    let handleNum = Number(num)
    let isToFixed = handleNum.toString().includes('.') && handleNum.toString().split('.')[1].length > 2
    if (isToFixed) {
      return Number(handleNum.toFixed(2))
    } else {
      return Number(handleNum)
    }
  }
}
/**
 * 保留有效数字为3位
 * @param {*} num 源数据
 * @param {*} decimals 保留的小数位数
 * */
export const effectiveFilter = (num, n) => {
  let handleNum = Number(num)
  if (handleNum == null) {
    return '--'
  }
  if (handleNum / 10000 >= 1) {
    const decNum = handleNum / 10000
    return Number(decNum.toPrecision(n))
  } else {
    return Number(handleNum.toFixed(n))
  }
}

/**
 * 截取当前数据到小数点后n位
 * @param {*} num 源数据
 * @param {*} decimals 保留的小数位数
 * */
export const decimalFilter = (num, n) => {
  if (num === null) {
    return '--'
  } else if (num === 0) {
    return n > 0 ? (0).toFixed(n) : 0
  } else {
    let handleNum = Number(num)
    let isToFixed = handleNum.toString().includes('.') && handleNum.toString().split('.')[1].length > 2
    if (isToFixed || n > 0) {
      // 当需要显示小数位时，返回字符串格式以保持小数位显示
      return handleNum.toFixed(n)
    } else if (n == 0) {
      return Number(handleNum.toFixed(0))
    } else {
      return Number(handleNum)
    }
  }
}
