<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="文件名称">
        <a-input v-model="queryParam.archivesName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :userOptions="userOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <!-- <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :inspectionTypeOptions="inspectionTypeOptions"
          :projectOptions="projectOptions"
          :propertyList="propertyList"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        /> -->
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree, getSysUserPage } from '@/api/common'
  import { getArchivesPage, deleteArchives } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'DailyMaintenance',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
    },
    data() {
      return {
        userOptions: [],
        isChecked: false,

        showForm: false,
        showFormDetails: false,

        list: [],
        tableTitle: '纸质档案',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          archivesName: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 40 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
          },
          {
            title: '文件名称',
            field: 'archivesName',
          },
          {
            title: '存放位置',
            field: 'archivesAddress',
          },
          {
            title: '存放地点',
            field: 'archivesPlace',
          },
          {
            title: '管理人员',
            field: 'name',
          },
          {
            title: '联系电话',
            field: 'mobile',
          },
          {
            title: '创建人',
            field: 'createdUserName',
          },

          {
            title: '创建时间',
            field: 'createdTime',
          },

          {
            title: '操作',
            field: 'operate',
            width: 190,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
      getSysUserPage({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
        isDisabled: 0,
        deptId: null,
      }).then(res => {
        this.userOptions = res.data.data
      })
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getArchivesPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.paperArchivesId)
        // this.names = valObj.records.map(item => item.planName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          archivesName: '',
          endTime: '',
          pageNum: 1,
          sort: [],
        }
        this.planTimes = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.paperArchivesId ? [row?.paperArchivesId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteArchives({ paperArchivesIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
