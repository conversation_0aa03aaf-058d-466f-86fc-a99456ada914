<template>
  <!-- 详情查看 -->
  <ant-modal :visible="open" :modal-title="formTitle" :loading="modalLoading" @cancel="cancel"
    @ok="cancel" modalWidth="1400" modalHeight="728">
    <div slot="content">
      <div class="planting-intention-report">
        <!-- 基本信息部分 -->
        <div class="basic-info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="form-row">
            <div class="form-item">
              <label>计划年份：</label>
              <span class="form-value">{{ formData.planYear || '-' }}</span>
            </div>
            <div class="form-item">
              <label>填报单位：</label>
              <span class="form-value">{{ getReportUnitLabel(formData.reportUnit) }}</span>
            </div>
          </div>
        </div>

        <!-- 引黄灌溉（按渠道）部分 -->
        <IrrigationPlantingDetailTable title="引黄灌溉（按渠道）" :table-data="yellowRiverIrrigationData"
          table-ref="yellowRiverIrrigationTableRef" />

        <!-- 引黄滴灌（按渠道）部分 -->
        <IrrigationPlantingDetailTable title="引黄滴灌（按渠道）" :table-data="yellowRiverDripIrrigationData"
          table-ref="yellowRiverDripIrrigationTableRef" />

        <!-- 纯井灌（按渠道）部分 -->
        <IrrigationPlantingDetailTable title="纯井灌（按渠道）" :table-data="wellIrrigationData"
          table-ref="wellIrrigationTableRef" />

      </div>
    </div>
  </ant-modal>
</template>

<script lang="jsx">
import AntModal from '@/components/pt/dialog/AntModal'
import IrrigationPlantingDetailTable from './IrrigationPlantingDetailTable'
import { getIrrigationPlantIntentById } from '../service'
import { getLeafCropsWithFieldNames, getValueToFieldNameMap, findCropByPlantTypeCode, findCropByTypeCodes, testCropMatching, testPreciseCropMatching } from '@/enum/planting-crop'
import { getOptions } from '@/api/common'
import request from '@/utils/request'
import { getOrgTree } from '@/api/user'

export default {
  name: 'DetailsDrawer',
  props: [],
  components: { AntModal, IrrigationPlantingDetailTable },
  data() {
    return {
      open: false,
      modalLoading: false,
      formTitle: '',
      formData: {
        planYear: null,
        reportUnit: null
      },
      // 作物字典选项
      cropOptions: [],
      // 部门相关数据
      orgTree: [],
      reportUnitOptions: [],
      // 项目（渠道）数据
      projectOptions: [],
      projectOptionsLoading: false,
      projectOptionsLoaded: false,
      yellowRiverIrrigationData: [],
      yellowRiverDripIrrigationData: [],
      wellIrrigationData: []
    }
  },
  computed: {
  },
  created() {
    this.loadCropOptions()
    this.getOrgTreeData()
    // 不在这里预加载项目数据，改为在需要时加载
    // this.loadProjectOptions()
  },
  mounted() {
    // 如果用户信息已经加载但部门选项还没有构建，重新构建
    if (this.orgTree.length > 0 && this.reportUnitOptions.length === 0) {
      this.buildReportUnitOptions()
    }
  },
  methods: {
    cancel() {
      this.open = false
      this.resetData()
    },

    async showDetails(record) {
      this.open = true
      this.formTitle = '详情查看'
      this.modalLoading = true

      try {
        await this.loadDetailData(record.id)
      } catch (error) {
        console.error('加载详情数据失败:', error)
        this.$message.error('加载详情数据失败')
      } finally {
        this.modalLoading = false
      }
    },

    // 重置数据
    resetData() {
      this.formData = {
        planYear: null,
        reportUnit: null
      }
      this.yellowRiverIrrigationData = []
      this.yellowRiverDripIrrigationData = []
      this.wellIrrigationData = []
    },

    // 加载详情数据
    async loadDetailData(id) {
      try {
        // 确保作物选项已加载
        if (this.cropOptions.length === 0) {
          await this.loadCropOptions()
        }

        // 确保项目数据已加载
        if (!this.projectOptionsLoaded && !this.projectOptionsLoading) {
          await this.loadProjectOptions()
        } else if (this.projectOptionsLoading) {
          // 等待加载完成
          while (this.projectOptionsLoading) {
            await new Promise(resolve => setTimeout(resolve, 100))
          }
        }

        const response = await getIrrigationPlantIntentById(id)

        if (response.code === 200 && response.data) {
          const data = response.data

          // 设置基本信息
          this.formData.planYear = data.planYear
          this.formData.reportUnit = data.depId

          // 解析灌溉数据
          this.parseIrrigationData(data.cstIrrigationDtoList || [])

          // 测试作物匹配功能
          this.testCropMatchingFunction(data.cstIrrigationDtoList || [])
        } else {
          this.$message.error(response.message || '获取详情数据失败')
        }
      } catch (error) {
        console.error('获取详情数据失败:', error)
        throw error
      }
    },

    // 解析灌溉数据
    parseIrrigationData(cstIrrigationDtoList) {
      // 重置数据
      this.yellowRiverIrrigationData = []
      this.yellowRiverDripIrrigationData = []
      this.wellIrrigationData = []

      cstIrrigationDtoList.forEach(irrigationDto => {
        const { irrigationType, channelDtoList } = irrigationDto

        // 根据灌溉类型分类处理
        const tableData = this.parseChannelData(channelDtoList || [])

        switch (irrigationType) {
          case 1: // 引黄灌溉
            this.yellowRiverIrrigationData = tableData
            break
          case 2: // 引黄滴灌
            this.yellowRiverDripIrrigationData = tableData
            break
          case 3: // 纯井灌
            this.wellIrrigationData = tableData
            break
          default:
            console.warn('未知的灌溉类型:', irrigationType)
        }
      })
    },

    // 解析渠道数据
    parseChannelData(channelDtoList) {
      const tableData = []

      channelDtoList.forEach((channelDto, index) => {
        const { channelNo, cstIrrigationPlantIntentDetailDtoList } = channelDto

        // 获取渠道名称
        const channelName = this.getChannelName(channelNo)

        // 根据渠道编码查找项目ID
        const project = this.projectOptions.find(option => option.projectCode === channelNo)
        const projectId = project ? project.projectId : `${channelNo}_${index}`

        // 创建行数据
        const rowData = {
          id: projectId,
          projectId: projectId,
          channelCode: channelNo,
          channel: channelName,
          farmlandArea: null,
          totalIrrigationArea: null
        }

        // 初始化所有作物字段
        this.initializeCropFields(rowData)

        // 填充种植数据
        this.fillPlantingData(rowData, cstIrrigationPlantIntentDetailDtoList || [])

        // 计算小计和总计
        this.calculateSubtotals(rowData)

        tableData.push(rowData)
      })

      return tableData
    },

    // 初始化作物字段
    initializeCropFields(rowData) {
      const leafCrops = getLeafCropsWithFieldNames()
      leafCrops.forEach(crop => {
        if (crop.fieldName) {
          rowData[crop.fieldName] = null
        }
      })

      // 初始化小计字段
      const subtotalFields = [
        'melonVegetableSubtotal', 'wheatIntercroppingSubtotal', 'summerCropsSubtotal',
        'autumnCropsSubtotal', 'forestlandSubtotal', 'forestGrasslandSubtotal'
      ]
      subtotalFields.forEach(field => {
        rowData[field] = null
      })
    },

    // 填充种植数据
    fillPlantingData(rowData, plantIntentDetailList) {
      const valueToFieldNameMap = getValueToFieldNameMap()

      plantIntentDetailList.forEach(detail => {
        const { plantTypeCode, plantAmount, firstTypeCode, secondTypeCode } = detail

        // 根据plantTypeCode查找对应的作物
        const cropInfo = this.findCropByTypeCode(plantTypeCode, firstTypeCode, secondTypeCode)

        if (cropInfo && cropInfo.fieldName) {
          rowData[cropInfo.fieldName] = plantAmount
          // console.log(`设置字段 ${cropInfo.fieldName} = ${plantAmount}`)
        } else {
          console.warn('未找到对应的作物字段:', { plantTypeCode, firstTypeCode, secondTypeCode })
        }
      })

    },

    // 根据类型编码查找作物
    findCropByTypeCode(plantTypeCode, firstTypeCode, secondTypeCode) {

      // 使用新的精确匹配方法
      const cropInfo = findCropByTypeCodes(plantTypeCode, firstTypeCode, secondTypeCode, this.cropOptions)

      if (cropInfo) {
        return cropInfo
      }

      console.warn('未找到匹配的作物:', { plantTypeCode, firstTypeCode, secondTypeCode })
      return null
    },

    // 计算小计和总计
    calculateSubtotals(row) {
      // 计算瓜菜小计
      const melon = parseFloat(row.melon) || 0
      const sunflowerInMelon = parseFloat(row.sunflowerInMelon) || 0
      const tomato = parseFloat(row.tomato) || 0
      row.melonVegetableSubtotal = (melon + sunflowerInMelon + tomato) || null

      // 计算小麦间套种小计
      const corn = parseFloat(row.corn) || 0
      const pepper = parseFloat(row.pepper) || 0
      const sunflowerInWheat = parseFloat(row.sunflowerInWheat) || 0
      const otherInWheat = parseFloat(row.otherInWheat) || 0
      row.wheatIntercroppingSubtotal = (corn + pepper + sunflowerInWheat + otherInWheat) || null

      // 计算夏季作物小计
      const wheat = parseFloat(row.wheat) || 0
      const oilCrop = parseFloat(row.oilCrop) || 0
      const summerMisc = parseFloat(row.summerMisc) || 0
      const melonVegetableSubtotal = parseFloat(row.melonVegetableSubtotal) || 0
      const wheatIntercroppingSubtotal = parseFloat(row.wheatIntercroppingSubtotal) || 0
      row.summerCropsSubtotal = (wheat + oilCrop + summerMisc + melonVegetableSubtotal + wheatIntercroppingSubtotal) || null

      // 计算秋季作物小计
      const autumnCorn = parseFloat(row.autumnCorn) || 0
      const gourd = parseFloat(row.gourd) || 0
      const autumnPepper = parseFloat(row.autumnPepper) || 0
      const dehydratedVegetables = parseFloat(row.dehydratedVegetables) || 0
      const autumnSunflower = parseFloat(row.autumnSunflower) || 0
      const autumnMisc = parseFloat(row.autumnMisc) || 0
      row.autumnCropsSubtotal = (autumnCorn + gourd + autumnPepper + dehydratedVegetables + autumnSunflower + autumnMisc) || null

      // 计算林地小计
      const matureForest = parseFloat(row.matureForest) || 0
      const youngForest = parseFloat(row.youngForest) || 0
      const fruitTree = parseFloat(row.fruitTree) || 0
      const wolfberry = parseFloat(row.wolfberry) || 0
      row.forestlandSubtotal = (matureForest + youngForest + fruitTree + wolfberry) || null

      // 计算林牧地小计
      const forestlandSubtotal = parseFloat(row.forestlandSubtotal) || 0
      const grassland = parseFloat(row.grassland) || 0
      row.forestGrasslandSubtotal = (forestlandSubtotal + grassland) || null

      // 计算耕地面积（夏季作物小计 + 秋季作物小计）
      const summerCropsSubtotal = parseFloat(row.summerCropsSubtotal) || 0
      const autumnCropsSubtotal = parseFloat(row.autumnCropsSubtotal) || 0
      row.farmlandArea = (summerCropsSubtotal + autumnCropsSubtotal) || null

      // 计算总灌溉面积（耕地面积 + 林牧地小计）
      const farmlandArea = parseFloat(row.farmlandArea) || 0
      const forestGrasslandSubtotal = parseFloat(row.forestGrasslandSubtotal) || 0
      row.totalIrrigationArea = (farmlandArea + forestGrasslandSubtotal) || null
    },

    // 获取渠道名称
    getChannelName(channelNo) {
      console.log('查找渠道名称:', channelNo, '项目数据长度:', this.projectOptions.length)

      if (this.projectOptions.length === 0) {
        console.warn('项目数据未加载，返回原始渠道编码')
        return channelNo
      }

      const project = this.projectOptions.find(option => option.projectCode === channelNo)
      console.log('匹配结果:', project)

      return project ? project.projectName : channelNo
    },

    // 获取组织机构树数据
    async getOrgTreeData() {
      try {
        const response = await getOrgTree()
        if (response.success && response.data) {
          this.orgTree = response.data
          this.buildReportUnitOptions()
        }
      } catch (error) {
        console.error('获取组织机构树失败:', error)
      }
    },

    // 构建填报单位选项
    buildReportUnitOptions() {
      const options = []
      // 优先使用loginOrgId，其次使用deptId，最后使用默认值
      const currentUserDepId = this.$store.state.user.loginOrgId || this.$store.state.user.deptId || 10020

      // 递归查找匹配的部门
      const findMatchingDept = (nodes, targetDepId) => {
        for (const node of nodes) {
          // 确保ID比较时类型一致
          if (Number(node.deptId) === Number(targetDepId)) {
            return node
          }
          if (node.children && node.children.length > 0) {
            const found = findMatchingDept(node.children, targetDepId)
            if (found) return found
          }
        }
        return null
      }

      const matchedDept = findMatchingDept(this.orgTree, currentUserDepId)

      if (matchedDept) {
        // 添加当前部门
        options.push({
          value: matchedDept.deptId,
          label: matchedDept.deptName,
          deptId: matchedDept.deptId
        })

        // 如果有子部门，也添加子部门
        if (matchedDept.children && matchedDept.children.length > 0) {
          matchedDept.children.forEach(child => {
            options.push({
              value: child.deptId,
              label: child.deptName,
              deptId: child.deptId
            })
          })
        }
      }

      this.reportUnitOptions = options
    },

    // 获取填报单位标签
    getReportUnitLabel(value) {
      const unit = this.reportUnitOptions.find(option => option.value === value)
      return unit ? unit.label : value || '-'
    },

    // 加载作物选项字典
    async loadCropOptions() {
      try {
        const response = await getOptions('crop')
        if (response.code === 200 && response.data) {
          this.cropOptions = response.data.map(item => ({
            key: item.key,
            value: item.value,
            label: item.value
          }))
        }
      } catch (error) {
        console.error('获取作物选项失败:', error)
      }
    },

    // 测试作物匹配功能
    testCropMatchingFunction(cstIrrigationDtoList) {

      // 收集所有的种植数据用于测试
      const testData = []
      cstIrrigationDtoList.forEach(irrigationDto => {
        const { channelDtoList } = irrigationDto
        channelDtoList.forEach(channelDto => {
          const { cstIrrigationPlantIntentDetailDtoList } = channelDto
          cstIrrigationPlantIntentDetailDtoList.forEach(detail => {
            testData.push(detail)
          })
        })
      })

      // 调用新的精确匹配测试方法
      testPreciseCropMatching(testData, this.cropOptions)

    },

    // 加载项目（渠道）选项
    async loadProjectOptions() {
      try {
        this.projectOptionsLoading = true

        const response = await request({
          url: '/base/project/page',
          method: 'post',
          data: {
            pageNum: 1,
            pageSize: 1000,
            districtCode: "0"
          }
        })

        if (response.code === 200 && response.data && response.data.data) {
          this.projectOptions = response.data.data.map(item => ({
            projectId: item.projectId,
            projectCode: item.projectCode,
            projectName: item.projectName,
            projectNameAbbr: item.projectNameAbbr,
            objectCategoryName: item.objectCategoryName
          }))

          this.projectOptionsLoaded = true
        } else {
          console.error('获取项目数据失败:', response.message)
          this.$message.error('获取项目数据失败: ' + (response.message || '未知错误'))
        }
      } catch (error) {
        console.error('获取项目数据失败:', error)
        this.$message.error('获取项目数据失败: ' + (error.message || '网络错误'))
      } finally {
        this.projectOptionsLoading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.planting-intention-report {
  background: #fff;

  .basic-info-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .form-row {
      display: flex;
      gap: 30px;

      .form-item {
        display: flex;
        align-items: center;

        label {
          margin-right: 8px;
          font-weight: 500;
          color: #333;
        }

        .form-value {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }



  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e8e8e8;
  }
}
</style>