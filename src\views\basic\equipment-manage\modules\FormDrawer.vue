<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="550"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="设备编码" prop="deviceCode">
                <a-input allowClear v-model="form.deviceCode" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="设备名称" prop="deviceName">
                <a-input allowClear v-model="form.deviceName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="设备简称" prop="deviceNameAbbr">
                <a-input allowClear v-model="form.deviceNameAbbr" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="设备类别" prop="deviceCategoryId">
                <a-tree-select
                  v-model="form.deviceCategoryId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="deviceCategoryOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'deviceCategoryName',
                    key: 'deviceCategoryId',
                    value: 'deviceCategoryId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="行政区划" prop="districtCode">
                <a-tree-select
                  v-model="form.districtCode"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="districtOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'districtName',
                    key: 'districtCode',
                    value: 'districtCode',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属工程" prop="projectId">
                <a-tree-select
                  v-model="form.projectId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="projectOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'projectName',
                    key: 'projectId',
                    value: 'projectId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="外部标识" prop="outerId">
                <a-input allowClear v-model="form.outerId" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="备注" prop="remark">
                <a-textarea v-model="form.remark" placeholder="请输入" allow-clear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addDevice, updateDevice, getDevice } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormDrawer',
    props: ['deviceCategoryOptions', 'districtOptions', 'projectOptions'],
    components: { AntModal },
    data() {
      return {
        parentCategoryOptions: [],

        loading: false,
        modalLoading: false,
        formTitle: '',
        // 表单参数
        form: {
          deviceCode: undefined,
          deviceName: undefined,
          deviceNameAbbr: undefined,
          deviceCategoryId: undefined,
          districtCode: undefined,
          projectId: undefined,
          outerId: undefined,
          remark: undefined,
        },
        open: false,

        rules: {
          deviceName: [{ required: true, message: '设备名称不能为空', trigger: 'blur' }],
          deviceCode: [{ required: true, message: '设备编码不能为空', trigger: 'blur' }],
          deviceCategoryId: [{ required: true, message: '设备类别不能为空', trigger: 'change' }],
          districtCode: [{ required: true, message: '行政区划不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
        },
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd(r, treeTabType) {
        this.open = true
        this.formTitle = '新增'

        if (treeTabType === '1') {
          // this.form.objectCategoryId = r.objectCategoryId || undefined
          this.form.projectId = r.treeNodeId
        } else if (treeTabType === '2') {
          this.form.districtCode = r.districtCode || undefined
        } else if (treeTabType === '3') {
          this.form.deviceCategoryId = r.deviceCategoryId || undefined
        }
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.modalLoading = true
        getDevice({ deviceId: record.deviceId }).then(res => {
          this.form = res.data
          this.modalLoading = false
        })
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            const saveForm = JSON.parse(JSON.stringify(this.form))
            this.loading = true

            if (this.form.deviceId !== undefined) {
              updateDevice(saveForm)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            } else {
              addDevice(saveForm)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
