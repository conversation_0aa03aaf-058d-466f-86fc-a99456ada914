import request from '@/utils/request'

// 列表分页查询
export function getDispatchPage(data) {
  return request({
    url: '/custom/dispatch/page',
    method: 'post',
    data,
  })
}
// 增加
export function addDispatch(data) {
  return request({
    url: '/custom/dispatch/add',
    method: 'post',
    data,
  })
}
// 详情
export function getDispatchById(params) {
  return request({
    url: '/custom/dispatch/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editDispatch(data) {
  return request({
    url: '/custom/dispatch/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteDispatch(params) {
  return request({
    url: '/custom/dispatch/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
