import request from '@/utils/request'

// 发布项维护
export function getMaintenanceList(data) {
  return request({
    url: '/war/reportItem/page',
    method: 'post',
    data
  })
}
// 增加
export function addMaintenance(data) {
  return request({
    url: '/war/reportItem/add',
    method: 'post',
    data
  })
}
// 更新
export function editMaintenance(data) {
  return request({
    url: '/war/reportItem/update',
    method: 'post',
    data
  })
}
// 删除
export function deleteMaintenance(params) {
  return request({
    url: '/war/reportItem/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
