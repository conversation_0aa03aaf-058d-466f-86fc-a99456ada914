<template>
  <!-- 增加修改 -->
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      modalWidth="600"
      @cancel="cancel"
      modalHeight="550"
    >
      <div slot="content">
        <div class="table-panel" layout="vertical">
          <a-form-model ref="form" :model="form" :rules="rules">
            <a-row class="form-row" :gutter="32">
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="类别编码" prop="objectCategoryCode">
                  <a-input allowClear v-model="form.objectCategoryCode" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="类别名称" prop="objectCategoryName">
                  <a-input allowClear v-model="form.objectCategoryName" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="上级类别" prop="parentId">
                  <a-tree-select
                    v-model="form.parentId"
                    style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :tree-data="parentCategoryOptions"
                    show-search
                    treeNodeFilterProp="title"
                    allowClear
                    placeholder="请选择"
                    :replaceFields="{
                      children: 'children',
                      title: 'objectCategoryName',
                      key: 'objectCategoryId',
                      value: 'objectCategoryId',
                    }"
                    tree-default-expand-all
                  ></a-tree-select>
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="展示信息" prop="displayCodes">
                  <a-select
                    allowClear
                    mode="multiple"
                    :maxTagCount="2"
                    v-model="form.displayCodes"
                    placeholder="请选择"
                    option-filter-prop="children"
                  >
                    <a-select-option v-for="(d, index) in displayOptions" :key="index" :value="d.key">
                      {{ d.value }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <!--  -->

              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="GIS图层">
                  <a-select
                    allowClear
                    v-model="form.gisLayer"
                    placeholder="请选择"
                    show-search
                    :filter-option="filterOption"
                  >
                    <a-select-option v-for="(d, index) in layerOptions" :key="index" :value="d.name">
                      {{ d.name }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="GIS图层类型">
                  <a-select
                    allowClear
                    v-model="form.gisLayerType"
                    placeholder="请选择"
                    show-search
                    :filter-option="filterOption"
                  >
                    <a-select-option v-for="(d, index) in layerTypeOptions" :key="index" :value="d.value">
                      {{ d.label }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="标签">
                  <a-select
                    allowClear
                    mode="multiple"
                    :maxTagCount="2"
                    v-model="form.labels"
                    placeholder="请选择"
                    show-search
                    :filter-option="filterOption"
                  >
                    <a-select-option v-for="(d, index) in labelsOptions" :key="index" :value="d.value">
                      {{ d.label }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="排序号" prop="sort">
                  <a-input-number v-model="form.sort" style="width: 100%" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :lg="24" :md="24" :sm="24" :span="24">
                <a-form-model-item label="备注" prop="remark">
                  <a-textarea v-model="form.remark" placeholder="请输入" allowClear />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </div>
      </div>
      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import {
    getObjectCategoryParentTree,
    getObjectCategoryNextSort,
    addObjectCategory,
    updateObjectCategory,
    getGeoserver,
    getObjectCategoryDetail,
  } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormDrawer',
    props: {},
    components: { AntModal },
    data() {
      return {
        parentCategoryOptions: [],
        displayOptions: [],
        layerOptions: [],
        layerTypeOptions: [],
        labelsOptions: [],

        loading: false,
        modalLoading: false,
        // 默认密码
        formTitle: '',
        // 表单参数
        form: {
          objectCategoryId: undefined,
          objectCategoryName: undefined,
          parentId: undefined,
          objectCategoryCode: undefined,
          displayCodes: undefined,
          gisLayer: null,
          gisLayerType: undefined,
          labels: undefined,
          sort: undefined,
          remark: undefined,
        },
        open: false,

        rules: {
          objectCategoryName: [{ required: true, message: '类别名称不能为空', trigger: 'blur' }],
          parentId: [{ required: true, message: '上级类别不能为空', trigger: 'change' }],
          objectCategoryCode: [{ required: true, message: '类别编码不能为空', trigger: 'blur' }],
          sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
        },
      }
    },
    filters: {},
    created() {
      this.init()
    },
    computed: {},
    watch: {},
    methods: {
      init() {
        // 字典获取下拉
        getOptions('displayInfoTypes').then(res => {
          this.displayOptions = res?.data || []
        })
        getGeoserver().then(res => {
          this.layerOptions = res?.data
        })
        getOptions('gisLayerType').then(res => {
          this.layerTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('objectCategoryLabel').then(res => {
          this.labelsOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd(parentId) {
        this.open = true
        this.formTitle = '新增'
        this.form.parentId = parentId || undefined
        this.getParentTree()
        getObjectCategoryNextSort({ parentId }).then(res => {
          this.form.sort = res.data
        })
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.getParentTree(record)
        this.modalLoading = true
        getObjectCategoryDetail({ objectCategoryId: record.objectCategoryId }).then(res => {
          this.form = res.data
          this.modalLoading = false
        })
      },
      // 获取父节点树
      getParentTree(record) {
        getObjectCategoryParentTree({ objectCategoryId: record?.objectCategoryId }).then(res => {
          this.parentCategoryOptions = res?.data || []
        })
      },
      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.form.gisLayer = this.form.gisLayer == undefined ? '' : this.form.gisLayer
            this.form.gisLayerType = this.form.gisLayerType == undefined ? '' : this.form.gisLayerType

            const saveForm = JSON.parse(JSON.stringify(this.form))
            this.loading = true
            if (this.form.objectCategoryId !== undefined) {
              updateObjectCategory(saveForm)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            } else {
              addObjectCategory(saveForm)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
