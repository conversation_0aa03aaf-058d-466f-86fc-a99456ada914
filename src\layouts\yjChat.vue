<template>
  <div class="page-mask">
    <div class="chat-container">
      <div class="chat-header">
        <h1>永济灌区知识助理</h1>
        <h6 class="close" @click="close">×</h6>
      </div>

      <div class="chat-body" ref="chatBodyRef">
        <div class="chat-messages">
          <div
            v-for="(msg, index) in messages"
            :key="index"
            :class="['message', msg.role === 'user' ? 'user-message' : 'assistant-message']"
          >
            <div class="avatar">
              <div class="avatar-icon" :class="msg.role === 'user' ? 'user-avatar' : 'assistant-avatar'">
                <i :class="msg.role === 'user' ? 'fas fa-user' : 'fas fa-robot'"></i>
              </div>
            </div>
            <div class="content">
              <div v-if="msg.role === 'assistant' && msg.loading" class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div v-else v-html="formatMessage(msg.content)"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="chat-footer">
        <div class="input-container">
          <a-textarea
            v-model="userInput"
            placeholder="请输入您的问题..."
            allow-clear
            @keydown.enter.prevent="sendMessage"
          />
          <div class="button-group">
            <a-button type="primary" :disabled="isLoading || !userInput.trim()" @click="sendMessage">发送</a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, nextTick, watch, onBeforeUnmount } from 'vue'
  // import { ElMessage } from 'element-plus'
  import chatService from './yjChat.js'

  // 聊天记录
  const messages = ref([{ role: 'assistant', content: '你好！我是永济灌区知识助理，请问有什么可以帮助您的？' }])
  const userInput = ref('')
  const isLoading = ref(false)
  const chatBodyRef = ref(null)
  const chatId = ref(`chat_${Date.now()}`)
  const sseConnection = ref(null)

  const emits = defineEmits('close')
  const close = () => {
    emits('close')
  }

  // 发送消息
  const sendMessage = async () => {
    if (!userInput.value.trim() || isLoading.value) return

    // 添加用户消息
    const userMessage = userInput.value
    messages.value.push({ role: 'user', content: userMessage })

    // 清空输入框
    userInput.value = ''

    // 添加助手消息占位，并初始化lastProcessedLength
    const assistantIndex = messages.value.length
    messages.value.push({ role: 'assistant', content: '', loading: true, lastProcessedLength: 0 })

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    isLoading.value = true

    try {
      // 准备请求数据
      const requestData = {
        chatId: chatId.value,
        stream: true,
        detail: false,
        messages: [{ role: 'user', content: userMessage }],
      }

      // 清理之前的SSE连接
      if (sseConnection.value) {
        sseConnection.value.close()
        sseConnection.value = null
      }

      // 确保初始化助手消息为空字符串
      messages.value[assistantIndex].content = ''
      messages.value[assistantIndex].lastProcessedLength = 0

      // 使用POST方式发起流式请求
      let xhr = null
      try {
        xhr = chatService.chatStream(requestData, progressEvent => {
          if (progressEvent && progressEvent.currentTarget) {
            const responseText = progressEvent.currentTarget.responseText || progressEvent.currentTarget.response
            if (responseText) {
              handleStreamResponse(responseText, assistantIndex)
            }
          }
        })
        xhr.onloadend = () => {
          // 请求结束时，最后再处理一次完整内容
          if (xhr && xhr.responseText) {
            handleStreamResponse(xhr.responseText, assistantIndex)
          }
          messages.value[assistantIndex].loading = false
        }
      } catch (error) {
        // 如果流式请求失败，回退到同步方式
        requestData.stream = false
        const syncResponse = await chatService.chat(requestData)

        if (syncResponse && syncResponse.data && syncResponse.data.choices && syncResponse.data.choices.length > 0) {
          const assistantMessage = syncResponse.data.choices[0].message
          if (assistantMessage && assistantMessage.content) {
            // 过滤掉<think>标签
            let content = assistantMessage.content.replace(/<think>[\s\S]*?<\/think>/g, '')
            messages.value[assistantIndex].content = content
            messages.value[assistantIndex].loading = false
          }
        }
      }
    } catch (error) {
      console.error('发送消息失败', error)
      this.$message.error('发送消息失败，请稍后重试')
      this.$message.error('抱歉，我遇到了一些问题，请稍后再试。')
      // messages.value[assistantIndex].content = '抱歉，我遇到了一些问题，请稍后再试。'
      // messages.value[assistantIndex].loading = false
    } finally {
      isLoading.value = false
      scrollToBottom()
    }
  }

  // 处理流式响应
  const handleStreamResponse = (responseText, assistantIndex) => {
    try {
      if (!responseText) return

      // 只处理新收到的部分
      let msg = messages.value[assistantIndex]
      let start = msg.lastProcessedLength || 0
      let newText = responseText.slice(start)
      msg.lastProcessedLength = responseText.length

      if (!newText) return

      // 处理 event:message/data: 成对结构
      const lines = newText.split('\n')
      let extractedContent = ''
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()
        if (line.startsWith('event:message')) {
          // 下一个非空行如果是data:，才处理
          let j = i + 1
          while (j < lines.length && lines[j].trim() === '') j++
          if (j < lines.length && lines[j].trim().startsWith('data:')) {
            const dataLine = lines[j].trim()
            try {
              const jsonText = dataLine.substring(5)
              const jsonData = JSON.parse(jsonText)
              if (
                jsonData.choices &&
                jsonData.choices[0] &&
                jsonData.choices[0].delta &&
                'content' in jsonData.choices[0].delta
              ) {
                const content = jsonData.choices[0].delta.content
                if (content && content !== 'superscript:' && content !== '\n\n' && !content.includes('<think>')) {
                  extractedContent += content
                }
              }
            } catch (e) {}
          }
        }
      }
      if (extractedContent) {
        msg.content += extractedContent
        msg.loading = false
        scrollToBottom()
      }
    } catch (error) {
      console.error('处理流式响应失败', error)
    }
  }

  // 格式化消息内容
  const formatMessage = content => {
    if (!content) return ''

    // 去除<think>标签及其内容
    let formattedContent = content.replace(/<think>[\s\S]*?<\/think>/g, '')

    // 去除<|FunctionCallBegin|>前缀
    formattedContent = formattedContent.replace(/<\|FunctionCallBegin\|>/g, '')

    // 去除可能的superscript前缀
    formattedContent = formattedContent.replace(/superscript:\s*\n*/g, '')

    // 处理换行
    formattedContent = formattedContent.replace(/\n/g, '<br>')

    return formattedContent
  }

  // 滚动到底部
  const scrollToBottom = () => {
    if (chatBodyRef.value) {
      chatBodyRef.value.scrollTop = chatBodyRef.value.scrollHeight
    }
  }

  // 监听消息变化，自动滚动
  watch(
    messages,
    () => {
      nextTick(() => scrollToBottom())
    },
    { deep: true },
  )

  // 组件挂载后滚动到底部
  onMounted(() => {
    scrollToBottom()
  })

  // 在组件卸载前清理资源
  onBeforeUnmount(() => {
    if (sseConnection.value) {
      sseConnection.value.close()
    }
  })
</script>

<style lang="less" scoped>
  .page-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    filter: alpha(opacity=50);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .chat-container {
    display: flex;
    flex-direction: column;
    width: 663px;
    height: 664px;
    background-color: #f5f7fa;
  }

  .chat-header {
    padding: 16px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    text-align: center;
    display: flex;
  }
  .close {
    font-size: 28px;
    margin-left: auto;
    cursor: pointer;
  }

  .chat-header h1 {
    margin: 0;
    font-size: 20px;
    color: #333;
  }

  .chat-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
  }

  .chat-messages {
    max-width: 800px;
    margin: 0 auto;
  }

  .message {
    display: flex;
    margin-bottom: 20px;
  }

  .user-message {
    flex-direction: row-reverse;
  }

  .avatar {
    flex-shrink: 0;
    margin: 0 10px;
  }

  .avatar-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
  }

  .user-avatar {
    background-color: #409eff;
  }

  .assistant-avatar {
    background-color: #67c23a;
  }

  .content {
    padding: 12px 16px;
    border-radius: 8px;
    max-width: 70%;
    word-break: break-word;
  }

  .user-message .content {
    background-color: #ecf5ff;
    color: #333;
    border-radius: 8px 0 8px 8px;
  }

  .assistant-message .content {
    background-color: #fff;
    color: #333;
    border-radius: 0 8px 8px 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .chat-footer {
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid #ebeef5;
  }

  .input-container {
    max-width: 800px;
    margin: 0 auto;
    :deep(.ant-input) {
      height: 80px;
    }
  }

  .button-group {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }

  /* 打字指示器样式 */
  .typing-indicator {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .typing-indicator span {
    height: 8px;
    width: 8px;
    margin: 0 2px;
    background-color: #67c23a;
    display: block;
    border-radius: 50%;
    opacity: 0.4;
    animation: typing 1s infinite alternate;
  }

  .typing-indicator span:nth-child(1) {
    animation-delay: 0s;
  }

  .typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes typing {
    0% {
      opacity: 0.4;
      transform: translateY(0);
    }
    100% {
      opacity: 1;
      transform: translateY(-5px);
    }
  }
</style>
