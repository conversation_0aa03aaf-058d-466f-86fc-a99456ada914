<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="模板名称">
        <a-input
          v-model="queryParam.reportTemplateName"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>
      <a-form-item label="创建人">
        <a-select
          show-search
          placeholder="请输入"
          v-model="queryParam.createdUserId"
          option-filter-prop="children"
          :filter-option="filterOptionUser"
          @change="handleChangeUser"
        >
          <a-select-option v-for="item in createPersonList" :key="item.userId" :value="item.userId">
            {{ item.name }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="创建时间">
        <a-range-picker
          allow-clear
          :value="takeEffect"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormTemplate
          v-if="showFormTemplate"
          ref="formTemplateRef"
          @ok="onOperationComplete"
          @close="showFormTemplate = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getComUserList } from '@/api/common'
  import { getTemplateList, deleteTemplate } from './services'
  import FormTemplate from './modules/FormTemplate.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'templateConfig',
    components: {
      VxeTable,
      VxeTableForm,
      FormTemplate,
    },
    data() {
      return {
        createPersonList: [],
        showFormTemplate: false,
        takeEffect: [],
        lineTypes: {},
        list: [],
        tableTitle: '模板配置',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          createdUserId: null,
          endTime: '',
          pageNum: 1,
          pageSize: 10,
          reportTemplateName: '',
          startTime: '',
        },
        columns: [
          { type: 'checkbox', width: 40 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '模板名称',
            field: 'reportTemplateName',
          },
          {
            title: '简报标题',
            field: 'reportTitle',
          },
          {
            title: '简报报送单位',
            field: 'reportUnit',
          },
          {
            title: '创建人',
            field: 'createdUserName',
          },
          {
            title: '创建时间',
            field: 'createdTime',
          },
          {
            title: '操作',
            field: 'operate',
            width: 128,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {
      getComUserList({}).then(res => {
        if (res?.code == 200) {
          this.createPersonList = res?.data
        }
      })
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      onRangeChange(value, takeEffect) {
        this.takeEffect = value
        if (takeEffect.length == 0) {
          return
        }
        this.queryParam.startTime = takeEffect[0] ? moment(takeEffect[0]).format('YYYY-MM-DD') + ' 00:00:00' : undefined
        this.queryParam.endTime = takeEffect[1] ? moment(takeEffect[1]).format('YYYY-MM-DD') + ' 23:59:59' : undefined
      },
      /** 查询列表 */
      getList() {
        this.showFormTemplate = false
        this.loading = true
        getTemplateList(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      handleChangeUser(value) {
        this.queryParam.createdUserId = value
      },
      filterOptionUser(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.reportTemplateId)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          createdUserId: null,
          endTime: '',
          pageNum: 1,
          reportTemplateName: '',
          startTime: '',
        }
        this.takeEffect = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /* 新增 */
      handleAdd() {
        this.showFormTemplate = true
        this.$nextTick(() => this.$refs.formTemplateRef.handleTemplate())
      },
      /* 修改 */
      handleEdit(record) {
        this.showFormTemplate = true
        this.$nextTick(() => this.$refs.formTemplateRef.handleTemplate(record))
      },
      switchChange(record) {
        if (record.isStopped) {
          this.handleParamSet(record, 2)
        }
        if (!record.isStopped) {
          this.handleParamSet(record, 1)
        }
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const templateIds = row?.reportTemplateId ? [row?.reportTemplateId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteTemplate({ reportTemplateIds: templateIds.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`删除成功`, 3)
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
