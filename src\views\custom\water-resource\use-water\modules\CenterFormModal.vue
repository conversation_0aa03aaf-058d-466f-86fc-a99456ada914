<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="880"
  >
    <div slot="content" style="height: 100%">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">基本信息</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="时间维度" prop="planType">
              <a-radio-group
                v-model="form.planType"
                :options="radioOptions"
                @change="handlePlanTypeChange"
                :disabled="isDetail || !!rowInfo?.planReportId"
              ></a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="计划时间" prop="dateRange">
              <a-range-picker
                v-if="form.planType !== 3"
                :disabled="isDetail || !!rowInfo?.planReportId"
                style="width: 100%"
                v-model="form.dateRange"
                :allowClear="false"
                :placeholder="['开始时间', '结束时间']"
                :format="form.planType === 3 ? 'YYYY-MM' : 'YYYY-MM-DD'"
                :mode="form.planType === 3 ? ['month', 'month'] : ['date', 'date']"
                @panelChange="handlePanelChange"
                :disabledDate="disabledDate"
                @change="handleDateRangeChange"
              />
              <div v-else style="display: flex; align-items: center; gap: 10px">
                <a-month-picker
                  format="YYYY-MM"
                  :allowClear="false"
                  :disabled="isDetail || !!rowInfo?.planReportId"
                  placeholder="开始月份"
                  v-model="startMonth"
                  :disabledDate="disabledStartMonthDate"
                />
                <a-month-picker
                  format="YYYY-MM"
                  :allowClear="false"
                  :disabled="isDetail || !!rowInfo?.planReportId"
                  placeholder="结束月份"
                  v-model="endMonth"
                  :disabledDate="disabledEndMonthDate"
                />
              </div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">用水计划</div>
          </a-col>
        </a-row>
      </a-form-model>

      <div style="margin: 0 16px 5px">
        <span style="font-weight: 500">注：</span>
        <span>单位：Q日，1Q日=8.64万m³</span>
      </div>

      <vxe-grid
        :key="columns.length"
        border="full"
        size="small"
        :columns="columns"
        :columnConfig="{ resizable: false }"
        :data="tableData"
        :rowConfig="{ isHover: false }"
        :mergeCells="[
          { row: 0, col: 0, rowspan: 9, colspan: 1 },
          { row: 0, col: 1, rowspan: 1, colspan: 2 },
          { row: 1, col: 1, rowspan: 1, colspan: 2 },
          { row: 2, col: 1, rowspan: 1, colspan: 2 },
          { row: 3, col: 1, rowspan: 6, colspan: 1 },
        ]"
        show-footer
        :footerMethod="footerMethod"
        :merge-footer-items="[
          { row: 1, col: 0, rowspan: 1, colspan: 3 },
          { row: 2, col: 0, rowspan: 1, colspan: 3 },
          { row: 2, col: 3, rowspan: 1, colspan: 999 },
        ]"
      />
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading" v-if="!isDetail">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getOrgTree } from '@/api/user'
  import { addPlanReport, getPlanReportDetails, updatePlanReport, getPlanReportList } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'
  import getMapFlatTree from '@/utils/getMapFlatTree.js'
  import { orgConfig } from '../config'
  import { dealNumber, getFixedNum } from '@/utils/dealNumber.js'
  import * as _ from 'lodash'

  export default {
    name: 'CenterFormModal',
    components: { AntModal },
    props: ['radioOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        formTitle: '',
        rowInfo: null,

        deptName: '',
        deptList: [],
        isDetail: false,
        columns: [],
        tableData: [],
        summaries: [],
        remarks: undefined,

        startMonth: moment().add(1, 'month').startOf('month'),
        endMonth: moment().add(1, 'month').endOf('month'),
        // 表单参数
        form: {
          planReportId: undefined,
          planType: 1,
          planName: '',
          dateRange: undefined,
        },
        rules: {
          planType: [{ required: true, message: '时间维度不能为空', trigger: 'change' }],
          dateRange: [{ required: true, message: '计划时间不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    computed: {},
    watch: {
      startMonth: {
        handler(newVal) {
          this.form.dateRange = [newVal, this.endMonth]
          this.handleDateRangeChange()
        },
        deep: true,
      },
      endMonth: {
        handler(newVal) {
          this.form.dateRange = [this.startMonth, newVal]
          this.handleDateRangeChange()
        },
        deep: true,
      },
    },
    methods: {
      disabledStartMonthDate(startValue) {
        const endValue = this.endMonth
        if (!startValue || !endValue) {
          return false
        }
        return (
          startValue.valueOf() < moment().add(1, 'month').startOf('month').valueOf() ||
          startValue.valueOf() >= endValue.valueOf()
        )
      },
      disabledEndMonthDate(endValue) {
        const startValue = this.startMonth
        if (!endValue || !startValue) {
          return false
        }
        return (
          endValue.valueOf() < moment().add(1, 'month').startOf('month').valueOf() ||
          startValue.valueOf() >= endValue.valueOf()
        )
      },

      disabledDate(current, endCurrent) {
        return current && current < moment().endOf('day')
      },
      handleDateRangeChange() {
        setTimeout(() => {
          this.$nextTick(() => {
            this.getPlanInfo()
          })
        }, 20)
      },
      handlePanelChange(value, mode) {
        this.form.dateRange = value
        this.handleDateRangeChange()
      },
      handlePlanTypeChange(e) {
        if (e.target.value === 1) {
          this.form.dateRange = [moment().add(1, 'day'), moment().add(5, 'day')]
        }
        if (e.target.value === 2) {
          this.form.dateRange = [moment().add(1, 'day'), moment().add(10, 'day')]
        }
        if (e.target.value === 3) {
          this.form.dateRange = [moment().add(1, 'month').startOf('month'), moment().add(1, 'month').endOf('month')]
        }

        if (this.rowInfo) {
          this.form.planType = +this.rowInfo.planType
          this.form.dateRange = [moment(this.rowInfo.planStartDate), moment(this.rowInfo.planEndDate)]
        }

        this.form.planName =
          this.rowInfo?.planName ||
          `${this.deptName}${this.radioOptions.find(item => item.value === this.form.planType)?.label}计划${this.form.dateRange[0].format('YYYY-MM-DD')}`

        this.$nextTick(() => {
          this.getPlanInfo()
        })
      },
      sumNum1(list, date, field) {
        let count = 0
        list.forEach((item, idx) => {
          if (idx > 2) {
            count += Number(item.recordObj[date][field])
          }
        })
        return getFixedNum(count, 1)
      },
      sumNum2(list, date, field) {
        let count = 0
        list.forEach((item, idx) => {
          count += Number(item.recordObj[date][field])
        })
        return getFixedNum(count, 1)
      },
      footerMethod({ columns, data }) {
        const footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 2) {
              return '永济渠合计'
            }
            if (_columnIndex > 2 && _columnIndex < columns.length - 1) {
              return `${this.sumNum1(data, column.property, 'planStartFlow')} ~ ${this.sumNum1(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '永济渠报总局'
            }
            if (_columnIndex > 2 && _columnIndex < columns.length - 1) {
              return `${this.sumNum2(data, column.property, 'planStartFlow')} ~ ${this.sumNum2(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '备注'
            }
            if (_columnIndex > 2 && _columnIndex < columns.length - 1) {
              return this.remarks
            }
            return null
          }),
        ]
        return footerData
      },
      dealColumns(midTableData) {
        const dateRange = [moment(this.form.dateRange[0]), moment(this.form.dateRange[1])]

        this.tableData = midTableData
        this.columns = [
          {
            title: '所名',
            field: 'tableDeptName',
            align: 'center',
            width: 50,
            fixed: 'left',
            headerClassName: 'custmer-span',
            slots: {
              default: () => (
                <div style='writing-mode: vertical-rl;width: 100%; display: flex; align-items: center; letter-spacing: 6px'>
                  永济灌区
                </div>
              ),
            },
          },
          {
            title: '',
            align: 'center',
            field: 'tableDeptName',
            minWidth: 70,
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
            slots: {
              default: ({ row, rowIndex }) => {
                if (rowIndex < 3) {
                  return (
                    <div style='width:100%; display: flex; align-items: center; justify-content: center; height: 100%;'>
                      {row.tableDeptName}
                    </div>
                  )
                }
                return (
                  <div style='display: flex; align-items: center; justify-content: center; height: 100%;'>永济渠</div>
                )
              },
            },
          },
          {
            title: '',
            field: 'tableDeptName',
            align: 'center',
            minWidth: 90,
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
          },
          // 生成日期列
          ...[...Array(dateRange[1].diff(dateRange[0], 'day') + 1)].map((item, index) => {
            const date = moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD')

            return {
              title: moment(dateRange[0]).add(index, 'day').format('MM月DD日'),
              align: 'center',
              minWidth: 120,
              field: moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD'),
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number
                        size='small'
                        v-model={this.tableData[rowIndex].recordObj[date].planStartFlow}
                        min={0}
                        precision={1}
                        disabled={true}
                      />
                      &nbsp;~&nbsp;
                      <a-input-number
                        size='small'
                        v-model={this.tableData[rowIndex].recordObj[date].planEndFlow}
                        min={0}
                        precision={1}
                        disabled={true}
                      />
                    </div>
                  )
                },
                footer: rowInfo => {
                  // 永济渠合计
                  if (rowInfo.rowIndex === 0) {
                    return rowInfo.row[rowInfo.itemIndex]
                  }
                  // 永济渠报总局
                  if (rowInfo.rowIndex === 1) {
                    if (rowInfo.itemIndex < 2 || rowInfo.itemIndex === rowInfo.items.length - 1)
                      return rowInfo.row[rowInfo.itemIndex]

                    return (
                      <div class='table-cell-box'>
                        <a-input-number
                          disabled={this.isDetail}
                          size='small'
                          v-model={this.summaries[rowInfo.itemIndex - 3].planStartFlow}
                          min={0}
                          precision={1}
                          onChange={() => {
                            this.$nextTick(val => {
                              if (val === null) {
                                this.summaries[rowInfo.itemIndex - 3].planStartFlow = 0
                              } else {
                                const obj = this.summaries[rowInfo.itemIndex - 3]
                                if (obj.planStartFlow > obj.planEndFlow) {
                                  this.summaries[rowInfo.itemIndex - 3].planStartFlow = obj.planEndFlow
                                }
                              }
                            })
                          }}
                        />
                        &nbsp;~&nbsp;
                        <a-input-number
                          disabled={this.isDetail}
                          size='small'
                          v-model={this.summaries[rowInfo.itemIndex - 3].planEndFlow}
                          min={0}
                          precision={1}
                          onChange={val => {
                            this.$nextTick(() => {
                              if (val === null) {
                                this.summaries[rowInfo.itemIndex - 3].planEndFlow = 0
                                this.summaries[rowInfo.itemIndex - 3].planStartFlow = 0
                              } else {
                                const obj = this.summaries[rowInfo.itemIndex - 3]
                                if (obj.planStartFlow > obj.planEndFlow) {
                                  this.summaries[rowInfo.itemIndex - 3].planEndFlow = obj.planStartFlow
                                }
                              }
                            })
                          }}
                        />
                      </div>
                    )
                  }
                  if (rowInfo.rowIndex === 2) {
                    return <a-input size='small' v-model={this.remarks} disabled={this.isDetail} />
                  }
                },
              },
            }
          }),

          {
            title: '备注',
            field: 'remark',
            align: 'center',
            minWidth: 120,
            showOverflow: true,
            slots: {
              default: ({ row, rowIndex }) => {
                return <a-input size='small' v-model={this.tableData[rowIndex].remarks} disabled />
              },
            },
          },
        ]
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 修改按钮操作 */
      handleAdd(row) {
        this.open = true
        this.formTitle = '用水计划上报'
        this.modalLoading = true
        this.rowInfo = row ? { ...row } : null
        this.isDetail = row?.isDetail || false

        getOptions('planWaterDept').then(resp => {
          this.deptList = resp.data.map(item => {
            return {
              deptName: item.value,
              deptId: item.key,
            }
          })

          if (!this.rowInfo) {
            getOrgTree().then(res => {
              const userDeptId = JSON.parse(localStorage.getItem('user')).deptId
              const flatObj = getMapFlatTree(res.data, 'deptId')

              this.deptName = flatObj?.[userDeptId]?.deptName

              this.handlePlanTypeChange({ target: { value: 1 } })
            })
          } else {
            this.deptName = this.rowInfo.deptName
            this.form.planReportId = this.rowInfo.planReportId
            this.handlePlanTypeChange({ target: { value: 1 } })
          }
        })
      },
      getPlanInfo() {
        const dealFn = res => {
          const midTableData = res.data.map(item => {
            const recordObj = {}
            item.records?.forEach(item => {
              recordObj[item.planDate] = {
                planDate: item.planDate,
                planStartFlow: item.planStartFlow,
                planEndFlow: item.planEndFlow,
              }
            })
            return {
              ...item,
              recordObj,
            }
          })

          // 处理总计
          if (!this.rowInfo) {
            const arr = []
            midTableData[0].records.forEach(el => {
              arr.push(el)
            })
            arr.forEach(ele => {
              ele.planStartFlow = midTableData.reduce((accumulator, currentValue) => {
                return accumulator + Number(currentValue.recordObj[ele.planDate].planStartFlow)
              }, 0)
              ele.planEndFlow = midTableData.reduce((accumulator, currentValue) => {
                return accumulator + Number(currentValue.recordObj[ele.planDate].planEndFlow)
              }, 0)
            })
            this.summaries = arr
          }

          this.dealColumns(midTableData)

          // this.$nextTick(() => {
          //   setTimeout(() => {
          //     this.$nextTick(() => {
          //       document.querySelectorAll('.custmer-span').forEach(item => {
          //         item.setAttribute('colspan', '3')
          //       })
          //     })
          //   }, 400)
          // })

          this.modalLoading = false
        }

        this.tableData = []
        this.columns = []
        this.$nextTick(() => {
          if (this.rowInfo) {
            getPlanReportDetails({ planReportId: this.rowInfo.planReportId }).then(resp => {
              this.summaries = resp.data.summaries
              this.remarks = resp.data.remarks

              const arr = this.deptList
                .filter(item => resp.data.reportDetailsVOs.some(ele => ele.deptId === +item.deptId))
                .map(item => {
                  return {
                    ...resp.data.reportDetailsVOs.find(ele => ele.deptId === +item.deptId),
                    tableDeptName: item.deptName,
                  }
                })

              dealFn({ data: arr })
            })
          } else {
            getPlanReportList({
              planType: this.form.planType,
              reportType: 1,
              planStartDate: this.form.dateRange[0].format('YYYY-MM-DD'),
              planEndDate: this.form.dateRange[1].format('YYYY-MM-DD'),
            }).then(res => {
              const arr = this.deptList
                .filter(item => res.data.some(ele => ele.deptId === +item.deptId))
                .map(item => {
                  return { ...res.data.find(ele => ele.deptId === +item.deptId), tableDeptName: item.deptName }
                })

              dealFn({ data: arr })
            })
          }
        })
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            const params = {
              ...this.form,
              dateRange: undefined,
              planStartDate: this.form.dateRange[0].format('YYYY-MM-DD'),
              planEndDate: this.form.dateRange[1].format('YYYY-MM-DD'),
              remarks: this.remarks,
              summaries: this.summaries,
              reportType: 1,
              planWaters: this.tableData.map(ele => {
                return {
                  ...ele,
                  records: Object.values(ele.recordObj).map(item => {
                    if (item.planStartFlow === undefined && item.planEndFlow === undefined) {
                      return { ...item, planStartFlow: 0, planEndFlow: 0 }
                    }
                    if (item.planStartFlow === undefined || item.planEndFlow === undefined) {
                      if (item.planStartFlow === undefined) {
                        return { ...item, planStartFlow: item.planEndFlow }
                      }
                      if (item.planEndFlow === undefined) {
                        return { ...item, planEndFlow: item.planStartFlow }
                      }
                    }

                    return item
                  }),
                }
              }),
              reportDetailsVOs: this.tableData.map(ele => {
                return {
                  ...ele,
                  records: Object.values(ele.recordObj).map(item => {
                    if (item.planStartFlow === undefined && item.planEndFlow === undefined) {
                      return { ...item, planStartFlow: 0, planEndFlow: 0 }
                    }
                    if (item.planStartFlow === undefined || item.planEndFlow === undefined) {
                      if (item.planStartFlow === undefined) {
                        return { ...item, planStartFlow: item.planEndFlow }
                      }
                      if (item.planEndFlow === undefined) {
                        return { ...item, planEndFlow: item.planStartFlow }
                      }
                    }

                    return item
                  }),
                }
              }),
            }
            this.loading = true

            if (this.rowInfo) {
              updatePlanReport(params)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                })
                .finally(() => (this.loading = false))
            } else {
              addPlanReport(params)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                })
                .finally(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }

  ::v-deep .table-cell-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .ant-input-number-handler-wrap {
      display: none;
    }
  }

  ::v-deep .modal-content {
    height: 100%;
  }

  ::v-deep .vxe-table--render-default.border--inner .vxe-cell--col-resizable:before {
    background-color: transparent;
  }

  // ::v-deep .hidden-cell {
  //   display: none !important;
  // }

  ::v-deep .vxe-table--render-default.size--small .vxe-body--column.is--padding .vxe-cell {
    // height: 30px !important;
  }

  // 针对合并后的单元格进行
  /deep/ .vxe-header--column,
  /deep/ .vxe-body--column {
    text-align: center !important;
    justify-content: center !important;
    align-items: center !important;
  }

  ::v-deep .vxe-body--column .vxe-cell {
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }

  /deep/ .vxe-body--column .vxe-cell .vxe-cell--wrapper {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    height: 100% !important;
  }
</style>
