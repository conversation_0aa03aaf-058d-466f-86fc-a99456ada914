import request from '@/utils/request'

// 水利对象分类-根据编码获取树 其他管理对象
export function getTreeByCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'EX' },
  })
}

// 其他管理对象-列表分页查询
export function getOtherObjectPage(data) {
  return request({
    url: '/base/otherObject/page',
    method: 'post',
    data,
  })
}

//其他管理对象-删除
export function deleteOtherObject(params) {
  return request({
    url: '/base/otherObject/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 其他管理对象-新增
export function addOtherObject(data) {
  return request({
    url: '/base/otherObject/add',
    method: 'post',
    data,
  })
}

// 其他管理对象-更新
export function updateOtherObject(data) {
  return request({
    url: '/base/otherObject/update',
    method: 'post',
    data,
  })
}

// 其他管理对象-详情
export function getOtherObject(params) {
  return request({
    url: '/base/otherObject/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
