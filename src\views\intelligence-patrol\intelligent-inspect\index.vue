<template>
  <div class="intelligence-inspect">
    <div class="left">
      <div class="left-top">
        <a-tabs default-active-key="1">
          <a-tab-pane key="1" tab="视频巡检"></a-tab-pane>
          <a-tab-pane key="2" tab="无人机巡检"></a-tab-pane>
        </a-tabs>
        <div style="color: #4e5969; margin-bottom: 6px">巡检时间</div>
        <a-range-picker
          allow-clear
          show-time
          :show-time="{ format: 'HH' }"
          format="YYYY-MM-DD HH"
          style="width: 100%"
          v-model="state.rangeDate"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </div>
      <div class="left-bottom">
        <div class="item" v-for="item in state.list" :key="item.taskId" @click="handleClickPlan(item)">
          <div class="item-top">
            <div class="icon"></div>
            <div class="name" :style="{ color: state.activePlan?.taskId === item.taskId ? '#3491FA' : '#4e5969' }">
              {{ item.taskName }}
            </div>
          </div>

          <div class="item-content">
            <div class="indicate">
              <div class="label">任务编码：</div>
              <div class="value">{{ item.taskCode }}</div>
            </div>
            <div class="indicate">
              <div class="label">开始时间：</div>
              <div class="value">{{ item.planStartTime }}</div>
            </div>
            <div class="indicate">
              <div class="label">视频点：</div>
              <div class="value">{{ item.lineObjectList?.length }}</div>
            </div>
            <div class="indicate">
              <div class="label">备注：</div>
              <div class="value">{{ item.remarks }}</div>
            </div>
          </div>
          <div class="item-bottom">
            <div style="display: flex; align-items: center; cursor: pointer" @click.stop="handleInspection(item)">
              <SvgIcon iconClass="intelligent-inspect-patrol" style="margin-right: 6px; font-size: 16px" />
              <a>一键巡检</a>
            </div>
          </div>
        </div>

        <a-empty v-if="state.list.length === 0" style="margin-top: 40px" />
      </div>
    </div>

    <div style="height: 100%; position: relative">
      <MapBox @onMapMounted="onMapMounted" />

      <div style="position: absolute; right: 16px; bottom: 16px">
        <MapStyle v-if="!!mapIns" :mapIns="mapIns" ref="mapStyleRef" />
        <Layers
          v-if="!!mapIns && !!state.activePlan"
          :geojson="state.activePlan.geojson"
          :mapIns="mapIns"
          :activeItem="state.activeItem"
        />
      </div>
    </div>

    <PatrolModal
      v-if="state.isShowPatrolModal"
      ref="patrolModalRef"
      :activeItem="state.activeItem"
      @close="handleClose"
      @stopTimer="stopTimer"
      @continuePlay="continuePlay"
      :eventTypeOptions="state.eventTypeOptions"
      :warnLevelOptions="state.warnLevelOptions"
    />
  </div>
</template>
<script setup>
  import MapBox from '@/components/MapBox/index.vue'
  import MapStyle from '@/components/MapBox/MapStyle.vue'
  import { getPatrolTaskList, getPatrolPlanDetails, startPatrolTask, endPatrolTask } from './services'
  import { getOptions } from '@/api/common'
  import { reactive, ref, shallowRef, onBeforeMount, onMounted, nextTick, watch, getCurrentInstance } from 'vue'
  import mapboxgl from 'mapbox-gl'
  import Layers from './components/Layers.vue'
  import PatrolModal from './components/PatrolModal.vue'
  import { mapBoundGeo } from '@/utils/mapBounds.js'
  import monitorIcon from '@/assets/images/monitor-icon.png'

  const colors = [
    '#3491FA',
    '#FAC859',
    '#0FC6C2',
    '#A871E3',
    '#91CB74',
    '#FC8452',
    '#6DC8EC',
    '#A871E3',
    '#1E9493',
    '#FF99C3',
  ]

  const vIns = getCurrentInstance()
  const patrolModalRef = ref(null)
  const mapIns = shallowRef(null)
  const state = reactive({
    eventTypeOptions: [],
    warnLevelOptions: [],

    rangeDate: [],
    list: [],
    activePlan: null,
    lineGeoJson: null,
    pointGeoJson: null,
    isShowPatrolModal: false,
    timer: null,
    activeItem: null,
    markerIns: null,

    activeIndex: 0,

    interval: 10000,
  })

  const onMapMounted = map => {
    mapIns.value = map

    getList()
  }

  onBeforeMount(() => {
    stopTimer()
  })

  watch(
    () => state.activeItem,
    newVal => {
      if (state.markerIns) {
        state.markerIns?.remove?.()
      }
      if (!newVal) return

      if (!newVal?.longitude || !newVal?.latitude) return

      mapIns.value.flyTo({
        center: [+newVal.longitude, +newVal.latitude],
        zoom: 12,
        duration: 1000,
      })

      const el = document.createElement('div')
      el.innerHTML = `
        <div style="width: 54px; height: 60px; background-size: 100%; background-image: url('${monitorIcon}');"></div>`

      state.markerIns = new mapboxgl.Marker({ element: el, anchor: 'bottom', offset: [0, 10] })
        .setLngLat([+newVal.longitude, +newVal.latitude])
        .addTo(mapIns.value)

      state.markerIns.getElement().style['z-index'] = '3'

      setTimeout(() => {
        state.isShowPatrolModal = true
        nextTick(() => {
          patrolModalRef.value.handleOpen(newVal)

          startTimer(state.activePlan.lineObjectList, state.interval, () => {
            stopTimer()
            state.isShowPatrolModal = false
          })
        })
      }, 1500)
    },
  )

  getOptions('eventType').then(res => {
    state.eventTypeOptions = res.data.map(item => ({ label: item.value, value: +item.key }))
  })

  getOptions('warnLevel').then(res => {
    state.warnLevelOptions = res.data.map(item => ({ label: item.value, value: +item.key }))
  })

  const getList = () => {
    getPatrolTaskList({
      status: [1,2],
      pageNum: 1,
      pageSize: Number.MAX_SAFE_INTEGER,
      startTime: state.rangeDate?.[0]?.format('YYYY-MM-DD HH:00:00'),
      endTime: state.rangeDate?.[1]?.format('YYYY-MM-DD HH:00:00'),
    }).then(res => {
      state.list = (res.data.data || []).map((el, index) => ({
        ...el,
        lineObjectList: el.lineObjectList?.map(dot => ({
          ...el,
          ...dot,
          longitude: dot.longitude === null ? null : +dot.longitude,
          latitude: dot.latitude === null ? null : +dot.latitude,
        })),
        geojson: {
          type: 'FeatureCollection',
          features: [
            ...el.lineObjectList
              ?.filter(ele => ele.longitude && ele.latitude)
              ?.map(dot => ({
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [+dot.longitude, +dot.latitude],
                },
                properties: {
                  ...dot,
                  icon: monitorIcon,
                },
              })),
            ...el.lineObjectList
              ?.filter(ele => ele.longitude && ele.latitude)
              ?.map(dot => ({
                type: 'Feature',
                geometry: {
                  type: 'LineString',
                  coordinates:
                    el.lineObjectList
                      ?.filter(ele => ele.longitude && ele.latitude)
                      ?.map(dot => [+dot.longitude, +dot.latitude]) || [],
                },
                properties: {
                  color: colors[index % colors.length],
                },
              })),
          ],
        },
      }))

      state.activePlan = state.list[0]

      mapBoundGeo(state.activePlan.geojson, mapIns.value, { top: 150, bottom: 150, left: 400, right: 100 })
    })
  }

  const handleClickPlan = item => {
    state.activePlan = item
    state.activeItem = null
    mapBoundGeo(item.geojson, mapIns.value, { top: 150, bottom: 150, left: 400, right: 100 })
  }

  const onRangeChange = value => {
    getList()
  }

  const handleInspection = item => {
    if (!!state.markerIns) return

    getPatrolPlanDetails({ planId: item.planId }).then(res => {
      startPatrolTask({ taskId: item.taskId }).then(res => {
        this.$message.success('巡检开始')
      })

      const planDetail = res.data
      // (1-秒 2-分 3-时)
      if (planDetail.cameraPeriodUnit === 1) {
        state.interval = planDetail.cameraPeriod * 1000
      } else if (planDetail.cameraPeriodUnit === 2) {
        state.interval = planDetail.cameraPeriod * 60 * 1000
      } else if (planDetail.cameraPeriodUnit === 3) {
        state.interval = planDetail.cameraPeriod * 60 * 60 * 1000
      }
      state.interval = 90000
      state.activeItem = item.lineObjectList[0]
    })
  }

  const handleClose = () => {
    state.isShowPatrolModal = false
    state.activeItem = null
    state.activeIndex = 0
    stopTimer()

    nextTick(() => {
      endPatrolTask({ taskId: state.activePlan.taskId }).then(res => {
        vIns.proxy.$message.success('巡检结束')
        state.getList()
      })
    })
  }

  // 继续播放函数
  const continuePlay = () => {
    state.activeItem = { ...state.activeItem }
  }

  // 有暂停和继续功能的定时器，根据一个list循环，开始就执行一次，间隔时间可以动态设置
  const startTimer = (list, interval = 1000, callback) => {
    state.timer = setInterval(() => {
      if (state.activeIndex >= list.length - 1) {
        handleClose()
        return
      }

      callback?.()
      state.activeIndex += 1
      state.activeItem = list[state.activeIndex]
    }, interval)
  }

  const stopTimer = () => {
    clearInterval(state.timer)
    state.timer = null
  }
</script>
<style lang="less" scoped>
  .intelligence-inspect {
    height: calc(100vh - 120px);
    background-color: #fff;
    border-radius: 10px;
    padding: 10px;
    position: relative;

    .left {
      position: absolute;
      z-index: 100;
      top: 0;
      left: 0;
      width: 300px;
      height: 100%;
      display: flex;
      flex-direction: column;
      .left-top {
        background-color: #fff;
        border-radius: 0px 0px 8px 0px;
        padding: 10px 10px 20px;
        margin-bottom: 10px;
      }
      .left-bottom {
        flex: 1;
        background-color: #fff;
        border-radius: 0px 8px 0px 0px;
        padding: 10px 10px 20px;
        margin-bottom: 10px;
        padding: 10px 0;
        overflow-y: auto;
        .item {
          padding: 10px;
          border-bottom: 1px solid #f0f0f0;
          &:last-child {
            border-bottom: none;
          }

          .item-top {
            display: flex;
            align-items: center;
            .icon {
              width: 40px;
              height: 40px;
              background: url('@/assets/images/intelligent-inspect.png') no-repeat center / 100% 100%;
            }
            .name {
              margin-left: 10px;
              font-size: 16px;
              font-weight: 500;
              color: #1d2129;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .item-content {
            padding: 10px;
            background: #f5f9ff;
            border-radius: 4px 4px 4px 4px;
            .indicate {
              display: flex;
              align-items: center;
              .label {
              }
            }
          }
          .item-bottom {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-top: 5px;
            padding: 10px;
            user-select: none;
          }
        }
      }
    }
  }
</style>
