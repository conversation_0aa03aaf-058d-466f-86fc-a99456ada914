<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="简报名称">
        <a-input v-model="queryParam.reportName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="创建人">
        <a-select
          show-search
          placeholder="请输入"
          v-model="queryParam.createdUserId"
          option-filter-prop="children"
          :filter-option="filterOptionUser"
          @change="handleChangeUser"
        >
          <a-select-option v-for="item in createPersonList" :key="item.userId" :value="item.userId">
            {{ item.name }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="生效时间">
        <a-range-picker
          allow-clear
          :value="takeEffect"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormPlan v-if="showFormPlan" ref="formPlanRef" @ok="onOperationComplete" @close="showFormPlan = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getComUserList } from '@/api/common'
  import { getPlanList, cancelPlan, enablePlan, deactivatePlan, deletePlan, triggerOncePlan } from './services'
  import FormPlan from './modules/FormPlan.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'PatrolPlan',
    components: {
      VxeTable,
      VxeTableForm,
      FormPlan,
    },
    data() {
      return {
        createPersonList: [],
        showFormPlan: false,
        takeEffect: [],
        list: [],
        tableTitle: '计划配置',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          createdUserId: null,
          endTime: null,
          pageNum: 1,
          pageSize: 10,
          reportName: null,
          startTime: null,
        },
        columns: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '简报名称',
            field: 'reportName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '是否循环',
            field: 'isInfinite',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.isInfinite == 1 ? '是' : '否'
              },
            },
          },
          {
            title: '创建人',
            field: 'createdUserName',
            minWidth: 100,
          },
          {
            title: '下次执行时间',
            field: 'nextGenTime',
            minWidth: 170,
          },
          {
            title: '开始时间',
            field: 'genStartDate',
            minWidth: 170,
          },
          {
            title: '结束时间',
            field: 'genEndDate',
            minWidth: 170,
          },
          {
            title: '任务状态',
            field: 'status', //状态(1-启用 2-停用 9-已作废)
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                if (row.status === 9) {
                  return <span>已作废</span>
                } else {
                  return (
                    <a-switch
                      size='small'
                      checked-children='启用'
                      un-checked-children='停用'
                      v-model={row.status}
                      onClick={() => this.switchChange(row.status, row.reportConfigId)}
                    ></a-switch>
                  )
                }
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 220,
            slots: {
              default: ({ row }) => {
                if (row.status == true) {
                  return (
                    <span>
                      <a onClick={() => this.handleTriggerOnce(row.reportConfigId)}>执行一次</a>
                      <a-divider type='vertical' />
                      <a onClick={() => this.handleCancel(row.reportConfigId)}>作废</a>
                      <a-divider type='vertical' />
                      <a onClick={() => this.handleDelete(row)}>删除</a>
                    </span>
                  )
                } else if (row.status == false) {
                  return (
                    <span>
                      <a onClick={() => this.handleEdit(row)}>修改</a>
                      <a-divider type='vertical' />
                      <a onClick={() => this.handleTriggerOnce(row.reportConfigId)}>执行一次</a>
                      <a-divider type='vertical' />
                      <a onClick={() => this.handleCancel(row.reportConfigId)}>作废</a>
                      <a-divider type='vertical' />
                      <a onClick={() => this.handleDelete(row)}>删除</a>
                    </span>
                  )
                } else if (row.status === 9) {
                  return (
                    <span>
                      <a onClick={() => this.handleDelete(row)}>删除</a>
                    </span>
                  )
                }
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {
      getComUserList({}).then(res => {
        if (res?.code == 200) {
          this.createPersonList = res?.data
        }
      })
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.startTime = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') + ' 00:00:00' : null
        this.queryParam.endTime = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') + ' 23:59:59' : null
      },
      /** 查询列表 */
      getList() {
        this.showFormPlan = false
        this.loading = true
        getPlanList(this.queryParam).then(response => {
          if (response?.code == 200) {
            this.loading = false
            this.list = response?.data?.data
            this.list.forEach(item => {
              if (item.status == 1) {
                item.status = true
              } else if (item.status == 2) {
                item.status = false
              }
            })
            this.total = response?.data?.total
          }
        })
      },
      handleChangeUser(value) {
        this.queryParam.createdUserId = value
      },
      filterOptionUser(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.reportConfigId)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          createdUserId: null,
          endTime: null,
          pageNum: 1,
          reportName: null,
          startTime: null,
        }
        this.takeEffect = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /* 新增 */
      handleAdd() {
        this.showFormPlan = true
        this.$nextTick(() => this.$refs.formPlanRef.handlePlan())
      },
      /* 修改 */
      handleEdit(record) {
        this.showFormPlan = true
        this.$nextTick(() => this.$refs.formPlanRef.handlePlan(record))
      },
      handleTriggerOnce(id) {
        var that = this
        this.$confirm({
          title: '确认执行一次',
          content: '请确认是否要执行一次',
          onOk() {
            triggerOncePlan({ reportConfigId: id }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功`, 3)
                that.onOperationComplete()
              }
            })
          },
        })
      },
      //作废(1-启用 2-停用 9-已作废)
      handleCancel(id) {
        var that = this
        this.$confirm({
          title: '确认作废',
          content: '请确认是否要作废',
          onOk() {
            cancelPlan({ reportConfigId: id }).then(res => {
              if (res.code == 200) {
                that.$message.success(`作废成功`, 3)
                that.onOperationComplete()
              }
            })
          },
        })
      },
      switchChange(status, id) {
        if (!status) {
          deactivatePlan({ reportConfigId: id }).then(res => {
            if (res.code == 200) {
              this.$message.success(`停用成功`, 3)
              this.handleQuery()
            }
          })
        } else {
          enablePlan({ reportConfigId: id }).then(res => {
            if (res.code == 200) {
              this.$message.success(`启用成功`, 3)
              this.handleQuery()
            }
          })
        }
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const planIds = row?.reportConfigId ? [row?.reportConfigId] : this.selectIds

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deletePlan({ reportConfigIds: planIds.join(',') }).then(res => {
              that.$message.success(`删除成功`, 3)
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.queryParam.pageNum = 1
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
