<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="660"
  >
    <div slot="content">
      <a-row :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">事件名称：</label>
            <span class="common-value-text">{{ data.eventName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">异常点名称：</label>
            <span class="common-value-text">{{ data.cameraName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">事件类型：</label>
            <span class="common-value-text">{{ eventTypes[data.eventType]?.value || '-' }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">上报人：</label>
            <span class="common-value-text">{{ data.createdUserName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">上报时间：</label>
            <span class="common-value-text">{{ data.createdTime }}</span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">详细位置：</label>
            <span class="common-value-text">{{ data.location }}</span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">事件描述：</label>
            <span class="common-value-text">{{ data.content }}</span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">附件：</label>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div style="margin-bottom: 20px">
            <div class="file-item" v-for="(el, i) in data?.evenAttachList" :key="i" @click="() => downLoad(el)">
              <a-icon type="paper-clip" />
              <div class="file-name" :title="el" style="margin-left: 5px">{{ el }}</div>
            </div>
          </div>
        </a-col>
        <div v-if="data?.status !== 1">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">处理结果</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">处理状态：</label>
              <span class="common-value-text">{{ eventTypes[data.status]?.value || '' }}</span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">处置时间：</label>
              <span class="common-value-text">{{ data.disposeDate }}</span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">处置人：</label>
              <span class="common-value-text">{{ data.disposeUserName }}</span>
            </div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="item">
              <label class="common-label-text">处置方案：</label>
              <span class="common-value-text">{{ data.disposalConcept }}</span>
            </div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="item">
              <label class="common-label-text">附件：</label>
            </div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div style="margin-bottom: 20px">
              <div class="file-item" v-for="(el, i) in data?.disposalAttachList" :key="i" @click="() => downLoad(el)">
                <a-icon type="paper-clip" />
                <div class="file-name" :title="el" style="margin-left: 5px">{{ el }}</div>
              </div>
            </div>
          </a-col>
        </div>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getEventDetail } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'DetailModal',
    props: ['eventStatus', 'eventTypes'],
    components: { AntModal },
    data() {
      return {
        open: false,
        modalTitle: '',
        modalLoading: false,
        data: {},
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 查看按钮操作 */
      handleDetail(record) {
        this.open = true
        this.modalTitle = '查看'
        // console.log('查看详情 111111111', record)
        this.modalLoading = true
        getEventDetail({ evenId: record.eventId }).then(res => {
          this.data = res.data
          this.modalLoading = false
        })
      },
      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
