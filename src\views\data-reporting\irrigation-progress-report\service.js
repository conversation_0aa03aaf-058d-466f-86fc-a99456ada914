import request from '@/utils/request'

//灌溉进度填报

//新增
export function addIrrigationSetting(data) {
    return request({
        url: '/custom/irrigationProgress/add',
        method: 'post',
        data
    })
}

//删除
export function deleteIrrigationSetting(id) {
    return request({
        url: '/custom/irrigationProgress/delete',
        method: 'post',
        params: { id }
    })
}

//详情
export function getIrrigationSettingById(id) {
    return request({
        url: '/custom/irrigationProgress/get',
        method: 'post',
        params: { id }
    })
}


//修改
export function updateIrrigationSetting(data) {
    return request({
        url: '/custom/irrigationProgress/update',
        method: 'post',
        data
    })
}

//分页
export function getIrrigationSettingPage(data) {
    return request({
        url: '/custom/irrigationProgress/page',
        method: 'post',
        data
    })
}

//查部门树
export function getDeptTree() {
    return request({
        url: '/sys/dept/tree',
        method: 'post',
        data: { 
            "deptName": "",
            "parentId": 0,
            "remark": "",
            "sort": 0,
            "type": null
         },
    })
}

// 获取灌溉轮次
export function getIrrigationRound(endDate,startDate) {
    return request({
        url: '/custom/irrigationSetting/getIrrigationRound',
        method: 'post',
        params: { endDate,startDate }
    })
}