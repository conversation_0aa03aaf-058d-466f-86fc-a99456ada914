<template>
  <div style="display: flex; align-items: center">
    <div :class="`btn ${isPlay ? 'btn-pause' : 'btn-play'}`" @click="onPlay"></div>
    <a-slider
      id="time-slider"
      v-model="slideVal"
      :min="0"
      :max="times.length - 1"
      :tipFormatter="tipFormatter"
      :tooltipVisible="true"
      style="flex: 1"
      @change="onChange"
      :getTooltipPopupContainer="getTooltipPopupContainer"
    />
    <a-dropdown placement="topCenter" size="small">
      <div
        style="
          cursor: pointer;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          font-size: 12px;
          padding: 0 4px;
          margin-left: 8px;
          color: #000026;
        "
      >
        {{ speed }}
      </div>
      <a-menu slot="overlay">
        <a-menu-item>
          <a style="padding: 2px 16px" @click="onSpeedChange('X1')">X1</a>
        </a-menu-item>
        <a-menu-item>
          <a style="padding: 2px 16px" @click="onSpeedChange('X2')">X2</a>
        </a-menu-item>
      </a-menu>
    </a-dropdown>
  </div>
</template>

<script>
  import * as _ from 'lodash'

  export default {
    name: 'TimePlaySlider',
    props: {
      times: {
        default: () => [],
      },
    },
    components: {},
    data() {
      return {
        isPlay: false,
        slideVal: 0,
        timer: null,
        interval: 1000,
        speed: 'x1',
      }
    },
    computed: {},
    mounted() {
      this.$emit('onTimeChange', this.times[this.slideVal])
    },
    beforeDestroy() {
      if (this.timer) {
        clearInterval(this.timer)
      }
    },
    methods: {
      getTooltipPopupContainer() {
        return document.getElementById('time-slider')
      },
      onPlay() {
        if (!this.isPlay) {
          this.isPlay = true

          this.timer = setInterval(() => {
            if (this.slideVal === this.times.length - 1) {
              this.isPlay = false
              this.slideVal = 0
              clearInterval(this.timer)
              this.timer = null
            } else {
              this.slideVal += 1
            }
            this.$emit('onTimeChange', this.times[this.slideVal])
          }, this.interval)
        } else {
          clearInterval(this.timer)
          this.timer = null
          this.isPlay = false
        }
      },
      onChange: _.debounce(function (val) {
        this.slideVal = val
        this.$emit('onTimeChange', this.times[this.slideVal])
      }, 500),

      onSpeedChange(val) {
        switch (val) {
          case 'X1':
            this.interval = 1000
            break
          case 'X2':
            this.interval = 500
            break
          default:
            break
        }

        this.speed = val
        if (this.timer) {
          clearInterval(this.timer)
          this.timer = null
          this.isPlay = false
          this.isPlay = false
          this.onPlay()
        }
      },
      tipFormatter(index) {
        return this.times[index]
      },
    },
  }
</script>

<style lang="less" scoped>
  .btn-play {
    background: url('~@/assets/images/time-slide-paly.png') no-repeat center / 100% 100%;
  }

  .btn-pause {
    background: url('~@/assets/images/time-slide-pause.png') no-repeat center / 100% 100%;
  }

  .btn {
    margin-right: 8px;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
</style>
