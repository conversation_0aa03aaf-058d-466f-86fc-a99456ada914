<template>
  <ant-modal
    :visible="open"
    modal-title="生成时间预测"
    :loading="modalLoading"
    modalWidth="400"
    @cancel="cancel"
    modalHeight="480"
  >
    <div slot="content">
      <div v-if="generateTimeList?.length > 0">
        <div
          style="margin-top: 10px; margin-bottom: 10px; display: flex"
          v-for="(item, index) of generateTimeList"
          key="index"
        >
          <div style="display: inline-block; color: #1890ff">第{{ index + 1 }}次计划执行时间：</div>
          <div style="display: inline-block; margin-left: 10px">{{ item.execTime }} {{ item.dayOfWeek }}</div>
        </div>
      </div>
      <div v-else>暂无数据</div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { generateTimeForecastApi } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'GenerateTime',
    components: { AntModal },
    data() {
      return {
        modalLoading: false,
        open: false,
        generateTimeList: [],
      }
    },
    methods: {
      moment,
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleShowTime(formData) {
        let timeForm = {
          genEndDate: formData.genEndDate,
          genFrequency: formData.genFrequency,
          genStartDate: formData.genStartDate,
          genTime: formData.genTime,
          isInfinite: formData.isInfinite,
          reportName: formData.reportName,
          reportTemplateId: formData.reportTemplateId,
        }
        this.open = true
        this.modalLoading = true
        generateTimeForecastApi(timeForm).then(res => {
          this.modalLoading = false
          if (res.code == 200) {
            this.generateTimeList = res.data
          } else {
            this.$message.error(res.message)
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  .modal-content {
    overflow-x: auto;
    .form-item {
      display: flex;
      label {
        white-space: nowrap;
        line-height: 30px;
      }
    }
    .date-picker {
      margin-bottom: 10px;
    }
  }
</style>
