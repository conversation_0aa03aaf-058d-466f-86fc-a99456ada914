import request from '@/utils/request'

// 水利对象分类-获取树
export function getObjectCategoryTree(params) {
  return request({
    url: '/base/objectCategory/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水利对象分类-列表分页查询
export function getObjectCategoryPage(data) {
  return request({
    url: '/base/objectCategory/page',
    method: 'post',
    data,
  })
}

// 水利对象分类-获取父节点树
export function getObjectCategoryParentTree(params) {
  return request({
    url: '/base/objectCategory/getParentTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水利对象分类-获取排序号
export function getObjectCategoryNextSort(params) {
  return request({
    url: '/base/objectCategory/getNextSort',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 水利对象分类-新增
export function addObjectCategory(data) {
  return request({
    url: '/base/objectCategory/add',
    method: 'post',
    data,
  })
}

// 水利对象分类-更新
export function updateObjectCategory(data) {
  return request({
    url: '/base/objectCategory/update',
    method: 'post',
    data,
  })
}

// 水利对象分类-删除
export function deleteObjectCategory(params) {
  return request({
    url: '/base/objectCategory/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水利对象分类-详情
export function getObjectCategoryDetail(params) {
  return request({
    url: '/base/objectCategory/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取所有图层
export function getGeoserver() {
  return request({
    url: '/external/geoserver/layers',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

//监测指标 详情
export function getObjectIndex(params) {
  return request({
    url: '/base/objectCategory/index/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//监测指标 新增
export function addObjectIndex(data) {
  return request({
    url: '/base/objectCategory/index/add',
    method: 'post',
    data,
  })
}
//监测指标 删除
export function deleteObjectIndex(params) {
  return request({
    url: '/base/objectCategory/index/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
