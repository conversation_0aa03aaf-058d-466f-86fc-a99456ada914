<template>
  <div class="irrigation-section">
    <div class="section-header">
      <h3 class="section-title">{{ title }}</h3>
    </div>
    <vxe-table
      :ref="tableRef"
      :data="displayTableData"
      border
      stripe
      :row-config="{ keyField: 'id' }"
      :column-config="{ resizable: true }"
      height="350"
      class="irrigation-table"
      width="100%"
      :footer-data="computedFooterData"
      :show-footer="showFooter"
      :empty-text="'暂无数据'"
      header-align="center"
      align="center"
    >
      <!-- 渠道列 -->
      <vxe-column field="channel" title="渠道\作物" width="120">
        <template #default="{ row }">
          <span>
            {{ row.channel }}
          </span>
        </template>
      </vxe-column>

      <!-- 动态渲染表头 -->
      <template v-for="column in tableColumns" >
        <!-- 分组列 -->
        <vxe-colgroup v-if="column.type === 'group'" :title="column.title">
          <template v-for="subColumn in column.children" >
            <!-- 子分组列 -->
            <vxe-colgroup v-if="subColumn.type === 'group'" :title="subColumn.title">
              <template v-for="subSubColumn in subColumn.children" >
                <vxe-column 
                  v-if="subSubColumn.field"
                  :field="subSubColumn.field" 
                  :title="subSubColumn.title" 
                  :width="subSubColumn.width"
                >
                  <template #default="{ row }">
                    <span v-if="subSubColumn.isSubtotal" :style="{ fontWeight: 'bold', color: '#1890ff' }">
                      {{ formatNumber(row[subSubColumn.field]) }}
                    </span>
                    <span v-else>
                      {{ formatNumber(row[subSubColumn.field]) }}
                    </span>
                  </template>
                </vxe-column>
              </template>
            </vxe-colgroup>
            <!-- 普通子列 -->
            <vxe-column 
              v-else-if="subColumn.field"
              :field="subColumn.field" 
              :title="subColumn.title" 
              :width="subColumn.width"
            >
              <template #default="{ row }">
                <span v-if="subColumn.isSubtotal" :style="{ fontWeight: 'bold', color: '#52c41a' }">
                  {{ formatNumber(row[subColumn.field]) }}
                </span>
                <span v-else>
                  {{ formatNumber(row[subColumn.field]) }}
                </span>
              </template>
            </vxe-column>
          </template>
        </vxe-colgroup>
        <!-- 普通列 -->
        <vxe-column 
          v-else
          :field="column.field" 
          :title="column.title" 
          :width="column.width"
        >
          <template #default="{ row }">
            <span v-if="column.isTotal" :style="{ fontWeight: 'bold', color: '#52c41a' }">
              {{ formatNumber(row[column.field]) }}
            </span>
            <span v-else>
              {{ formatNumber(row[column.field]) }}
            </span>
          </template>
        </vxe-column>
      </template>
    </vxe-table>
  </div>
</template>

<script>
import { plantingCrop, getLeafCropsWithFieldNames } from '@/enum/planting-crop'

export default {
  name: 'IrrigationPlantingDetailTable',
  props: {
    title: {
      type: String,
      required: true
    },
    tableData: {
      type: Array,
      default: () => []
    },
    tableRef: {
      type: String,
      default: 'tableRef'
    }
  },
  data() {
    return {
      // 作物分类数据
      plantingCropCategories: [],
      // 表格列配置
      tableColumns: []
    }
  },
  computed: {
    // 显示的表格数据
    displayTableData() {
      return this.tableData
    },
    // 计算合计值
    computedFooterData() {
      // 如果表格数据为空，返回空数组
      if (this.tableData.length === 0) {
        return []
      }
      
      const totalRow = { channel: '合计' }
      
      // 获取所有需要计算合计的字段
      const leafCrops = getLeafCropsWithFieldNames()
      const fields = leafCrops.map(crop => crop.fieldName).filter(Boolean)
      
      // 添加其他字段
      fields.push('farmlandArea', 'totalIrrigationArea')
      
      // 添加小计字段
      this.plantingCropCategories.forEach(category => {
        const subtotalField = this.getSubtotalFieldName(category)
        if (subtotalField) {
          fields.push(subtotalField)
        }
        
        if (category.children) {
          category.children.forEach(subCategory => {
            if (subCategory.children && subCategory.children.length > 0) {
              const subSubtotalField = this.getSubtotalFieldName(subCategory)
              if (subSubtotalField) {
                fields.push(subSubtotalField)
              }
            }
          })
        }
      })

      fields.forEach(field => {
        let total = 0
        this.tableData.forEach(row => {
          total += parseFloat(row[field]) || 0
        })
        totalRow[field] = total > 0 ? total.toFixed(2) : null
      })

      return [totalRow]
    },
    showFooter() {
      return this.tableData.length > 0
    }
  },
  created() {
    this.initPlantingCropCategories()
    this.initTableColumns()
  },
  methods: {
    // 初始化表格列配置
    initTableColumns() {
      const columns = []
      
      // 动态生成作物分类列（排除热水地和干地）
      this.plantingCropCategories.forEach(category => {
        if (category.children) {
          // 有子分类的情况，创建分组列
          const groupColumn = {
            type: 'group',
            title: category.label,
            children: []
          }
          
          category.children.forEach(subCategory => {
            if (subCategory.children && subCategory.children.length > 0) {
              // 有三级分类，创建子分组
              const subGroupColumn = {
                type: 'group',
                title: subCategory.label,
                children: []
              }
              
              // 添加三级分类的列
              subCategory.children.forEach(crop => {
                if (crop.fieldName) {
                  subGroupColumn.children.push({
                    field: crop.fieldName,
                    title: crop.label,
                    width: 70,
                    cropInfo: crop
                  })
                }
              })
              
              // 添加子分组小计列
              const subSubtotalField = this.getSubtotalFieldName(subCategory)
              if (subSubtotalField) {
                subGroupColumn.children.push({
                  field: subSubtotalField,
                  title: '小计',
                  width: 70,
                  isSubtotal: true
                })
              }
              
              groupColumn.children.push(subGroupColumn)
            } else if (subCategory.fieldName) {
              // 没有三级分类，直接添加为列
              groupColumn.children.push({
                field: subCategory.fieldName,
                title: subCategory.label,
                width: 80,
                cropInfo: subCategory
              })
            }
          })
          
          // 添加大分类小计列
          const subtotalField = this.getSubtotalFieldName(category)
          if (subtotalField) {
            groupColumn.children.push({
              field: subtotalField,
              title: '小计',
              width: 80,
              isSubtotal: true
            })
          }
          
          columns.push(groupColumn)
          
          // 在秋季作物后添加耕地面积列
          if (category.value === 'AUTUMN_CROPS') {
            columns.push({
              field: 'farmlandArea',
              title: '耕地面积',
              width: 100
            })
          }
        }
      })
      
      // 添加总灌溉面积列
      columns.push({
        field: 'totalIrrigationArea',
        title: '总灌溉面积',
        width: 120,
        isTotal: true
      })
      
      // 添加热水地和干地列（放在最右侧）
      const hotWaterLand = plantingCrop.find(crop => crop.value === 'HOT_WATER_LAND')
      const dryLand = plantingCrop.find(crop => crop.value === 'DRY_LAND')
      
      if (hotWaterLand && hotWaterLand.fieldName) {
        columns.push({
          field: hotWaterLand.fieldName,
          title: hotWaterLand.label,
          width: 100,
          cropInfo: hotWaterLand
        })
      }
      
      if (dryLand && dryLand.fieldName) {
        columns.push({
          field: dryLand.fieldName,
          title: dryLand.label,
          width: 100,
          cropInfo: dryLand
        })
      }
      
      this.tableColumns = columns
    },

    // 初始化作物分类数据
    initPlantingCropCategories() {
      // 过滤出有子分类的作物（排除热水地和干地）
      this.plantingCropCategories = plantingCrop.filter(category => 
        !['HOT_WATER_LAND', 'DRY_LAND'].includes(category.value)
      )
    },

    // 获取小计字段名
    getSubtotalFieldName(category) {
      const subtotalMap = {
        'SUMMER_CROPS': 'summerCropsSubtotal',
        'AUTUMN_CROPS': 'autumnCropsSubtotal',
        'FOREST_PASTURE': 'forestGrasslandSubtotal',
        'MELON_VEGETABLES': 'melonVegetableSubtotal',
        'WHEAT_CATEGORY': 'wheatIntercroppingSubtotal',
        'FOREST_LAND': 'forestlandSubtotal'
      }
      return subtotalMap[category.value] || null
    },

    // 格式化数字显示
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '-'
      }
      const num = parseFloat(value)
      if (isNaN(num) || num === 0) {
        return '-'
      }
      return num.toFixed(2)
    }
  }
}
</script>

<style lang="less" scoped>
.irrigation-section {
  margin-bottom: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

  .irrigation-table {
    /deep/ .vxe-header--column {
      font-weight: bold;
    }

    /deep/ .vxe-cell {
      padding: 2px 4px;
    }

    /deep/ .vxe-table--border-line {
      border-color: #e8e8e8;
    }
    
    /deep/ .vxe-header--column {
      background-color: #fafafa;
      font-weight: 600;
      font-size: 12px;
    }
    
    /deep/ .vxe-body--row {
      &:nth-child(even) {
        background-color: #fafafa;
      }
    }
    
    /deep/ .vxe-table {
      width: 100% !important;
      font-size: 12px;
    }
    
    /deep/ .vxe-cell {
      padding: 4px 8px;
    }
    
    // 空数据状态样式
    /deep/ .vxe-table--empty-block {
      padding: 40px 0;
      color: #999;
      font-size: 14px;
    }
  }
}
</style> 