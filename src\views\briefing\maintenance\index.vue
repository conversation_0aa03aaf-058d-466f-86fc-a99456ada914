<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="发布项名称">
        <a-input
          v-model="queryParam.reportItemName"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>
      <a-form-item label="类型">
        <a-select v-model="queryParam.reportItemType" allow-clear @keyup.enter.native="handleQuery">
          <a-select-option v-for="(el, i) in briefingTypeList" :key="i" :value="el.key">
            {{ el.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormMaintenance
          v-if="showFormMaintenance"
          ref="formMaintenanceRef"
          @ok="onOperationComplete"
          @close="showFormMaintenance = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getMaintenanceList, deleteMaintenance } from './services'
  import FormMaintenance from './modules/FormMaintenance.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'maintenance',
    components: {
      VxeTable,
      VxeTableForm,
      FormMaintenance,
    },
    data() {
      return {
        showFormMaintenance: false,
        createValue: [],
        configTreeTypes: [],
        configTreeOptions: [],
        briefingTypeList: [],
        list: [],
        tableTitle: '发布项维护',
        isChecked: false,
        selectIds: [],
        names: [],
        loading: false,
        total: 0,

        workShiftList: [],
        workGroupList: [],
        lineOptions: [],
        queryParam: {
          reportItemName: null,
          reportItemType: null,
          pageNum: 1,
          pageSize: 10,
        },
        columns: [
          { type: 'checkbox', width: 40 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '发布项名称',
            field: 'reportItemName',
          },
          {
            title: '类型',
            slots: {
              default: ({ row }) => {
                return this.getTypeNameById(row.reportItemType)
              },
            },
          },
          {
            title: '是否包含表格',
            slots: {
              default: ({ row }) => {
                return this.isHaveTerm(row.isIncludeTable)
              },
            },
          },
          {
            title: '是否包含图表',
            slots: {
              default: ({ row }) => {
                return this.isHaveTerm(row.isIncludeChart)
              },
            },
          },
          {
            title: '创建时间',
            field: 'createdTime',
          },
          {
            title: '操作',
            field: 'operate',
            width: 128,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleParamCopy(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete('one', row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      getOptions('publicationItem').then(res => {
        this.briefingTypeList = res?.data || []
      })
      this.getList()
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showFormMaintenance = false
        this.loading = true
        getMaintenanceList(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.list.forEach(item => {
            item.isStopped = item.isStopped == 0 ? true : false
            return item
          })
          this.total = response?.data?.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.reportItemId)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          reportItemName: null,
          reportItemType: null,
          pageNum: 1,
        }
        this.createValue = []
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showFormMaintenance = true
        this.$nextTick(() => this.$refs.formMaintenanceRef.handleMaintenance())
      },
      /* 修改 */
      handleParamCopy(record) {
        this.showFormMaintenance = true
        this.$nextTick(() => this.$refs.formMaintenanceRef.handleMaintenance(record))
      },
      /** 删除按钮操作 */
      handleDelete(type, row) {
        let ids
        if (type == 'one') {
          ids = row.reportItemId
        } else {
          ids = this.selectIds.join(',')
        }
        var that = this
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteMaintenance({ reportItemIds: ids }).then(res => {
              if (res.code == 200) {
                that.$message.success(`删除成功`, 3)
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
      getTypeNameById(type) {
        let row = this.briefingTypeList.find(item => item.key == type)
        return row.value
      },
      isHaveTerm(val) {
        return val ? '是' : '否'
      },
    },
  }
</script>
<style lang="less" scoped></style>
