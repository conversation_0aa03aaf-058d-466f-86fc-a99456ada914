<template>
  <ant-modal 
    :visible="open" 
    modal-title="导入数据" 
    :loading="modalLoading" 
    modalWidth="600" 
    @cancel="cancel"
    modalHeight="400"
  >
    <div slot="content">
      <div class="import-content">
        <a-upload
          :file-list="fileList"
          :before-upload="beforeUpload"
          :remove="handleRemove"
          accept=".xlsx,.xls"
        >
          <a-button>
            <a-icon type="upload" />
            选择文件
          </a-button>
        </a-upload>
        
        <div class="import-tips">
          <p>导入说明：</p>
          <ul>
            <li>支持Excel格式文件(.xlsx, .xls)</li>
            <li>请按照模板格式填写数据</li>
            <li>导入前请先下载模板文件</li>
          </ul>
        </div>
      </div>
    </div>
    
    <div slot="footer">
      <a-button @click="downloadTemplate">下载模板</a-button>
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="handleImport" :loading="modalLoading">确认导入</a-button>
    </div>
  </ant-modal>
</template>

<script>
import AntModal from '@/components/pt/dialog/AntModal'

export default {
  name: 'ImportModal',
  components: { AntModal },
  data() {
    return {
      open: false,
      modalLoading: false,
      fileList: []
    }
  },
  methods: {
    show() {
      this.open = true
      this.fileList = []
    },
    
    cancel() {
      this.open = false
      this.fileList = []
    },
    
    beforeUpload(file) {
      this.fileList = [file]
      return false // 阻止自动上传
    },
    
    handleRemove() {
      this.fileList = []
    },
    
    downloadTemplate() {
      this.$message.info('模板下载功能待实现')
    },
    
    async handleImport() {
      if (this.fileList.length === 0) {
        this.$message.error('请选择要导入的文件')
        return
      }
      
      this.modalLoading = true
      
      try {
        // 这里实现导入逻辑
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟导入
        
        this.$message.success('导入成功')
        this.open = false
        this.fileList = []
        this.$emit('ok')
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('导入失败')
      } finally {
        this.modalLoading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.import-content {
  .import-tips {
    margin-top: 20px;
    padding: 16px;
    background-color: #f6f8fa;
    border-radius: 4px;
    
    p {
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
        color: #666;
      }
    }
  }
}
</style> 