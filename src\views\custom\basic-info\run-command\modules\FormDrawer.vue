<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="550"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工作票名称" prop="cmdName">
              <a-input v-model="form.cmdName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="调度方案" prop="dispatchId">
              <a-select
                show-search
                allow-clear
                v-model="form.dispatchId"
                :options="dispatchOptions"
                placeholder="请选择"
                option-filter-prop="children"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工程" prop="projectId">
              <a-select
                show-search
                v-model="form.projectId"
                :options="projectOptions"
                placeholder="请选择"
                @change="handleProjectChange"
                option-filter-prop="children"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工作负责人" prop="wardUserIds">
              <a-select
                show-search
                allowClear
                v-model="form.wardUserIds"
                :options="userOptions"
                placeholder="请选择"
                option-filter-prop="children"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="工作人员" prop="workUserIds">
              <a-select
                show-search
                allowClear
                mode="multiple"
                v-model="form.workUserIds"
                :options="userOptions"
                placeholder="请选择"
                option-filter-prop="children"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="计划工作时间" prop="planTime">
              <a-range-picker
                allow-clear
                showTime
                format="YYYY-MM-DD HH:mm"
                valueFormat="YYYY-MM-DD HH:mm"
                :placeholder="['开始时间', '结束时间']"
                v-model="form.planTime"
                @change="onRangeChange"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div class="title">工作内容</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" v-if="deviceList?.length > 0">
            <div class="checkbox-container">
              <a-checkbox-group v-model="deviceSelect" @change="deviceOnChange">
                <a-checkbox :value="item.deviceId" v-for="(item, index) in deviceList" :key="item.deviceId">
                  {{ item.deviceName }}
                </a-checkbox>
              </a-checkbox-group>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" v-for="(item, index) in deviceOptions" :key="item.deviceId">
            <a-form-model-item :label="`${item.deviceName}开启时间范围`">
              <a-range-picker
                allow-clear
                showTime
                format="YYYY-MM-DD HH:mm"
                valueFormat="YYYY-MM-DD HH:mm"
                :placeholder="['开始时间', '结束时间']"
                v-model="item.planTime"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" v-if="!deviceOptions || deviceOptions.length === 0 || !deviceList">
            <span class="common-value-text">暂无数据</span>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="注意事项(安全措施)" prop="items">
              <a-textarea v-model="form.items" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addRunmd, editRunmd, getRunmdById, getDeviceByProjectId } from '../services'
  import { getDispatchProjectList, getUserList } from '../../dispatch-project/services'
  import { getDispatchPage } from '../../dispatch-case/services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal },
    props: ['dispatchOptions'],
    data() {
      return {
        deviceSelect: [],
        deviceList: [],
        loading: false,
        modalLoading: false,
        projectOptions: [],
        userOptions: [],
        deviceOptions: [],
        formTitle: '',
        form: {
          cmdName: '',
          planEndDate: null,
          planStartDate: null,
          projectId: null,
          wardUserIds: null,
          workUserIds: [],
          items: '',
        },
        open: false,
        rules: {
          cmdName: [{ required: true, message: '工作票名称不能为空', trigger: 'blur' }],
          projectId: [{ required: true, message: '工程能为空', trigger: 'change' }],
          wardUserIds: [{ required: true, message: '工作负责人不能为空', trigger: 'change' }],
          workUserIds: [{ required: true, message: '工作人员不能为空', trigger: 'change' }],
          planTime: [{ required: true, message: '计划工作时间不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    watch: {
      deviceList: {
        immediate: true,
        handler(val) {
          console.log('**** 168 change list', val)

          this.deviceOptions = this.deviceList?.filter(item => item.active === true)
        },
      },
    },
    methods: {
      onRangeChange(dates) {
        this.form.planStartDate = dates[0]
        this.form.planEndDate = dates[1]
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      deviceOnChange(val) {
        this.deviceSelect = val
        this.deviceList = this.deviceList?.map(item => {
          // 如果 deviceId 存在于 deviceIds 数组中，则将 active 设置为 true
          if (this.deviceSelect?.includes(item.deviceId)) {
            return { ...item, active: true }
          }
          // 否则保持原样
          return { ...item, active: false }
        })
        console.log('**** 191 change list', val)
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = row.action
        getDispatchProjectList().then(res => {
          if (res.code == 200) {
            this.projectOptions = res?.data?.map(el => ({
              label: el.projectName,
              value: el.projectId,
              dispatchProjectId: el.dispatchProjectId,
            }))
          }
        })

        if (row.action == '修改' || row.action == '复制') {
          this.modalLoading = true

          getRunmdById({ runCmdId: row.runCmdId }).then(res => {
            if (res.code == 200) {
              this.loadUserOptions(res.data.projectId).then(e => {
                this.userOptions = e

                this.form = {
                  ...res.data,
                }

                if (row.action == '复制') {
                  this.form.runCmdId = null
                }

                getDeviceByProjectId({ projectId: res.data.projectId }).then(el => {
                  this.deviceList = el?.data?.map(el => ({ ...el, active: false }))
                  this.deviceSelect = this.form.deviceOpenTimes?.map(item => item.deviceId)
                  this.deviceList = this.deviceList?.map(item => {
                    // 如果 deviceId 存在于 deviceIds 数组中，则将 active 设置为 true
                    if (this.deviceSelect.includes(item.deviceId)) {
                      return { ...item, active: true }
                    }
                    // 否则保持原样
                    return { ...item }
                  })

                  this.deviceOptions = this.deviceList?.filter(item => item.active === true) // el?.data
                  console.log('*** 204 deviceOptions', this.deviceOptions, this.form.deviceOpenTimes)
                  this.form.deviceOpenTimes.forEach(e => {
                    let device = this.deviceOptions.find(item => item.deviceId === e.deviceId)
                    if (device) {
                      this.$set(device, 'planTime', [e.startDate, e.endDate])
                    }
                  })

                  this.$set(this.form, 'planTime', [this.form.planStartDate, this.form.planEndDate])
                  this.modalLoading = false
                })
              })
            }
          })
        }
      },

      handleProjectChange(value) {
        getDeviceByProjectId({ projectId: value }).then(res => {
          if (res.code == 200) {
            // this.deviceOptions = res?.data
            this.deviceList = res?.data?.map(el => ({ ...el, active: false }))
            console.log('*** 224 deviceOptions', this.deviceOptions)
          }
        })

        this.form.wardUserIds = null
        this.form.workUserIds = []
        this.loadUserOptions(value).then(e => {
          this.userOptions = e
        })
      },

      loadUserOptions(projectId) {
        return new Promise((resolve, reject) => {
          let project = this.projectOptions.find(item => item.value === projectId)
          let options = []
          if (project) {
            getUserList({ dispatchProjectId: project.dispatchProjectId }).then(res => {
              if (res.code == 200) {
                options = res?.data?.map(el => ({
                  label: el.name,
                  value: el.userId,
                }))
              }
              resolve(options)
            })
          } else {
            resolve(options)
          }
        })
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.planStartDate = moment(this.form.planStartDate).format('YYYY-MM-DD HH:mm:ss')
            this.form.planEndDate = moment(this.form.planEndDate).format('YYYY-MM-DD HH:mm:ss')
            this.form.deviceOpenTimes = []
            this.deviceOptions.forEach(e => {
              if (e.planTime && e.planTime.length > 0) {
                this.form.deviceOpenTimes.push({
                  deviceId: e.deviceId,
                  startDate: moment(e.planTime[0]).format('YYYY-MM-DD HH:mm:ss'),
                  endDate: moment(e.planTime[1]).format('YYYY-MM-DD HH:mm:ss'),
                })
              }
            })
            if (this.form.runCmdId == null) {
              addRunmd(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editRunmd(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }

  ::v-deep .ant-tabs-tabpane {
    // height: calc(100vh - 160px);
  }

  // ::v-deep .title {
  //   font-size: 15px;
  //   color: #1890ff !important;
  // }

  ::v-deep .attaches {
    // margin-top: 100px;
  }

  // ::v-deep .ant-checkbox-wrapper {
  // }
  .checkbox-container {
    // width: 300px;
    text-align: left; /* 左对齐 */
  }

  /* 设置每个 checkbox 的样式 */
  .ant-checkbox-wrapper:first-child {
    margin-left: 8px;
  }
  .ant-checkbox-wrapper {
    margin-bottom: 8px;
  }

  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
</style>
