import request from '@/utils/request'

// 工程设备-列表分页查询
export function getDevicePage(data) {
  return request({
    url: '/base/device/page',
    method: 'post',
    data
  })
}

// 工程设备-删除
export function deleteDevice(params) {
  return request({
    url: '/base/device/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 工程设备-新增
export function addDevice(data) {
  return request({
    url: '/base/device/add',
    method: 'post',
    data
  })
}

// 工程设备-更新
export function updateDevice(data) {
  return request({
    url: '/base/device/update',
    method: 'post',
    data
  })
}

// 工程设备-详情
export function getDevice(params) {
  return request({
    url: '/base/device/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
