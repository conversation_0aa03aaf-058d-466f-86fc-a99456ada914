<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="560"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="调度类型" prop="dispatchType">
                <a-radio-group v-model="form.dispatchType" @change="changeDispatchType">
                  <a-radio value="1">日常调度</a-radio>
                  <a-radio value="2">临时调度</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="调度日期" prop="dispatchDate">
                <a-date-picker
                  v-model="form.dispatchDate"
                  valueFormat="YYYY-MM-DD"
                  placeholder="请选择"
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>

            <a-col class="input-alternator" :lg="24" :md="24" :sm="24" :span="24">
              <div class="input-alternator-text">
                <div class="input-title">开启设备</div>
                <div class="input-item">1#发电机</div>
                <div class="input-item">2#发电机</div>
                <div class="input-item">3#发电机</div>
              </div>
              <div class="input-alternator-value" v-if="form.dispatchType == '1'">
                <div class="input-title">开启时长</div>
                <div class="input-item">
                  <a-input-number
                    :precision="1"
                    :min="0.1"
                    :max="24"
                    v-model="alternator1"
                    placeholder="请输入"
                  ></a-input-number>
                  <span class="unit">h</span>
                </div>
                <div class="input-item">
                  <a-input-number
                    :precision="1"
                    :min="0.1"
                    :max="24"
                    v-model="alternator2"
                    placeholder="请输入"
                  ></a-input-number>
                  <span class="unit">h</span>
                </div>
                <div class="input-item">
                  <a-input-number
                    :precision="1"
                    :min="0.1"
                    :max="24"
                    v-model="alternator3"
                    placeholder="请输入"
                  ></a-input-number>
                  <span class="unit">h</span>
                </div>
              </div>
              <div class="input-alternator-value" v-if="form.dispatchType == '2'">
                <div class="input-title">开启时间</div>
                <div class="input-item">
                  <a-time-picker v-model="startTime1" format="HH:mm" formatValue="HH:mm" />
                </div>
                <div class="input-item">
                  <a-time-picker v-model="startTime2" format="HH:mm" formatValue="HH:mm" />
                </div>
                <div class="input-item">
                  <a-time-picker v-model="startTime3" format="HH:mm" formatValue="HH:mm" />
                </div>
              </div>
              <div class="input-alternator-value" v-if="form.dispatchType == '2'">
                <div class="input-title end-time">结束时间</div>
                <div class="input-item">
                  <span class="time-clearance">~</span>
                  <a-time-picker v-model="endTime1" format="HH:mm" formatValue="HH:mm" @change="changeEndTime(1)" />
                </div>
                <div class="input-item">
                  <span class="time-clearance">~</span>
                  <a-time-picker v-model="endTime2" format="HH:mm" formatValue="HH:mm" @change="changeEndTime(2)" />
                </div>
                <div class="input-item">
                  <span class="time-clearance">~</span>
                  <a-time-picker v-model="endTime3" format="HH:mm" formatValue="HH:mm" @change="changeEndTime(3)" />
                </div>
              </div>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="备注">
                <a-textarea v-model="form.remark" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions, getSysUserPage } from '@/api/common'
  import { getHydropowerDetails, addHydropowerStation, editHydropowerStation } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  import moment from 'moment'

  export default {
    name: 'FormHydropower',
    props: ['shiftList', 'deptOptions'],
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,

        deviceOptions: [],

        formTitle: '',

        alternator1: undefined,
        alternator2: undefined,
        alternator3: undefined,

        startTime1: undefined,
        startTime2: undefined,
        startTime3: undefined,

        endTime1: undefined,
        endTime2: undefined,
        endTime3: undefined,
        // 表单参数
        form: {
          detailsAdds: [],
          dispatchDate: undefined,
          dispatchType: '1',
          remark: undefined,
        },
        open: false,
        rules: {
          dispatchType: [{ required: true, message: '调度类型不能为空', trigger: 'change' }],
          dispatchDate: [{ required: true, message: '调度日期不能为空', trigger: 'change' }],
        },
      }
    },
    filters: {},
    created() {
      getSysUserPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        this.userOptions = res.data.data
      })
    },
    computed: {},
    watch: {},
    methods: {
      handleClose() {
        this.open = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.open = true
        this.formTitle = '新增'
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.modalLoading = true
        getHydropowerDetails({ id: record.id }).then(res => {
          this.form.dispatchType = res?.data?.dispatchType + ''
          this.form.dispatchDate = res?.data?.dispatchDate
          this.form.remark = res?.data?.remark
          this.form.id = res?.data?.id
          if (res?.data?.dispatchType == 1) {
            res?.data?.detailsVos.forEach(el => {
              if (el.sort == 1) {
                this.alternator1 = el.hour
              } else if (el.sort == 2) {
                this.alternator2 = el.hour
              } else if (el.sort == 3) {
                this.alternator3 = el.hour
              }
            })
          } else if (res?.data?.dispatchType == 2) {
            res?.data?.detailsVos.forEach(el => {
              if (el.sort == 1) {
                this.startTime1 = moment(el.startTime, 'HH:mm')
                this.endTime1 = moment(el.endTime, 'HH:mm')
              } else if (el.sort == 2) {
                this.startTime2 = moment(el.startTime, 'HH:mm')
                this.endTime2 = moment(el.endTime, 'HH:mm')
              } else if (el.sort == 3) {
                this.startTime3 = moment(el.startTime, 'HH:mm')
                this.endTime3 = moment(el.endTime, 'HH:mm')
              }
            })
          }
          this.modalLoading = false
        })
      },
      changeDispatchType() {
        if (this.form.dispatchType == '1') {
          this.startTime1 = undefined
          this.endTime1 = undefined
          this.startTime2 = undefined
          this.endTime2 = undefined
          this.startTime3 = undefined
          this.endTime3 = undefined
        } else if (this.form.dispatchType == '2') {
          this.alternator1 = undefined
          this.alternator2 = undefined
          this.alternator3 = undefined
        }
      },
      changeEndTime(val) {
        if (val === 1) {
          if (moment(this.endTime1).diff(moment(this.startTime1), 'seconds') < 0) {
            this.$message.warning('结束时间不能小于开始时间', 3)
            this.endTime1 = undefined
          }
        } else if (val === 2) {
          if (moment(this.endTime2).diff(moment(this.startTime2), 'seconds') < 0) {
            this.$message.warning('结束时间不能小于开始时间', 3)
            this.endTime2 = undefined
          }
        } else if (val === 3) {
          if (moment(this.endTime3).diff(moment(this.startTime3), 'seconds') < 0) {
            this.$message.warning('结束时间不能小于开始时间', 3)
            this.endTime3 = undefined
          }
        }
      },
      getTransition(value) {
        let jq = value.toString()
        let str = jq.substring(16, 21)
        return str
      },
      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.form.detailsAdds = []
            if (this.form.dispatchType == '1') {
              if (this.alternator1 == undefined && !this.alternator2 == undefined && !this.alternator3 == undefined) {
                this.$message.warning('至少开启一台设备', 3)
                return
              }
              if (this.alternator1) {
                this.form.detailsAdds.push({
                  hour: this.alternator1,
                  sort: 1,
                })
              }
              if (this.alternator2) {
                this.form.detailsAdds.push({
                  hour: this.alternator2,
                  sort: 2,
                })
              }
              if (this.alternator3) {
                this.form.detailsAdds.push({
                  hour: this.alternator3,
                  sort: 3,
                })
              }
            } else if (this.form.dispatchType == '2') {
              if (
                (!this.startTime1 &&
                  !this.endTime1 &&
                  !this.startTime2 &&
                  !this.endTime2 &&
                  !this.startTime3 &&
                  !this.endTime3) ||
                (this.startTime1 && !this.endTime1) ||
                (this.startTime2 && !this.endTime2) ||
                (this.startTime3 && !this.endTime3)
              ) {
                this.$message.warning('至少开启一台设备', 3)
                return
              }
              if (this.startTime1 && this.endTime1) {
                this.form.detailsAdds.push({
                  sort: 1,
                  startTime: this.getTransition(this.startTime1),
                  endTime: this.getTransition(this.endTime1),
                })
              }
              if (this.startTime2 && this.endTime2) {
                this.form.detailsAdds.push({
                  sort: 2,
                  startTime: this.getTransition(this.startTime2),
                  endTime: this.getTransition(this.endTime2),
                })
              }
              if (this.startTime3 && this.endTime3) {
                this.form.detailsAdds.push({
                  sort: 3,
                  startTime: this.getTransition(this.startTime3),
                  endTime: this.getTransition(this.endTime3),
                })
              }
            }

            this.loading = true
            if (this.form.id !== undefined) {
              editHydropowerStation(this.form)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            } else {
              addHydropowerStation(this.form)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .input-alternator {
    display: flex;
    .input-alternator-text {
      margin-right: 15px;
      .input-item {
        line-height: 30px;
      }
    }
    .input-title {
      color: #333;
      padding-left: 10px;
      position: relative;
      &::before {
        content: '*';
        position: absolute;
        left: 0;
        color: #f5222d;
      }
    }
    .input-item,
    .input-title {
      margin-bottom: 10px;
    }
    .unit {
      margin-left: 10px;
    }
  }
  .time-clearance {
    margin: 0 6px;
  }
  .end-time {
    margin-left: 18px;
  }
</style>
