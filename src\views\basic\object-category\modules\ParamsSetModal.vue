<template>
  <div>
    <ant-modal :visible="open" :modal-title="modalTitle" modalWidth="600" @cancel="cancel" modalHeight="420">
      <div class="param-set-content" slot="content">
        <advance-table
          :columns="columns"
          bordered
          :data-source="list"
          :title="tableTitle"
          :loading="loading"
          rowKey="rowKey"
          size="middle"
          tableKey="param-set-table"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :format-conditions="true"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="!multiple" @click="handleDelete" style="margin-left: 10px">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </advance-table>
      </div>

      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="confirm" :loading="btnLoading">确定</a-button>
      </template>
    </ant-modal>
    <!-- <TableModal v-if="isShowTableModal" ref="tableModalRef" @close="isShowTableModal = false" /> -->
  </div>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'

  import draggable from 'vuedraggable'
  import cloneDeep from 'lodash.clonedeep'
  import { getObjectIndex, addObjectIndex } from '../services'
  import { getOptions } from '@/api/common'

  export default {
    name: 'ParamSet',
    props: {},
    components: {
      AntModal,
      AdvanceTable,
      draggable,
    },
    data() {
      return {
        open: false,
        btnLoading: false,
        modalTitle: '',
        rowInfo: {},
        isShowTableModal: false,
        dragging: false,

        reqType: 'add',
        form: {
          objectCategoryId: undefined,
        },

        monitoringIndexOptions: [],

        searchTValue: '',
        tableTitle: ' ',
        loading: false,
        selectedRowKeys: [],
        selectedRows: [],
        ids: [],
        single: true,
        multiple: true,
        list: [],
        columns: [
          {
            title: '序号',
            width: 50,
            customRender: (_, r, i) => {
              return i + 1
            },
          },
          {
            title: '监测指标',
            dataIndex: 'indexCode',
            width: 180,
            minWidth: 180,
            customRender: (_, r, i) => {
              const cIndexs = this.list.map(el => el.indexCode)
              // const cOptions = this.monitoringIndexOptions.filter(
              //   el => !cIndexs.includes(el.key) || el.key === r.indexCode
              // )
              const cOptions = this.monitoringIndexOptions
              return (
                <div class='indicator-cell'>
                  <a-select
                    showSearch
                    style={{ width: '100%' }}
                    v-model={this.list[i].indexCode}
                    onChange={v => this.onIndexCodeChange(v, i)}
                    filterOption={this.filterOption}
                    placeholder='请选择'
                  >
                    {cOptions.map((el, j) => {
                      return (
                        <a-select-option key={j} value={el.key}>
                          {el.value}
                        </a-select-option>
                      )
                    })}
                  </a-select>
                </div>
              )
            },
          },
          {
            title: '展示名称',
            customRender: (v, r, i) => {
              return (
                <a-input
                  value={this.list[i].displayName}
                  style='text-align: center;'
                  onChange={e => this.onDisplayNameChange(e.target.value, r)}
                  placeholder='请输入'
                ></a-input>
              )
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      getOptions('monitoringIndex').then(res => {
        this.monitoringIndexOptions = res.data
      })
    },
    mounted() {},
    methods: {
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 取消按钮
      cancel() {
        this.open = false
        // this.$emit('close')
      },
      handleShow(record) {
        this.open = true
        this.modalTitle = record.objectCategoryName + '监测指标'
        this.form.objectCategoryId = record.objectCategoryId

        this.rowInfo = record

        this.loading = true
        let getParams = {
          objectCategoryId: record?.objectCategoryId,
          // objectId: record?.parentId,
          // objectType: record?.objectCategoryCode?.substring(0, 2),
        }
        getObjectIndex(getParams).then(res => {
          this.loading = false
          if (res?.data?.indexs?.length) {
            this.reqType = 'edit'
          } else {
            this.reqType = 'add'
          }
          this.form.objectCategoryId = record.objectCategoryId

          this.list = (res?.data || []).map((el, i) => ({ ...el, rowKey: i }))
        })
      },

      //搜索
      fetchTerminal(val) {
        this.searchTValue = val
      },

      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        this.ids = this.selectedRows.map(item => item.rowKey)
        this.single = selectedRowKeys.length !== 1
        this.multiple = !selectedRowKeys.length
      },

      onIndexCodeChange(v, i) {
        // this.list[i].indexCodes = [
        //   {
        //     terminalId: undefined,
        //     indexCode: undefined,
        //     terminalCode: undefined,
        //     terminalName: undefined,
        //     sort: null,
        //   },
        // ]
      },
      onDisplayNameChange(v, i) {
        this.list?.forEach((el, index) => {
          if (el.rowKey == i.rowKey) {
            el.displayName = v
          }
        })
      },

      // 查看指标详情
      handleDetail(row) {
        this.isShowTableModal = true
        // this.$nextTick(() =>
        //   this.$refs.tableModalRef.handleShow({ siteId: this.rowInfo.siteId, indexCode: row.indexCode })
        // )
      },

      // 增加指标
      handleAdd() {
        this.list.push({
          rowKey: this.list.length,
          indexCode: null,
          displayName: null,
          objectCategoryId: this.form.objectCategoryId,
          objectCategoryIndexId: undefined,
        })
      },

      // 删除指标
      handleDelete() {
        this.list = this.list
          ?.filter(el => {
            return !this.ids.includes(el.rowKey)
          })
          .map((el, idx) => ({ ...el, rowKey: idx }))

        this.onSelectChange([], [])
      },

      // checkMove: function (e) {
      //   window.console.log('Future index: ' + e.draggedContext.futureIndex)
      // },

      /** 提交按钮 */
      confirm() {
        this.btnLoading = true
        addObjectIndex(this.list)
          .then(res => {
            this.$message.success('配置成功', 3)
            this.open = false
            this.$emit('ok')
          })
          .catch(err => (this.btnLoading = false))
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';

  // 多余滚动条
  ::v-deep .advanced-table {
    // overflow-y: visible;
  }

  ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background-color: transparent;
  }

  .add-terminal {
    .add-terminal-btn {
      padding: 0 8px;
      height: 24px;
      min-width: 50px;
    }
  }

  .indicator-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .indicator-btn {
      width: 60px;
    }
  }

  .terminal-select-item {
    display: flex;
    width: 100%;
    justify-content: space-between;
    > span {
      flex: 1;
      text-align: center;
    }
  }
  .terminal-content {
    .terminal-box {
      background-color: #f1f1f1;
      cursor: move;
      border-radius: 12px;
      padding: 6px 10px;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      ::v-deep .ant-select-selection-selected-value {
        width: 100%;
      }

      .terminal-item-operation {
        display: flex;
        flex: 1;
        justify-content: center;
        align-items: center;
        .terminal-delete {
          margin-left: 20px;
          cursor: pointer;
          color: @primary-color;
        }
      }
    }
  }

  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }
  .list-group {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    .list-group-item {
      cursor: move;
      position: relative;
      display: block;
      margin-bottom: -1px;
      background-color: #fff;
      border: 1px solid rgba(0, 0, 0, 0.125);
    }
  }
</style>
