import request from '@/utils/request'

// 设备分类-列表分页查询
export function getDeviceCategoryPage(data) {
  return request({
    url: '/base/deviceCategory/page',
    method: 'post',
    data
  })
}
// 设备分类-获取父节点树
export function getParentTree(params) {
  return request({
    url: '/base/deviceCategory/getParentTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 设备分类-新增
export function addDeviceCategoryPage(data) {
  return request({
    url: '/base/deviceCategory/add',
    method: 'post',
    data
  })
}

//设备分类-删除
export function deleteDeviceCategory(params) {
  return request({
    url: '/base/deviceCategory/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

//设备分类-获取排序号
export function getNextSort(params) {
  return request({
    url: '/base/deviceCategory/getNextSort',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 设备分类-更新
export function updateDeviceCategory(data) {
  return request({
    url: '/base/deviceCategory/update',
    method: 'post',
    data
  })
}

// 设备分类-详情
export function getDeviceCategory(params) {
  return request({
    url: '/base/deviceCategory/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
