<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="简报名称">
        <a-input v-model="queryParam.reportName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="发布人">
        <a-select
          allow-clear
          show-search
          v-model="queryParam.publisherId"
          placeholder="请输入"
          option-filter-prop="children"
          :filter-option="filterOptionUser"
          @change="handleChangeUser"
        >
          <a-select-option v-for="item in createPersonList" :key="item.userId" :value="item.userId">
            {{ item.name }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="发布状态">
        <a-select
          allow-clear
          show-search
          placeholder="请输入"
          v-model="queryParam.status"
          option-filter-prop="children"
          :filter-option="filterOptionUser"
          :options="statusOptions"
        ></a-select>
      </a-form-item>
      <a-form-item label="创建时间">
        <a-range-picker
          allow-clear
          :value="takeEffect"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormMyBriefing
          v-if="showFormMyBriefing"
          ref="formMyBriefingRef"
          @ok="onOperationComplete"
          @close="showFormMyBriefing = false"
        />
        <FormManage v-if="showFormManage" ref="formManageRef" @previewClose="showFormManage = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getComUserList } from '@/api/common'
  import { getMyBriefingList, deleteMyBriefing } from './services'
  import FormMyBriefing from './modules/FormMyBriefing.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormManage from '@/views/briefing/manage/modules/FormManage.vue'
  import moment from 'moment'

  export default {
    name: 'MyBriefing',
    components: {
      VxeTable,
      VxeTableForm,
      FormMyBriefing,
      FormManage,
    },
    data() {
      return {
        showFormMyBriefing: false,
        createPersonList: [],
        takeEffect: [],
        list: [],
        tableTitle: '我的简报',
        isChecked: false,
        selectIds: [],
        names: [],
        loading: false,
        total: 0,
        showFormManage: false,
        workShiftList: [],
        workGroupList: [],
        lineOptions: [],
        statusOptions: [
          { value: '1', label: '草稿' },
          { value: '2', label: '待发布' },
          { value: '3', label: '已发布' },
        ],
        queryParam: {
          endTime: '',
          pageNum: 1,
          pageSize: 10,
          publisherId: undefined,
          status: undefined,
          reportName: '',
          sort: [],
          startTime: '',
        },
        columns: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '简报名称',
            field: 'reportName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '发布人',
            field: 'publisherName',
            minWidth: 100,
          },
          {
            title: '发布时间',
            field: 'publishTime',
            minWidth: 180,
          },
          {
            title: '创建人',
            field: 'generateName',
            minWidth: 100,
          },
          {
            title: '创建时间',
            field: 'genTime',
            minWidth: 180,
          },
          {
            title: '发布状态', //状态(1-草稿 2-待发布 3-已发布)
            field: 'status',
            minWidth: 120,
            slots: {
              default: ({ row }) => {
                let statusPointColor = 'common-status-incomplete'
                if (row.status == '2') {
                  statusPointColor = 'common-status-waiting'
                } else if (row.status == '3') {
                  statusPointColor = 'common-status-completed'
                }
                return (
                  <div class='common-status-box'>
                    <i class={['common-status-icon', statusPointColor]}></i>
                    <span>{this.statusOptions.find(el => el.value == row.status)?.label}</span>
                  </div>
                )
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 128,
            slots: {
              default: ({ row, rowIndex }) => {
                if (row.status === 1) {
                  return (
                    <span>
                      <a onClick={() => this.handleEdit(row)}>修改</a>
                      <a-divider type='vertical' />
                      <a onClick={() => this.handleDelete(row)}>删除</a>
                    </span>
                  )
                } else if (row.status === 2) {
                  return (
                    <span>
                      <a onClick={() => this.handlePreview(row)}>预览</a>
                    </span>
                  )
                } else if (row.status === 3) {
                  return (
                    <span>
                      <a onClick={() => this.handlePreview(row)}>预览</a>
                      <a-divider type='vertical' />
                      <a onClick={() => this.handleDownload(row)}>下载</a>
                    </span>
                  )
                }
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {
      getComUserList({}).then(res => {
        if (res?.code == 200) {
          this.createPersonList = res?.data
        }
      })
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.startTime = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') + ' 00:00:00' : null
        this.queryParam.endTime = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') + ' 23:59:59' : null
      },
      /** 查询列表 */
      getList() {
        this.showFormMyBriefing = false
        this.loading = true
        getMyBriefingList(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.list.forEach(item => {
            item.isStopped = item.isStopped == 0 ? true : false
            return item
          })
          this.total = response?.data?.total
          this.loading = false
        })
      },
      handleChangeUser(value) {
        this.queryParam.publisherId = value
      },
      filterOptionUser(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.reportId)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          endTime: '',
          pageNum: 1,
          publisherId: undefined,
          status: undefined,
          reportName: '',
          sort: [],
          startTime: '',
        }
        this.takeEffect = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showFormMyBriefing = true
        this.$nextTick(() => this.$refs.formMyBriefingRef.handleMyBriefing())
      },
      /* 修改 */
      handleEdit(record) {
        this.showFormMyBriefing = true
        this.$nextTick(() => this.$refs.formMyBriefingRef.handleMyBriefing(record))
      },
      handlePreview(record) {
        const previewData = {
          docUrl: record.docUrl,
        }
        this.showFormManage = true
        this.$nextTick(() => this.$refs.formManageRef.handleManage(previewData))
      },
      handleDownload(record) {
        window.location.href = record.docUrl
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const reportIds = row?.reportId ? [row.reportId] : this.selectIds
        // const names = row?.planName || this.names
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteMyBriefing({ reportIds: reportIds.join(',') }).then(res => {
              that.$message.success(`删除成功`, 3)
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
