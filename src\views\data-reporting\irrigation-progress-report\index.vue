<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="灌溉时间">
        <a-date-picker v-model="queryParam.queryTime" placeholder="请选择灌溉时间" allow-clear style="width: 100%"
          @change="handleQuery" @keyup.enter.native="handleQuery"></a-date-picker>
      </a-form-item>

      <template #table>
        <VxeTable ref="vxeTableRef" tableTitle="灌溉进度报表" :columns="columns" :tableData="list" :loading="loading"
          :isAdaptPageSize="true" @adaptPageSizeChange="adaptPageSizeChange" @refresh="getList"
          @selectChange="selectChange" @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange">
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="primary" @click="handleImport()">
              <a-icon type="import" />
              导入
            </a-button>
            <a-button type="primary" @click="handleExport()">
              <a-icon type="export" />
              导出
            </a-button>
          </div>
        </VxeTable>

        <FormDrawer v-if="showFormDrawer" ref="formDrawerRef" @ok="getList" @close="showFormDrawer = false" />
        <DetailsDrawer v-if="showDetails" ref="detailsRef" @ok="getList" @close="showDetails = false" />
        <ImportModal v-if="showImport" ref="importRef" @ok="getList" @close="showImport = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
import VxeTable from '@/components/VxeTable'
import VxeTableForm from '@/components/VxeTableForm'
import FormDrawer from './modules/FormDrawer.vue'
import DetailsDrawer from './modules/DetailsDrawer.vue'
import ImportModal from './modules/ImportModal.vue'
import { getIrrigationSettingPage, deleteIrrigationSetting } from './service'
import moment from 'moment'

export default {
  name: 'IrrigationProgressReport',
  components: {
    VxeTableForm,
    VxeTable,
    FormDrawer,
    DetailsDrawer,
    ImportModal,
  },
  data() {
    return {
      showFormDrawer: false,
      showDetails: false,
      showImport: false,

      list: [],
      loading: false,
      total: 0,

      queryParam: {
        pageNum: 1,
        pageSize: 10,
        queryTime: null,
        sort: [],
      },
      columns: [
        { type: 'seq', title: '序号', width: 50 },
        {
          title: '灌溉时间',
          field: 'irrigationDate',
          minWidth: 120,
          showOverflow: 'tooltip',
          slots: {
            default: ({ row }) => {
              if (row.startTime && row.endTime) {
                return `${moment(row.startTime).format('YYYY-MM-DD')} ~ ${moment(row.endTime).format('YYYY-MM-DD')}`
              }
              return '-'
            },
          },
        },
        {
          title: '全灌域直口实引水量（流量日）',
          field: 'totalWaterAmount',
          minWidth: 180,
          slots: {
            default: ({ row }) => {
              return row.totalWaterAmount || '-'
            },
          },
        },
        {
          title: '合计浇地面积（万亩）',
          field: 'totalPourAmount',
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return row.totalPourAmount || '-'
            },
          },
        },
        {
          title: '填报时间',
          field: 'fillTime',
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return row.fillTime ? moment(row.fillTime).format('YYYY-MM-DD HH:mm') : '-'
            },
          },
        },
        {
          title: '填报人',
          field: 'fillUserName',
          minWidth: 100,
        },
        {
          title: '操作',
          field: 'operate',
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return (
                <span>
                  <a onClick={() => this.handleDetails(row)}>查看</a>
                  <a-divider type='vertical' />
                  <a onClick={() => this.handleUpdate(row)}>编辑</a>
                  <a-divider type='vertical' />
                  <a onClick={() => this.handleDelete(row)}>删除</a>
                </span>
              )
            },
          },
        },
      ],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    adaptPageSizeChange(pageSize) {
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    /** 查询列表 */
    async getList() {
      this.loading = true
      try {
        const params = {
          pageNum: this.queryParam.pageNum,
          pageSize: this.queryParam.pageSize,
          queryTime: this.queryParam.queryTime ? moment(this.queryParam.queryTime).format('YYYY-MM-DD HH:mm:ss') : ''
        }

        const response = await getIrrigationSettingPage(params)
        if (response.success) {
          this.list = response.data.data || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.message || '获取数据失败')
        }
      } catch (error) {
        console.error('获取列表数据失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParam.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParam = {
        ...this.queryParam,
        pageNum: 1,
        queryTime: null,
        sort: [],
      }
      this.handleQuery()
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.queryParam.pageNum = currentPage
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    // 多选框选中功能已移除
    selectChange() {
      // 已移除多选功能
    },
    // 排序
    sortChange(valObj) {
      this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
      this.getList()
    },
    /* 新增 */
    handleAdd() {
      this.showFormDrawer = true
      this.$nextTick(() => this.$refs.formDrawerRef.handleAdd())
    },
    /* 修改 */
    handleUpdate(record) {
      this.showFormDrawer = true
      this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
    },
    /* 查看 */
    handleDetails(record) {
      this.showDetails = true
      this.$nextTick(() => this.$refs.detailsRef.showDetails(record))
    },
    /* 导入 */
    handleImport() {
      // this.showImport = true
      // this.$nextTick(() => this.$refs.importRef.show())
    },
    /* 导出 */
    handleExport() {
      this.$message.success('导出成功')
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const that = this
      const id = row.id

      this.$confirm({
        title: '确认删除所选中数据?',
        content: '删除后数据将无法恢复',
        async onOk() {
          try {
            const response = await deleteIrrigationSetting(id)
            if (response.success) {
              that.$message.success('删除成功')
              that.getList()
            } else {
              that.$message.error(response.message || '删除失败')
            }
          } catch (error) {
            console.error('删除失败:', error)
            that.$message.error('删除失败')
          }
        },
        onCancel() { },
      })
    },
  },
}
</script>
<style lang="less" scoped>
.table-operations {
  .ant-btn {
    margin-right: 8px;

    &:last-child {
      margin-right: 0px;
    }
  }
}
</style>
