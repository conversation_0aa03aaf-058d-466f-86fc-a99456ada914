<template>
  <base-echart id="line-echart" class="line-echart" :width="width" :height="height" :option="options" />
</template>

<script lang="jsx">
  import BaseEchart from './BaseEchart.vue'
  import * as echarts from 'echarts/core'

  const colors = [
    '#58A9FB',
    '#B5E241',
    '#4363d8',
    '#911eb4',
    '#00A316',
    '#fabed4',
    '#469990',
    '#dcbeff',
    '#9A6324',
    '#aaffc3',
    '#808000',
    '#000075',
    '#a9a9a9',
  ]

  //hex -> rgba
  function hexToRgba(hex, opacity) {
    return (
      'rgba(' +
      parseInt('0x' + hex.slice(1, 3)) +
      ',' +
      parseInt('0x' + hex.slice(3, 5)) +
      ',' +
      parseInt('0x' + hex.slice(5, 7)) +
      ',' +
      opacity +
      ')'
    )
  }

  export default {
    components: {
      BaseEchart,
    },
    props: {
      dataSource: {
        require: true,
        default: () => [
          {
            name: '',
            data: [],
          },
        ],
      },
      custom: { default: () => {} },
      width: { default: '100%' },
      height: { default: '300px' },
    },
    data() {
      return {}
    },
    computed: {
      options() {
        return this.getOptions(this.dataSource, this.custom)
      },
    },
    mounted() {},
    methods: {
      getOptions(data, custom) {
        let {
          shortValue = true, // 缩写坐标值
          xLabel = '', // x轴名称
          yLabel = '', //y轴名称
          yUnit = '', //y轴单位
          legend = false, // 图例
          showAreaStyle = true, // 颜色区域
          rYUnit = '', // 右侧y轴单位
          rYLabel = '', // 右侧y轴名称
          dataZoom = true,
          color = null,
        } = custom

        const option = {
          color: color || colors,
          title: {
            top: 10,
            left: 10,
            // text: yLabel,
            textAlign: 'left',
            textStyle: {
              color: '#000',
              fontSize: 14,
              fontWeight: 400,
            },
          },
          grid: {
            left: '4%',
            right: xLabel ? '10%' : '6%',
            bottom: !!dataZoom ? '16%' : '5%',
            top: '15%',
            containLabel: true,
          },
          tooltip: {
            appendToBody: true,
            confine: true,
            position: (pos, params, dom, rect, size) => {
              let obj = { bottom: size.viewSize[1] - pos[1] + 10 }
              obj[['right', 'left'][+(pos[0] < size.viewSize[0] / 2)]] =
                pos[0] < size.viewSize[0] / 2 ? pos[0] + 10 : size.viewSize[0] - pos[0] + 10
              return obj
            },
            trigger: 'axis',
            // backgroundColor: 'rgba(0,0,0,0.6)', //通过设置rgba调节背景颜色与透明度
            borderWidth: 0,
            textStyle: {
              color: '#000',
            },
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
          },
          xAxis: {
            data: data.length ? data[0].data.map(i => i[0]) : [],
            name: (data.length && xLabel) || '',
            nameTextStyle: {
              padding: [0, 0, 0, -5],
              color: '#000',
              fontSize: 12,
              fontWeight: 400,
            },
            axisLabel: {
              textStyle: {
                color: '#000',
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            z: 10,
          },
          yAxis: [
            {
              name: yLabel || '',
              axisPointer: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed',
                  color: '#BBB',
                },
              },
              boundaryGap: ['0', '10%'], // 上下各留 10% 的空白
              axisTick: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  color: '#000',
                },
                formatter(t) {
                  if (shortValue) {
                    if (t > Math.pow(10, 8) - 1) {
                      return t / Math.pow(10, 9) + '亿'
                    }
                    if (t > Math.pow(10, 4) - 1) {
                      return t / 10000 + '万'
                    }
                  }
                  if (yUnit) {
                    return `${t}${yUnit}`
                  }
                  return t
                },
              },
            },
            {
              name: rYLabel || '',
              position: 'right',
              axisPointer: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              alignTicks: true, // ！！配置多坐标轴标签对齐
              axisLabel: {
                textStyle: {
                  color: '#000',
                },
                formatter(t) {
                  if (shortValue) {
                    if (t > Math.pow(10, 8) - 1) {
                      return t / Math.pow(10, 9) + '亿'
                    }
                    if (t > Math.pow(10, 4) - 1) {
                      return t / 10000 + '万'
                    }
                  }
                  if (yUnit) {
                    return `${t}${rYUnit}`
                  }
                  return t
                },
              },
            },
          ],
          dataZoom: [
            {
              show: !!dataZoom,
              moveHandleSize: 12,
              height: 25,
              width: '80%',
              left: '10%',
              bottom: '6%',
            },
            {
              type: 'inside',
            },
          ],
          legend: {
            show: !!legend,
            icon: 'rect',
            itemWidth: 20,
            itemHeight: 3,
            top: 5,
            left: '50%',
            textStyle: {
              color: '#000',
            },
            ...legend,
          },
          series: data.map((item, i) => {
            return {
              type: 'line',
              showBackground: true,
              smooth: false,
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              name: item.name,
              color: item.color,
              yAxisIndex: item?.yAxisIndex || 0,
              areaStyle: {
                opacity: showAreaStyle ? 0.4 : 0,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: hexToRgba(colors[i % colors.length], 0.3),
                  },
                  {
                    offset: 1,
                    color: '#fff',
                  },
                ]),
              },
              emphasis: {
                focus: 'series',
              },
              data: item.data,
            }
          }),
        }
        return option
      },
    },
  }
</script>

<style scoped></style>
