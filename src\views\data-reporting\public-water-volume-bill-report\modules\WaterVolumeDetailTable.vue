<template>
  <div class="water-volume-section">
    <div class="section-header" @click="toggleCollapse">
      <h3 class="section-title">
        <a-icon :type="collapsed ? 'right' : 'down'" style="margin-right: 8px;" />
        {{ title }}
      </h3>
    </div>
    
    <div v-show="!collapsed" class="section-content">
      <!-- 折算率显示 -->
      <div class="conversion-rate-section">
        <label>折算率：</label>
        <span class="conversion-rate-value">{{ formatConversionRate(conversionRate) }}%</span>
      </div>

      <!-- 表格数据展示 -->
      <div>
        <vxe-table
          :data="tableData"
          border
          stripe
          :row-config="{ keyField: 'id' }"
          :column-config="{ resizable: true }"
          height="400"
          class="water-volume-table"
          width="100%"
          header-align="center"
          align="center"
          show-footer
          :footer-method="footerMethod"
        >
          <!-- 直口渠名列 -->
          <vxe-column field="channelName" title="直口渠名" width="120" fixed="left">
            <template #default="{ row }">
              <span :title="row.channelCode">
                {{ row.channelName }}
              </span>
            </template>
          </vxe-column>

          <!-- 水资源费价格列 -->
          <vxe-column field="waterFeePrice20" title="超水20%内的水资源费价格（元）" width="180">
            <template #default="{ row }">
              <span>{{ formatNumber(row.waterFeePrice20) }}</span>
            </template>
          </vxe-column>

          <vxe-column field="waterFeePrice50" title="超水20%-50%的水资源费价格（元）" width="200">
            <template #default="{ row }">
              <span>{{ formatNumber(row.waterFeePrice50) }}</span>
            </template>
          </vxe-column>

          <!-- 动态渲染季度列 -->
          <template v-for="season in currentSeasons">
            <vxe-colgroup :key="season.key" :title="season.title">
              <!-- 包干水量 -->
              <vxe-column :field="`${season.key}_contractWater`" title="包干水量（万m³）" width="140">
                <template #default="{ row }">
                  <span>{{ formatNumber(row[`${season.key}_contractWater`]) }}</span>
                </template>
              </vxe-column>

              <!-- 实用水量分组 -->
              <vxe-colgroup :title="'实用水量（万m³）'">
                <!-- 月份列 -->
                <template v-for="month in season.months">
                  <vxe-column :key="month" :field="`${season.key}_month${month}`" :title="`${month}月`" width="80">
                    <template #default="{ row }">
                      <span>{{ formatNumber(row[`${season.key}_month${month}`]) }}</span>
                    </template>
                  </vxe-column>
                </template>

                <!-- 合计水量（自动计算） -->
                <vxe-column :field="`${season.key}_totalWater`" title="合计水量" width="80">
                  <template #default="{ row }">
                    <span style="font-weight: bold; color: #1890ff;">
                      {{ formatNumber(calculateSeasonTotal(row, season.key)) }}
                    </span>
                  </template>
                </vxe-column>
              </vxe-colgroup>

              <!-- 多元化供水 -->
              <vxe-column :field="`${season.key}_diversifiedWater`" title="多元化供水（万m³）" width="170">
                <template #default="{ row }">
                  <span>{{ formatNumber(row[`${season.key}_diversifiedWater`]) }}</span>
                </template>
              </vxe-column>
            </vxe-colgroup>
          </template>

          <!-- 自定义空数据显示 -->
          <template #empty>
            <div class="custom-empty">
              <a-empty description="暂无数据" />
            </div>
          </template>
        </vxe-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WaterVolumeDetailTable',
  props: {
    title: {
      type: String,
      required: true
    },
    tableData: {
      type: Array,
      default: () => []
    },
    conversionRate: {
      type: Number,
      default: 0.8
    },
    seasons: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      collapsed: false,
      // 默认季度配置（当没有传入seasons时使用）
      defaultSeasons: [
        {
          key: 'spring',
          title: '4月-6月',
          months: [4, 5, 6]
        },
        {
          key: 'summer',
          title: '7月-9月',
          months: [7, 8, 9]
        },
        {
          key: 'autumn',
          title: '10月-11月',
          months: [10, 11]
        }
      ]
    }
  },
  computed: {
    // 使用传入的seasons或默认的seasons
    currentSeasons() {
      return this.seasons && this.seasons.length > 0 ? this.seasons : this.defaultSeasons
    }
  },
  methods: {
    // 切换折叠状态
    toggleCollapse() {
      this.collapsed = !this.collapsed
    },
    
    // 计算季度合计
    calculateSeasonTotal(row, seasonKey) {
      const season = this.currentSeasons.find(s => s.key === seasonKey)
      if (!season) return 0
      
      let total = 0
      season.months.forEach(month => {
        const value = row[`${seasonKey}_month${month}`] || 0
        total += Number(value)
      })
      
      return total
    },
    
    // 格式化数字
    formatNumber(value) {
      if (value === null || value === undefined || isNaN(value)) {
        return '0.00'
      }
      return Number(value).toFixed(2)
    },

    // 格式化折算率（将小数转换为百分数显示）
    formatConversionRate(value) {
      if (value === null || value === undefined || isNaN(value)) {
        return '0.00'
      }
      return Number(value * 100)
    },

    // 合计行计算方法
    footerMethod({ columns, data }) {
      const footerData = [
        columns.map((column, columnIndex) => {
          // 第一列显示"合计"
          if (columnIndex === 0) {
            return '合计'
          }

          // 水资源费价格列显示价格总和
          if (column.field === 'waterFeePrice20' || column.field === 'waterFeePrice50') {
            if (data.length > 0) {
              const sum = data.reduce((total, row) => {
                const value = row[column.field] || 0
                return total + Number(value)
              }, 0)
              return this.formatNumber(sum)
            }
            return '0.00'
          }

          // 计算数值列的合计
          if (column.field && data.length > 0) {
            // 检查是否是季度相关的数值字段
            const isSeasonField = this.currentSeasons.some(season => {
              return column.field.includes(`${season.key}_contractWater`) ||
                     column.field.includes(`${season.key}_month`) ||
                     column.field.includes(`${season.key}_totalWater`) ||
                     column.field.includes(`${season.key}_diversifiedWater`)
            })

            if (isSeasonField) {
              // 特别处理合计水量列
              const isTotalWaterField = this.currentSeasons.some(season =>
                column.field === `${season.key}_totalWater`
              )

              if (isTotalWaterField) {
                // 对于合计水量列，需要使用 calculateSeasonTotal 方法计算
                const seasonKey = column.field.replace('_totalWater', '')
                const sum = data.reduce((total, row) => {
                  const seasonTotal = this.calculateSeasonTotal(row, seasonKey)
                  return total + Number(seasonTotal)
                }, 0)
                return this.formatNumber(sum)
              } else {
                // 对于其他数值列，直接从字段值计算
                const sum = data.reduce((total, row) => {
                  const value = row[column.field] || 0
                  return total + Number(value)
                }, 0)
                return this.formatNumber(sum)
              }
            }
          }

          return null
        })
      ]
      return footerData
    }
  }
}
</script>

<style lang="less" scoped>
.water-volume-section {
  margin-bottom: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  
  .section-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    cursor: pointer;
    user-select: none;
    
    &:hover {
      background: #f0f0f0;
    }
    
    .section-title {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      display: flex;
      align-items: center;
    }
  }
  
  .section-content {
    padding: 16px;
    
    .conversion-rate-section {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      label {
        margin-right: 8px;
        font-weight: 500;
        color: #333;
      }
      
      .conversion-rate-value {
        font-weight: 600;
        color: #1890ff;
        font-size: 14px;
      }
    }
    
    .water-volume-table {
      /deep/ .vxe-table--border-line {
        border-color: #e8e8e8;
      }
      
      /deep/ .vxe-header--column {
        background-color: #fafafa;
        font-weight: 600;
        font-size: 12px;
      }
      
      /deep/ .vxe-body--row {
        &:nth-child(even) {
          background-color: #fafafa;
        }
      }
      
      /deep/ .vxe-table {
        width: 100% !important;
        font-size: 12px;
      }
      
      /deep/ .vxe-cell {
        padding: 4px 8px;
      }

      // 底部合计行样式
      /deep/ .vxe-table--footer-wrapper {
        .vxe-footer--row {
          background-color: #f0f8ff !important;
          font-weight: bold;
          color: #1890ff;

          .vxe-footer--column {
            background-color: #f0f8ff !important;
            border-top: 2px solid #1890ff;
            text-align: center;
          }
        }
      }
    }

    .custom-empty {
      padding: 40px 20px;
      text-align: center;
      color: #999;
    }
  }
}
</style>
