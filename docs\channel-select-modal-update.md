# 种植意向填报模块选择渠道功能更新

## 更新内容

在种植意向填报模块的选择渠道弹窗中，新增了"填报单位"下拉框功能。

### 主要变更

1. **新增填报单位下拉框**
   - 在左侧"选择项"和右侧"已选项"的查询条件中都添加了填报单位下拉框
   - 填报单位选项与种植意向填报模块新增页面的填报单位下拉框保持一致
   - 默认选中当前用户所属部门

2. **API调用优化**
   - 在调用渠道数据接口时，会传递选中的填报单位ID作为orgId参数
   - 选择不同的填报单位会重新调用接口获取对应的渠道数据

3. **缓存机制更新**
   - 缓存键现在包含区划代码和填报单位ID，确保不同填报单位的数据独立缓存
   - 避免不同填报单位之间的数据混淆

### 技术实现

#### 1. 数据结构变更

```javascript
// 左侧参数新增reportUnit字段
leftParam: {
  pageNum: 1,
  pageSize: 10,
  reportUnit: null,  // 新增
  projectCode: '',
  projectName: ''
}

// 右侧参数新增reportUnit字段
rightParam: {
  pageNum: 1,
  pageSize: 10,
  reportUnit: null,  // 新增
  projectCode: '',
  projectName: ''
}

// 新增填报单位相关数据
orgTree: [],
reportUnitOptions: []
```

#### 2. 新增方法

- `getOrgTreeData()`: 获取组织机构树数据
- `buildReportUnitOptions()`: 构建填报单位选项，设置默认值

#### 3. 修改的方法

- `loadLeftData()`: 在API调用中传递reportUnit参数
- `filterAndPaginateLeftData()`: 使用包含填报单位的缓存键
- `handleLeftReset()`: 重置时恢复填报单位默认值并重新加载数据
- `handleRightReset()`: 重置时恢复填报单位默认值

#### 4. 参数传递机制

- `ChannelSelectModal.show(selectedProjects, reportUnit)`: 新增reportUnit参数
- `IrrigationPlantingTable`: 新增reportUnit prop，从父组件接收填报单位
- `FormDrawer`: 将formData.reportUnit传递给子组件

#### 5. 样式优化

- 调整了查询条件行的布局，支持三个输入框的合理显示
- 设置了最小宽度和换行支持，确保在不同屏幕尺寸下的良好显示效果

### 使用说明

1. **智能填报单位设置**
   - 如果在新增页面已选择填报单位，弹窗会自动填充为该单位
   - 如果未选择填报单位，则默认选中当前用户所属部门
   - 自动加载对应部门的渠道数据

2. **切换填报单位**
   - 用户可以从下拉框中选择其他填报单位
   - 选择后会自动重新查询对应的渠道数据

3. **查询和重置**
   - 查询功能会基于当前选中的填报单位进行过滤
   - 重置功能会将填报单位恢复为默认值并重新加载数据

### 兼容性

- 保持了原有的所有功能和接口
- 新增的填报单位功能不影响现有的渠道选择逻辑
- 向后兼容，不会影响其他模块对该组件的使用

### 文件变更

- `src/views/data-reporting/planting-intention-report/modules/ChannelSelectModal.vue`
  - 新增填报单位下拉框UI
  - 新增相关数据属性和方法
  - 优化API调用和缓存逻辑
  - 调整样式布局
  - 修改show方法支持reportUnit参数

- `src/views/data-reporting/planting-intention-report/modules/IrrigationPlantingTable.vue`
  - 新增reportUnit prop
  - 修改handleSelectChannel方法传递reportUnit参数

- `src/views/data-reporting/planting-intention-report/modules/FormDrawer.vue`
  - 在IrrigationPlantingTable组件中传递report-unit属性
