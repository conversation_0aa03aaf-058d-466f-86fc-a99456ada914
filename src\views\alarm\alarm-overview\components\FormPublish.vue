<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      :maskClosable="false"
      modalWidth="460"
      @cancel="cancel"
      modalHeight="660"
    >
      <div slot="content">
        <a-form-model ref="form" :model="form" :rules="rules">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="发布方式" prop="wayType">
                <a-radio-group v-model="form.wayType">
                  <!-- :style="radioStyle" -->
                  <a-radio :value="1">系统内通知</a-radio>
                  <a-radio :value="2">短信</a-radio>
                  <a-radio :value="3">电子邮件</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="通知内容" prop="wayContent">
                <a-textarea v-model="form.wayContent" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="处置方案" prop="disposalConcept">
                <a-textarea v-model="form.disposalConcept" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="处置单位" prop="deptId">
                <a-tree-select
                  v-model="form.deptId"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="deptOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'name',
                    key: 'key',
                    value: 'key',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="责任人" prop="userIds">
                <a-select
                  show-search
                  placeholder="请输入"
                  mode="multiple"
                  :maxTagCount="2"
                  v-model="form.userIds"
                  option-filter-prop="children"
                >
                  <a-select-option v-for="item in userOptions" :key="item.key" :value="item.key">
                    {{ item.value }}
                  </a-select-option>
                </a-select>
                <!-- <UploadFile
                  :fileUrl.sync="form.qrUrl"
                  :multiple="false"
                  listType="picture-card"
                  folderName="projectCover"
                  accept=".png,.jpg,.jpeg"
                /> -->
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="submitForm(1)" :loading="loading">保存</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script>
  import { issueEvent } from '../services'
  import { getOptions, getComUserList, getDeptUserTree } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormQR',
    components: { AntModal, UploadFile },
    props: [],
    data() {
      return {
        updateInterval: null,

        loading: false,
        modalLoading: false,
        currentLength: 0,

        formTitle: '',
        type: 0,

        previewEditType: 0,
        evenId: null,
        form: {
          deptId: null,
          disposalConcept: '',
          evenId: null,
          userIds: [],
          wayContent: '',
          wayType: 1,
        },
        open: false,
        deptOptions: [],
        userOptions: [],
        rules: {
          disposalConcept: [{ required: true, message: '处置方案不能为空', trigger: 'blur' }],
          wayContent: [{ required: true, message: '通知内容不能为空', trigger: 'blur' }],

          userIds: [{ required: true, message: '通知人不能为空', trigger: 'change' }],
          wayType: [{ required: true, message: '发布方式不能为空', trigger: 'change' }],
          deptId: [{ required: true, message: '单位不能为空', trigger: 'change' }],
        },
      }
    },
    created() {
      getComUserList({ isOnlyOrg: true }).then(res => {
        this.userOptions = res?.data?.map(el => ({ value: el.name, key: el.userId }))
      })
      getDeptUserTree().then(res => {
        this.deptOptions = res?.data
        console.log('deptOptions', this.deptOptions)
      })
    },
    mounted() {},
    beforeDestroy() {
      // 组件销毁前清除定时器
      if (this.updateInterval) {
        clearInterval(this.updateInterval)
      }
    },
    methods: {
      handleInputChange(value) {
        // 更新当前输入的字符长度
        this.currentLength = this.form.qrName.length
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      remove() {
        this.$refs.tinyMceRef.remove()
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '处理反馈'
        console.log('处理反馈 666', row)
        // this.modalLoading = true
        this.evenId = row.eventId
        this.form.evenId = row.eventId
        // getQR().then(res => {
        //   if (res.code == 200) {
        //     //附件显示
        //     // this.form = { ...res.data, attaches: [res.data.qrUrl] }
        //     this.form = { ...res.data }
        //     this.currentLength = this.form.qrName.length
        //     this.modalLoading = false
        //   }
        // })
      },

      /** 提交按钮 */
      submitForm(type) {
        this.$refs.form.validate(valid => {
          if (valid) {
            // const saveForm = { qrName: this.form.qrName, qrUrl: this.form.qrUrl }
            // this.form.evenId = this.evenId
            console.log('******* 211', this.form)
            this.loading = true
            issueEvent(this.form)
              .then(response => {
                this.$message.success('事件发布成功', 3)
                this.open = false
                this.loading = false
                this.$emit('close')
                this.$emit('ok')
              })
              .catch(err => (this.loading = false))
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .character-count {
    position: relative;
    // display: block;
    width: 80px;
    float: right;
    text-align: right;
    margin-right: 20px;
    margin-top: -26px;
    // background: red;
  }

  .summary-tip {
    position: absolute;
    top: 5px;
    left: 86px;
    width: 330px;
    height: 16px;
  }
  .upload-tip {
    position: absolute;
    bottom: 5px;
    left: 16px;
    width: 420px;
    height: 16px;
    // background: yellowgreen;
  }
</style>
