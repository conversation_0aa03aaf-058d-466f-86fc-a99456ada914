<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
        <a-tab-pane key="1" tab="按类别">
          <TreeGeneral
            v-if="treeTabKey === '1'"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="treeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'category')"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="行政区划">
          <TreeGeneral
            v-if="treeTabKey === '2'"
            :key="2"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="districtTreeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'district')"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="水系编码">
          <a-input
            v-model="queryParam.riverSystemCode"
            placeholder="请输入水系编码"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="水系名称">
          <a-input
            v-model="queryParam.riverSystemName"
            placeholder="请输入水系名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
    </div>

    <FormDrawer
      v-if="showFormDrawer"
      ref="formDrawerRef"
      :districtOptions="districtTreeOptions.dataSource"
      :objectCategoryOptions="treeOptions.dataSource"
      @ok="onOperationComplete"
      @close="showFormDrawer = false"
    />
    <MonitoringIndexModal
      v-if="showMonitoringIndexModal"
      ref="paramsMonitoringIndexModalRef"
      @ok="showMonitoringIndexModal = false"
    />
  </div>
</template>

<script lang="jsx">
  import { getTreeByCode, getRiverSystemPage, deleteRiverSystem } from './services'
  import { getDistrictTree } from '@/api/common'
  import FormDrawer from './modules/FormDrawer'
  import TreeGeneral from '@/components/TreeGeneral'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import MonitoringIndexModal from '@/components/MonitoringIndex'

  export default {
    name: 'RiverSystem',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      FormDrawer,
      MonitoringIndexModal,
    },
    data() {
      return {
        objectType: undefined,
        showMonitoringIndexModal: false,
        treeTabKey: '1',
        treeOptions: {
          getDataApi: getTreeByCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
            value: 'objectCategoryId',
          },
        },
        districtTreeOptions: {
          getDataApi: getDistrictTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'districtName',
            key: 'districtCode',
            value: 'districtCode',
          },
        },
        showFormDrawer: false,

        list: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          riverSystemCode: undefined,
          riverSystemName: undefined,
          objectCategoryId: undefined,
          districtCode: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        districtTypes: {},
        columns: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '水系编码',
            field: 'riverSystemCode',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '水系名称',
            field: 'riverSystemName',
            minWidth: 180,
          },
          {
            title: '水系类别',
            field: 'objectCategoryName',
            minWidth: 140,
          },
          {
            title: '行政区划',
            field: 'districtCode',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.districtTypes[row.districtCode]?.districtName || ''
              },
            },
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 170,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleParamSet(row)}>监测指标</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      getDistrictTree().then(res => {
        this.districtTreeOptions.dataSource = res.data
        this.districtTypes = getFlatTreeMap(res.data, 'districtCode')
      })
    },
    computed: {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        // 根据树默认选中id来判断
        if (this.queryParam.objectCategoryId || this.queryParam.riverSystemCode) {
          this.getList()
        }
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getRiverSystemPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          riverSystemCode: undefined,
          riverSystemName: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.riverSystemId)
        this.names = valObj.records.map(item => item.riverSystemName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 树加载完成后
      onTreeMounted(data) {
        if (this.treeTabKey === '1') {
          this.queryParam.objectCategoryId = data[0].objectCategoryId
          this.objectType = data[0]?.objectCategoryCode?.substring(0, 2)
          this.queryParam.districtCode = ''
          this.tableTitle = data[0].objectCategoryName

          this.treeOptions.dataSource = data
        }
        if (this.treeTabKey === '2') {
          this.queryParam.objectCategoryId = ''
          this.queryParam.districtCode = data[0].districtCode
          this.tableTitle = data[0].districtName

          // this.districtTreeOptions.dataSource = data
          // this.districtTypes = getFlatTreeMap(data, 'districtCode')
        }

        this.getList()
      },
      clickTreeNode(node, type) {
        if (type === 'category') {
          this.queryParam.objectCategoryId = node.$options.propsData.eventKey
          this.objectType = node.$options.propsData?.dataRef?.objectCategoryCode?.substring(0, 2)
        }
        if (type === 'district') {
          this.queryParam.districtCode = node.$options.propsData.eventKey
        }
        this.tableTitle = node.$options.propsData.dataRef.title

        this.queryParam.pageNum = 1
        this.getList()
      },

      // 参数配置
      handleParamSet(record) {
        this.showMonitoringIndexModal = true
        let obj = {
          ...record,
          objectType: this.objectType,
          objectId: record?.riverSystemId,
        }
        this.$nextTick(() => {
          this.$refs.paramsMonitoringIndexModalRef.handleShow(obj)
        })
      },
      /* 新增 */
      handleAdd() {
        this.showFormDrawer = true
        if (this.treeTabKey === '1') {
          this.$nextTick(() =>
            this.$refs.formDrawerRef.handleAdd({ objectCategoryId: this.queryParam.objectCategoryId }, '1'),
          )
        }
        if (this.treeTabKey === '2') {
          this.$nextTick(() => this.$refs.formDrawerRef.handleAdd({ districtCode: this.queryParam.districtCode }, '2'))
        }
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const riverSystemIds = row.riverSystemId ? [row.riverSystemId] : this.ids
        const names = row.riverSystemName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteRiverSystem({ riverSystemIds: riverSystemIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
