import request from '@/utils/request'

// 水利对象分类-根据编码获取树
export function getTreeByCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'MS' },
  })
}

// 监测站点-列表分页查询
export function getSitePage(data) {
  return request({
    url: '/base/site/page',
    method: 'post',
    data,
  })
}

// 监测站点-删除
export function deleteSite(params) {
  return request({
    url: '/base/site/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 监测站点-新增
export function addSite(data) {
  return request({
    url: '/base/site/add',
    method: 'post',
    data,
  })
}

// 监测站点-更新
export function updateSite(data) {
  return request({
    url: '/base/site/update',
    method: 'post',
    data,
  })
}

// 监测站点-详情
export function getSite(params) {
  return request({
    url: '/base/site/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//其他管理对象-列表查询
export function getBaseOtherObject() {
  return request({
    url: '/base/otherObject/list',
    method: 'post',
  })
}
