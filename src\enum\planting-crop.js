// 种植作物枚举类
export const plantingCrop = [
  // 夏季作物
  {
    label: '夏季作物',
    value: 'SUMMER_CROPS',
    firstTypeCode: '1',
    children: [
      {
        label: '小麦',
        value: 'WHEAT',
        firstTypeCode: '1',
        secondTypeCode: '0',
        fieldName: 'wheat'
      },
      {
        label: '油料',
        value: 'OIL_CROPS',
        firstTypeCode: '1',
        secondTypeCode: '0',
        fieldName: 'oilCrop'
      },
      {
        label: '夏杂',
        value: 'SUMMER_MISCELLANEOUS',
        firstTypeCode: '1',
        secondTypeCode: '0',
        fieldName: 'summerMisc'
      },
      {
        label: '瓜菜',
        value: 'MELON_VEGETABLES',
        firstTypeCode: '1',
        secondTypeCode: '1',
        children: [
          {
            label: '瓜类',
            value: 'MELONS',
            firstTypeCode: '1',
            secondTypeCode: '1',
            fieldName: 'melon'
          },
          {
            label: '葵花',
            value: 'VEGETABLES',
            firstTypeCode: '1',
            secondTypeCode: '1',
            fieldName: 'sunflowerInMelon'
          },
          {
            label: '番茄',
            value: 'TOMATO',
            firstTypeCode: '1',
            secondTypeCode: '1',
            fieldName: 'tomato'
          }
        ]
      },
      {
        label: '其中：小麦间套种',
        value: 'WHEAT_CATEGORY',
        firstTypeCode: '1',
        secondTypeCode: '2',
        children: [
          {
            label: '玉米',
            value: 'CORN',
            firstTypeCode: '1',
            secondTypeCode: '2',
            fieldName: 'corn'
          },
          {
            label: '辣椒',
            value: 'CHILI',
            firstTypeCode: '1',
            secondTypeCode: '2',
            fieldName: 'pepper'
          },
          {
            label: '葵花',
            value: 'SUNFLOWER',
            firstTypeCode: '1',
            secondTypeCode: '2',
            fieldName: 'sunflowerInWheat'
          },
          {
            label: '其他',
            value: 'OTHER',
            firstTypeCode: '1',
            secondTypeCode: '2',
            fieldName: 'otherInWheat'
          }
        ]
      }
    ]
  },
  
  // 秋季作物
  {
    label: '秋季作物',
    value: 'AUTUMN_CROPS',
    firstTypeCode: '2',
    children: [
      {
        label: '玉米',
        value: 'AUTUMN_CORN',
        firstTypeCode: '2',
        secondTypeCode: '0',
        fieldName: 'autumnCorn'
      },
      {
        label: '葫芦',
        value: 'GOURD',
        firstTypeCode: '2',
        secondTypeCode: '0',
        fieldName: 'gourd'
      },
      {
        label: '辣椒',
        value: 'AUTUMN_CHILI',
        firstTypeCode: '2',
        secondTypeCode: '0',
        fieldName: 'autumnPepper'
      },
      {
        label: '脱水菜（复种）',
        value: 'DEHYDRATED_VEGETABLES',
        firstTypeCode: '2',
        secondTypeCode: '0',
        fieldName: 'dehydratedVegetables'
      },
      {
        label: '葵花',
        value: 'AUTUMN_SUNFLOWER',
        firstTypeCode: '2',
        secondTypeCode: '0',
        fieldName: 'autumnSunflower'
      },
      {
        label: '秋杂',
        value: 'AUTUMN_MISCELLANEOUS',
        firstTypeCode: '2',
        secondTypeCode: '0',
        fieldName: 'autumnMisc'
      }
    ]
  },
  
  // 林牧地
  {
    label: '林牧地',
    value: 'FOREST_PASTURE',
    firstTypeCode: '3',
    secondTypeCode: '3',
    children: [
      {
        label: '林地',
        value: 'FOREST_LAND',
        firstTypeCode: '3',
        secondTypeCode: '3',
        children: [
          {
            label: '成林',
            value: 'MATURE_FOREST',
            firstTypeCode: '3',
            secondTypeCode: '3',
            fieldName: 'matureForest'
          },
          {
            label: '幼林',
            value: 'YOUNG_FOREST',
            firstTypeCode: '3',
            secondTypeCode: '3',
            fieldName: 'youngForest'
          },
          {
            label: '果树',
            value: 'FRUIT_TREES',
            firstTypeCode: '3',
            secondTypeCode: '3',
            fieldName: 'fruitTree'
          },
          {
            label: '枸杞',
            value: 'GOJI_BERRY',
            firstTypeCode: '3',
            secondTypeCode: '3',
            fieldName: 'wolfberry'
          }
        ]
      },
      {
        label: '牧草地',
        value: 'GRASSLAND',
        firstTypeCode: '3',
        secondTypeCode: '0',
        fieldName: 'grassland'
      }
    ]
  },
  
  // 热水地
  {
    label: '热水地',
    value: 'HOT_WATER_LAND',
    firstTypeCode: '0',
    secondTypeCode: '0',
    fieldName: 'hotWaterLand'
  },
  
  // 干地
  {
    label: '干地',
    value: 'DRY_LAND',
    firstTypeCode: '0',
    secondTypeCode: '0',
    fieldName: 'dryLand'
  }
]

// 获取扁平化的作物列表（用于下拉选择等场景）
export const getFlatCropList = () => {
  const flatList = []
  
  const flatten = (arr, parentPath = '') => {
    arr.forEach((item, index) => {
      const currentPath = parentPath ? `${parentPath}.${index}` : index.toString()
      
      if (item.children) {
        // 如果有子级，继续递归
        flatten(item.children, currentPath)
      } else {
        // 没有子级，添加到扁平列表
        flatList.push({
          ...item,
          path: currentPath
        })
      }
    })
  }
  
  flatten(plantingCrop)
  return flatList
}

// 获取表格表头配置（用于复杂表格）
export const getTableHeaders = () => {
  const headers = []
  
  const buildHeaders = (arr, level = 0) => {
    arr.forEach(item => {
      const header = {
        label: item.label,
        value: item.value,
        level: level,
        hasChildren: !!item.children
      }
      
      if (item.children) {
        header.children = item.children.map(childItem => ({
          label: childItem.label,
          value: childItem.value,
          level: level + 1,
          hasChildren: !!childItem.children,
          children: childItem.children ? childItem.children.map(grandChildItem => ({
            label: grandChildItem.label,
            value: grandChildItem.value,
            level: level + 2,
            hasChildren: false
          })) : undefined
        }))
      }
      
      headers.push(header)
    })
  }
  
  buildHeaders(plantingCrop)
  return headers
}

// 根据值查找作物信息
export const findCropByValue = (value) => {
  const findInArray = (arr) => {
    for (let item of arr) {
      if (item.value === value) {
        return item
      }
      if (item.children) {
        const found = findInArray(item.children)
        if (found) return found
      }
    }
    return null
  }
  
  return findInArray(plantingCrop)
}

// 获取所有叶子节点（最底层的作物）
export const getLeafCrops = () => {
  const leafCrops = []
  
  const findLeaves = (arr) => {
    arr.forEach(item => {
      if (!item.children || item.children.length === 0) {
        leafCrops.push(item)
      } else {
        findLeaves(item.children)
      }
    })
  }
  
  findLeaves(plantingCrop)
  return leafCrops
}

// 获取所有叶子节点（最底层的作物）及其字段名映射
export const getLeafCropsWithFieldNames = () => {
  const leafCrops = []
  
  const findLeaves = (arr) => {
    arr.forEach(item => {
      if (!item.children || item.children.length === 0) {
        leafCrops.push(item)
      } else {
        findLeaves(item.children)
      }
    })
  }
  
  findLeaves(plantingCrop)
  return leafCrops
}

// 根据字段名查找作物信息
export const findCropByFieldName = (fieldName) => {
  const findInArray = (arr) => {
    for (let item of arr) {
      if (item.fieldName === fieldName) {
        return item
      }
      if (item.children) {
        const found = findInArray(item.children)
        if (found) return found
      }
    }
    return null
  }
  
  return findInArray(plantingCrop)
}

// 获取字段名到作物value的映射
export const getFieldNameToValueMap = () => {
  const map = {}
  const leafCrops = getLeafCropsWithFieldNames()
  
  leafCrops.forEach(crop => {
    if (crop.fieldName) {
      map[crop.fieldName] = crop.value
    }
  })
  
  return map
}

// 获取作物value到字段名的映射
export const getValueToFieldNameMap = () => {
  const map = {}
  const leafCrops = getLeafCropsWithFieldNames()
  
  leafCrops.forEach(crop => {
    if (crop.fieldName) {
      map[crop.value] = crop.fieldName
    }
  })
  
  return map
}

// 获取指定层级的作物
export const getCropsByLevel = (level) => {
  const crops = []
  
  const traverse = (arr, currentLevel = 0) => {
    arr.forEach(item => {
      if (currentLevel === level) {
        crops.push(item)
      }
      if (item.children && currentLevel < level) {
        traverse(item.children, currentLevel + 1)
      }
    })
  }
  
  traverse(plantingCrop)
  return crops
}

// 根据字典的 plantTypeCode 查找对应的作物信息
export const findCropByPlantTypeCode = (plantTypeCode, cropOptions) => {
  // 首先通过 plantTypeCode 在字典中查找作物名称
  const cropOption = cropOptions.find(option => option.key === plantTypeCode || option.key === plantTypeCode.toString())
  
  if (!cropOption) {
    console.warn('未在字典中找到对应的作物:', plantTypeCode)
    return null
  }
  
  const cropLabel = cropOption.value || cropOption.label
  
  // 在种植作物枚举中查找匹配的作物
  const findInArray = (arr) => {
    for (let item of arr) {
      // 直接匹配 label
      if (item.label === cropLabel) {
        return item
      }
      // 递归查找子项
      if (item.children) {
        const found = findInArray(item.children)
        if (found) return found
      }
    }
    return null
  }
  
  const matchedCrop = findInArray(plantingCrop)
  
  if (!matchedCrop) {
    console.warn('未在种植作物枚举中找到匹配的作物:', { plantTypeCode, cropLabel })
    return null
  }
  
  return matchedCrop
}

// 根据字典的 plantTypeCode 批量查找作物信息
export const findCropsByPlantTypeCodes = (plantTypeCodeList, cropOptions) => {
  if (!Array.isArray(plantTypeCodeList)) {
    return []
  }
  
  return plantTypeCodeList.map(plantTypeCode => {
    const crop = findCropByPlantTypeCode(plantTypeCode, cropOptions)
    return crop ? {
      plantTypeCode,
      cropInfo: crop
    } : {
      plantTypeCode,
      cropInfo: null
    }
  }).filter(item => item.cropInfo !== null)
}

// 测试方法 - 用于验证功能是否正常
export const testCropMatching = (testData, cropOptions) => {
  console.log('=== 测试作物匹配功能 ===')
  console.log('测试数据:', testData)
  console.log('作物字典:', cropOptions)
  
  testData.forEach(item => {
    const { plantTypeCode, firstTypeCode, secondTypeCode } = item
    console.log(`\n测试 plantTypeCode: ${plantTypeCode}`)
    
    // 使用新方法查找
    const cropInfo = findCropByPlantTypeCode(plantTypeCode, cropOptions)
    console.log('匹配结果:', cropInfo)
    
    if (cropInfo) {
      console.log(`- 作物名称: ${cropInfo.label}`)
      console.log(`- 字段名: ${cropInfo.fieldName}`)
      console.log(`- 作物值: ${cropInfo.value}`)
    } else {
      console.log('- 未找到匹配的作物')
    }
  })
  
  console.log('=== 测试完成 ===')
}

// 根据字典的 plantTypeCode、firstTypeCode 和 secondTypeCode 精确查找作物
export const findCropByTypeCodes = (plantTypeCode, firstTypeCode, secondTypeCode, cropOptions) => {
  // 首先通过 plantTypeCode 在字典中查找作物名称
  const cropOption = cropOptions.find(option => option.key === plantTypeCode || option.key === plantTypeCode.toString())
  
  if (!cropOption) {
    console.warn('未在字典中找到对应的作物:', plantTypeCode)
    return null
  }
  
  const cropLabel = cropOption.value || cropOption.label
  
  // 在种植作物枚举中查找匹配的作物，同时考虑分类代码
  const findInArray = (arr) => {
    for (let item of arr) {
      // 检查是否匹配 label 和分类代码
      if (item.label === cropLabel) {
        // 检查分类代码是否匹配
        const itemFirstTypeCode = item.firstTypeCode !== undefined ? item.firstTypeCode.toString() : '0'
        const itemSecondTypeCode = item.secondTypeCode !== undefined ? item.secondTypeCode.toString() : '0'
        const targetFirstTypeCode = firstTypeCode !== undefined ? firstTypeCode.toString() : '0'
        const targetSecondTypeCode = secondTypeCode !== undefined ? secondTypeCode.toString() : '0'
        
        // 精确匹配分类代码
        if (itemFirstTypeCode === targetFirstTypeCode && itemSecondTypeCode === targetSecondTypeCode) {
          return item
        }
      }
      
      // 递归查找子项
      if (item.children) {
        const found = findInArray(item.children)
        if (found) return found
      }
    }
    return null
  }
  
  const matchedCrop = findInArray(plantingCrop)
  
  if (!matchedCrop) {
    console.warn('未在种植作物枚举中找到匹配的作物:', { 
      plantTypeCode, 
      firstTypeCode, 
      secondTypeCode, 
      cropLabel 
    })
    return null
  }
  
  return matchedCrop
}

// 测试新的精确匹配方法
export const testPreciseCropMatching = (testData, cropOptions) => {
  console.log('=== 测试精确作物匹配功能 ===')
  console.log('测试数据:', testData)
  console.log('作物字典:', cropOptions)
  
  testData.forEach(item => {
    const { plantTypeCode, firstTypeCode, secondTypeCode, plantAmount } = item
    console.log(`\n测试 plantTypeCode: ${plantTypeCode}, firstTypeCode: ${firstTypeCode}, secondTypeCode: ${secondTypeCode}`)
    
    // 使用新的精确匹配方法
    const cropInfo = findCropByTypeCodes(plantTypeCode, firstTypeCode, secondTypeCode, cropOptions)
    console.log('精确匹配结果:', cropInfo)
    
    if (cropInfo) {
      console.log(`- 作物名称: ${cropInfo.label}`)
      console.log(`- 字段名: ${cropInfo.fieldName}`)
      console.log(`- 作物值: ${cropInfo.value}`)
      console.log(`- 分类代码: firstTypeCode=${cropInfo.firstTypeCode}, secondTypeCode=${cropInfo.secondTypeCode}`)
      console.log(`- 种植面积: ${plantAmount}`)
    } else {
      console.log('- 未找到匹配的作物')
    }
  })
  
  console.log('=== 精确匹配测试完成 ===')
}