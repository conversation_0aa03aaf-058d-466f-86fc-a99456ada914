<template>
  <div class="icc-player-wrapper">
    <video
      class="video-box"
      :id="videoHlsId"
      :width="width"
      :height="height"
      controls
      :preload="preload"
      :autoplay="autoplay"
    ></video>
    <div class="steering-wheel" v-if="cameraUrl && isShowDirection">
      <div class="btn-box">
        <a-icon type="up" class="btn-up btn-common" @click="onDirection('1')" />
        <a-icon type="down" class="btn-down btn-common" @click="onDirection('2')" />
        <a-icon type="left" class="btn-left btn-common" @click="onDirection('3')" />
        <a-icon type="right" class="btn-right btn-common" @click="onDirection('4')" />
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  export default {
    name: 'ICCHlsPlayer',
    props: {
      cameraUrl: {
        type: String,
        default: '',
      },
      width: {
        type: String,
        default: '400px',
      },
      height: {
        type: String,
        default: '300px',
      },
      videoHlsId: {
        type: String,
        default: '',
      },
      isShowDirection: {
        type: Boolean,
        default: false,
      },
      preload: {
        type: String,
        default: 'auto',
      },
      autoplay: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        hlsPlayer: null,
        active: '',
      }
    },
    mounted() {
      this.getHlsPlayer()
    },
    methods: {
      onDirection(direction) {
        this.$emit('setDirection', direction)
      },
      getHlsPlayer() {
        //先触发销毁
        if (this.hlsPlayer != null) {
          this.hlsPlayer.destroy()
        }
        //创建播放
        let video = document.getElementById(this.videoHlsId)
        if (Hls.isSupported()) {
          this.hlsPlayer = new Hls()
          this.hlsPlayer.loadSource(this.cameraUrl)
          this.hlsPlayer.attachMedia(video)
          this.hlsPlayer.on(Hls.Events.MANIFEST_PARSED, function () {
            video.play()
          })
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          // 如果支持原生播放
          video.src = this.cameraUrl
        }
      },
    },
    destroyed() {
      if (this.hlsPlayer != null) {
        this.hlsPlayer.destroy()
      }
    },
  }
</script>
<style lang="less" scoped>
  .steering-wheel {
    width: 244px;
    height: 244px;
    position: absolute;
    z-index: 999;
    right: 20px;
    top: 50%;
    width: 120px;
    height: 120px;
    border-radius: 120px;
    border: 3px solid #ffffff;

    .btn-box {
      width: 100%;
      height: 100%;
      position: relative;
      .btn-common {
        position: absolute;
        width: 20px;
        height: 20px;
        color: #fff;
        font-size: 30px;
        :hover,
        :active {
          color: #4cdeff;
        }
      }
      .btn-up {
        left: 48%;
        top: 0px;
        transform: translate(-50%);
      }
      .btn-down {
        left: 51%;
        bottom: 10%;
        transform: translate(-50%);
      }
      .btn-left {
        left: 0;
        top: 48%;
        transform: translateY(-50%);
      }
      .btn-right {
        right: 9%;
        top: 48%;
        transform: translateY(-50%);
      }
    }
  }
</style>
