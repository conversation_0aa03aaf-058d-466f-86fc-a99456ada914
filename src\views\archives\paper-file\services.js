import request from '@/utils/request'

// 工程管理_维修养护_维养计划-列表分页查询
export function getArchivesPage(data) {
  return request({
    url: '/custom/archives/page',
    method: 'post',
    data,
  })
}
// 增加
export function addArchives(data) {
  return request({
    url: '/custom/archives/add',
    method: 'post',
    data,
  })
}
// 详情
export function getArchivesById(params) {
  return request({
    url: '/custom/archives/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editArchives(data) {
  return request({
    url: '/custom/archives/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteArchives(params) {
  return request({
    url: '/custom/archives/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

export function getProjectListByPropertyId(params) {
  return request({
    url: '/custom/maintenance/getProjectList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
