import request from '@/utils/request'

// 分页查询列表
export function getPlanMsgPage(data) {
  return request({
    url: '/model/plan/report/pageDetails',
    method: 'post',
    data,
  })
}

// 新增-查询用水计划信息
export function getReportFlows(data) {
  return request({
    url: '/model/plan/msg/getReportFlows',
    method: 'post',
    data,
  })
}

// 新增
export function addPlanMsg(data) {
  return request({
    url: '/model/plan/msg/add',
    method: 'post',
    data,
  })
}

// 删除
export function deletePlanMsg(params) {
  return request({
    url: '/model/plan/msg/delete',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 查看
export function getPlanMsgById(params) {
  return request({
    url: '/model/plan/msg/get',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 详情
export function getPlanMsgDetails(params) {
  return request({
    url: '/model/plan/report/get',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 详情
export function getPlanReportDetails(params) {
  return request({
    url: '/model/plan/report/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
