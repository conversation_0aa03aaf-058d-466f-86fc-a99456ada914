<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="500"
    @cancel="cancel"
    modalHeight="430"
  >
    <div slot="content">
      <a-form-model ref="form" :model="briefForm" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="简报名称" prop="reportName">
          <a-input allowClear v-model="briefForm.reportName" :maxLength="15" placeholder="请输入" />
        </a-form-model-item>
        <a-form-model-item label="快报期数" prop="issue">
          <a-row class="form-item">
            <a-col :span="8">
              <a-input allowClear v-model="briefForm.year" :maxLength="15" placeholder="请输入" type="number" />
            </a-col>
            <a-col :span="1" class="text">年</a-col>
            <a-col :span="8">
              <a-input allowClear v-model="briefForm.issue" :maxLength="15" placeholder="请输入" />
            </a-col>
            <a-col :span="1" class="text">期</a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item label="统计时间" prop="statStartTime">
          <a-range-picker
            :show-time="{ format: 'HH' }"
            format="YYYY-MM-DD HH"
            formatValue="YYYY-MM-DD HH"
            :value="rangeValue"
            :placeholder="['开始时间', '结束时间']"
            @change="onRangeChange"
            @ok="onOkRange"
          />
        </a-form-model-item>
        <a-form-model-item label="简报模板" prop="reportTemplateId">
          <a-select allowClear v-model="briefForm.reportTemplateId" placeholder="请选择">
            <a-select-option
              v-for="(item, index) in briefTemplateList"
              :key="item.reportTemplateId"
              :value="item.reportTemplateId"
            >
              {{ item.reportTemplateName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <!-- <a-button type="primary" @click="previewForm">预览</a-button> -->
      <a-button type="primary" @click="saveForm" :loading="loading">确定</a-button>
      <a-button type="primary" @click="releaseForm" v-if="briefForm.reportId" :loading="createLoading">生成</a-button>
      <a-button v-else type="primary" @click="generateForm" :loading="createLoading">生成</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import {
    addBriefingPreview,
    addMyBriefingSave,
    addBriefingGenerate,
    editBriefingSave,
    editBriefingRelease,
  } from '../services'
  import { getTemplateList } from '@/views/briefing/templateConfig/services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormMyBriefing',
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        createLoading: false,
        labelCol: { span: 5 },
        wrapperCol: { span: 19 },
        formTitle: '',
        rangeValue: [],
        briefTemplateList: [],
        // 表单参数
        briefForm: {
          reportName: null,
          year: moment().format('YYYY'),
          issue: 1,
          statStartTime: null,
          statEndTime: null,
          reportTemplateId: null,
        },
        open: false,
        rules: {
          reportName: [{ required: true, message: '简报名称不能为空', trigger: 'blur' }],
          issue: [{ required: true, message: '快报期数不能为空', trigger: 'blur' }],
          statStartTime: [{ required: true, message: '统计时间不能为空', trigger: 'blur' }],
          reportTemplateId: [{ required: true, message: '简报模板不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {
      getTemplateList({ pageNum: 1, pageSize: 9999 }).then(res => {
        if (res.code == 200) {
          this.briefTemplateList = res.data?.data
        }
      })
    },
    methods: {
      onRangeChange(value, dateString) {
        this.rangeValue = value
        if (dateString.length == 0) {
          return
        }
        this.briefForm.statStartTime = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD HH') + ':00:00' : null
        this.briefForm.statEndTime = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD HH') + ':59:59' : null
      },
      onOkRange(value) {
        console.log('onOk: ', value)
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 新增按钮操作 */
      handleMyBriefing(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.formTitle = '修改'
          this.briefForm = row
          this.rangeValue = [row.statStartTime.substring(0, 13), row.statEndTime.substring(0, 13)]
        }
      },
      /** 预览 */
      previewForm() {},
      saveForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.briefForm.reportId == null) {
              addMyBriefingSave(this.briefForm)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editBriefingSave(this.briefForm)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
      generateForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            addBriefingGenerate(this.briefForm).then(res => {
              if (res.code == 200) {
                this.$message.success('成功', 3)
                this.open = false
                this.$emit('ok')
              }
            })
          }
        })
      },
      releaseForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            editBriefingRelease(this.briefForm).then(res => {
              if (res.code == 200) {
                this.$message.success('成功', 3)
                this.open = false
                this.$emit('ok')
              }
            })
          }
        })
      },
      moment,
      range(start, end) {
        const result = []
        for (let i = start; i < end; i++) {
          result.push(i)
        }
        return result
      },
      disabledDate(current) {
        return current && current < moment().endOf('day')
      },

      disabledRangeTime(_, type) {
        if (type === 'start') {
          return {
            disabledHours: () => this.range(0, 60).splice(4, 20),
            disabledMinutes: () => this.range(30, 60),
            disabledSeconds: () => [55, 56],
          }
        }
        return {
          disabledHours: () => this.range(0, 60).splice(20, 4),
          disabledMinutes: () => this.range(0, 31),
          disabledSeconds: () => [55, 56],
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  .modal-content {
    .form-item {
      line-height: 32px;
    }
    .text {
      margin: 0 10px;
    }
  }
</style>
