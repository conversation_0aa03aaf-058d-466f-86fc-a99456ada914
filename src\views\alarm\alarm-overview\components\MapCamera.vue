<template>
  <div class="map-box">
    <MapBox @onMapMounted="onMapMounted" />

    <div style="position: absolute; right: 16px; bottom: 16px">
      <MapStyle v-if="!!mapIns" :mapIns="mapIns" activeStyle="卫星图" ref="mapStyleRef" />
    </div>

    <PlayCamera
      v-if="showPlayCamera"
      :eventTypeOptions="eventTypeOptions"
      :warnLevelOptions="warnLevelOptions"
      ref="playCameraRef"
    />
  </div>
</template>

<script lang="jsx">
  import mapboxgl from 'mapbox-gl'
  import { boundFlatLngLats } from '@/utils/mapBounds'
  import MapBox from '@/components/MapBox'
  import cameraImg from '@/assets/images/patrol-camera.png'
  import alarmCameraImg from '@/assets/images/alarm-camera.png'
  import { clearSourceAndLayer } from '@/utils/mapUtils'
  import MapStyle from '@/components/MapBox/MapStyle.vue'
  import { getCamera } from '@/views/basic/camera/services.js'
  import PlayCamera from '../modules/PlayCamera.vue'

  export default {
    name: 'MapModel',
    props: ['list', 'queryCameraId', 'eventTypeOptions', 'warnLevelOptions'],
    components: { MapBox, MapStyle, PlayCamera },
    data() {
      return {
        mapIns: null,
        geojson84: {},
        pointLayerIns: null,
        sourceIns: null,
        markersIns: [],
        hasMarkers: false,

        showPlayCamera: false,
      }
    },
    created() {},
    mounted() {},
    computed: {},
    watch: {
      list: {
        handler(newVal, oldVal) {
          this.markersIns.forEach(ele => {
            ele.remove()
          })
          this.mapBounds(newVal)
        },
        deep: true,
      },
      queryCameraId: {
        handler(newVal) {
          if (newVal == undefined) {
            this.mapIns.flyTo({
              center: [107.384718, 40.744327],
              zoom: 11,
            })
            return
          }
          this.amplifyCameraPoint(newVal)
        },
        deep: true,
      },
    },
    methods: {
      amplifyCameraPoint(id) {
        getCamera({ cameraId: id }).then(res => {
          this.mapIns.flyTo({
            center: [+res?.data?.longitude, +res?.data?.latitude],
            zoom: 16,
          })
        })
      },
      onMapMounted(mapIns) {
        this.mapIns = mapIns
        this.mapBounds(this.list)
      },

      setMarkers(list) {
        this.geojson84 = {
          type: 'FeatureCollection',
          features: list
            .filter(el => +el.longitude && +el.latitude)
            .map(item => {
              return {
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [+item.longitude, +item.latitude],
                },
                properties: {
                  ...item,
                  name: item.projectName,
                },
              }
            }),
        }
        this.geojson84.features.forEach((item, index) => {
          const el = document.createElement('div')
          el.innerHTML = `
              <div style="display:flex; align-items:center;">
                <div style="width:40px; height:47px;margin:13px -6px 0 0; background-size:100%; background-image:url('${alarmCameraImg}');"></div>
                  <div style="height:28px; line-height:28px; color:#1D2129; font-size:14px;border-radius: 0px 2px 2px 0px; background:#fff; padding:0 8px; box-shadow:0px 4px 10px rgba(22, 93, 255, .4)">${
                    item.properties.cameraName
                  }</div>
              </div>`

          // 造假数据
          // if ([262, 116].includes(item.properties.cameraId)) {
          //   el.innerHTML = `
          //     <div style="display:flex; align-items:center;">
          //       <div style="width:40px; height:47px;margin:13px -6px 0 0; background-size:100%; background-image:url('${alarmCameraImg}');"></div>
          //         <div style="height:28px; line-height:28px; color:#1D2129; font-size:14px;border-radius: 0px 2px 2px 0px; background:#fff; padding:0 8px; box-shadow:0px 4px 10px rgba(22, 93, 255, .4)">${
          //           item.properties.cameraName
          //         }</div>
          //     </div>`
          // }

          const popup = new mapboxgl.Popup({
            closeButton: false,
            closeOnClick: false,
            offset: 40,
          })

          el.addEventListener('mouseenter', () => {
            console.log('🚀~item~>', item)
            const html = `<div style="line-height: 26px;">
              <div>告警名称：${item?.properties?.eventName || '--'}</div>
              <div>类型：${this.eventTypeOptions?.find(el => el.key == item?.properties?.eventType)?.value || '--'}</div>
              <div>告警级别：${this.warnLevelOptions?.find(el => el.key == item?.properties?.warnLevel)?.value || '--'}</div>
              <div>事件描述：${item?.properties?.content || '--'}</div>
              <div>异常点：${item?.properties?.cameraName || '--'}</div>
            </div>`
            const coordinates = item.geometry.coordinates
            popup.setLngLat(coordinates).setHTML(html).addTo(this.mapIns)
          })

          el.addEventListener('mouseleave', () => {
            popup.remove()
          })

          el.addEventListener('click', () => {
            popup.remove()
            this.showPlayCamera = true

            this.$nextTick(() => this.$refs.playCameraRef.handleEventDetails(item.properties))

            this.$listeners.clickItem && this.$listeners.clickItem(item.properties)
          })

          let markIns = new mapboxgl.Marker({ anchor: 'bottom-left', offset: [-19, -9], element: el })
          markIns.setLngLat(item.geometry.coordinates).addTo(this.mapIns)

          this.markersIns.push(markIns)
        })
        this.hasMarkers = true
      },

      mapBounds(newList) {
        if (newList?.length && !!this.mapIns) {
          const arr = newList.filter(el => +el.longitude && +el.latitude).map(el => [el.longitude, el.latitude])

          if (newList?.length == 1) {
            this.mapIns.flyTo({
              center: arr?.[0],
              zoom: 12,
            })
          } else {
            // 地图自动缩放偏移
            boundFlatLngLats(arr, this.mapIns)
          }

          this.setMarkers(newList)
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .map-box {
    margin: 0 16px 16px;
    height: calc(100% - 60px);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
  }
</style>
