<template>
  <!-- :closable="false" -->
  <a-drawer
    width="440px"
    :title="formTitle"
    :label-col="4"
    :wrapper-col="14"
    :visible="open"
    :body-style="{ height: 'calc(100vh - 100px)', overflow: 'auto' }"
    @close="cancel"
  >
    <!-- :rules="rules" -->
    <a-form-model ref="form" :model="form">
      <!-- :delay="delayTime" -->
      <a-spin :spinning="spinning" tip="Loading...">
        <div class="notice">
          <div v-if="appId == 'base' && waterList.length > 0">
            <div class="notice-content" v-for="(item, index) in waterList" :key="index">
              <div class="notice-item-title">
                <div class="icon icon-water"></div>
                <!-- {{ item.msgName }} -->
                <span class="text-overflow" style="width: 70%" :title="item.msgName">
                  {{ item.msgName }}
                </span>
                <div class="more" @click="viewDetail(item, 'water')">查看</div>
              </div>
              <div style="display: flex; flex-wrap: wrap">
                <div class="notice-item" style="width: 50%">
                  <div class="label">下发人：</div>
                  <div class="value">{{ item.sendUserName || '-' }}</div>
                </div>
                <div class="notice-item" style="width: 50%">
                  <div class="label">下发时间：</div>
                  <div class="value">{{ item.sendDate || '-' }}</div>
                </div>
                <div class="notice-item" style="width: 100%">
                  <div class="label">配水时间段：</div>
                  <div class="value">{{ item.planStartDate }} 至 {{ item.planEndDate }}</div>
                </div>
                <div class="notice-item" style="width: 100%">
                  <div class="label">通知内容：</div>
                  <div class="value text-overflow" :title="item.sendContent">{{ item.sendContent || '-' }}</div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="appId == 'base' && schemaList.length > 0">
            <div class="notice-content" v-for="(item, index) in schemaList" :key="index">
              <div class="notice-item-title">
                <div class="icon icon-schema"></div>
                <span class="text-overflow" style="width: 70%" :title="item.schemaName">
                  {{ item.schemaName || '-' }}
                </span>
                <div class="more" @click="viewDetail(item, 'schema')">查看</div>
              </div>
              <div style="display: flex; flex-wrap: wrap">
                <div class="notice-item">
                  <div class="label">下发人：</div>
                  <div class="value">{{ item.sendUserName || '-' }}</div>
                </div>
                <div class="notice-item" style="margin-left: auto">
                  <div class="label">下发时间：</div>
                  <div class="value">{{ item.sendDate || '-' }}</div>
                </div>
                <div class="notice-item" style="width: 100%">
                  <div class="label">调度时间段：</div>
                  <div class="value">{{ item.schemaStartDate }} 至 {{ item.schemaEndDate }}</div>
                </div>
                <div class="notice-item">
                  <div class="label">通知内容：</div>
                  <div class="value text-overflow" :title="item.sendContent">{{ item.sendContent || '-' }}</div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="appId == 'base' && instructList.length > 0">
            <div class="notice-content" v-for="(item, index) in instructList" :key="index">
              <div class="notice-item-title">
                <div class="icon icon-schema"></div>
                <span class="text-overflow" style="width: 70%" :title="item.instructName">
                  {{ item.instructName || '-' }}
                </span>
                <div class="more" @click="viewDetail(item, 'instruct')">查看</div>
              </div>
              <div style="display: flex; flex-wrap: wrap">
                <div class="notice-item" style="width: 50%">
                  <div class="label">开闸时间：</div>
                  <div class="value">{{ item.schemaStartDate || '-' }}</div>
                </div>
                <div class="notice-item" style="width: 50%">
                  <div class="label">调度流量：</div>
                  <div class="value">{{ item.instructFlow || '-' }}</div>
                </div>
                <div class="notice-item" style="width: 50%">
                  <div class="label">关闸时间：</div>
                  <div class="value">{{ item.schemaEndDate || '-' }}</div>
                </div>

                <div class="notice-item" style="width: 50%">
                  <div class="label">调度工程：</div>
                  <div class="value">{{ item.projectId || '-' }}</div>
                </div>
                <div class="notice-item" style="width: 100%">
                  <div class="label">通知内容：</div>
                  <div class="value text-overflow" :title="item.sendContent">{{ item.sendContent || '-' }}</div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="appId == 'patrol' && instructList.length > 0">
            <div class="notice-content" v-for="(item, index) in eventList" :key="index">
              <div class="notice-item-title">
                <div class="icon icon-event"></div>
                <span style="width: 40px">
                  {{ warnLevelOptions?.find(el => el.key == item.warnLevel)?.value || '-' }}
                </span>
                -
                <span class="text-overflow" style="width: 65%; margin-left: 5px" :title="item.eventName">
                  {{ item.eventName }}
                </span>
                <div class="more" @click="viewDetail(item, 'event')">查看</div>
              </div>
              <div style="display: flex; flex-wrap: wrap">
                <div class="notice-item" style="width: 50%">
                  <div class="label" style="width: 56px">异常点：</div>
                  <div class="value text-overflow" :title="item.cameraName">{{ item.cameraName || '-' }}</div>
                </div>
                <div class="notice-item" style="width: 50%">
                  <div class="label">事件类型：</div>
                  <div class="value">{{ eventTypeOptions?.find(el => el.key == item.eventType)?.value || '-' }}</div>
                </div>
                <div class="notice-item" style="width: 50%">
                  <div class="label">上报人：</div>
                  <div class="value">{{ item.createdUserName || '-' }}</div>
                </div>
                <div class="notice-item" style="width: 50%">
                  <div class="label">上报时间：</div>
                  <div class="value">{{ item.createdTime || '-' }}</div>
                </div>
                <div class="notice-item" style="width: 100%">
                  <div class="label">通知内容：</div>
                  <div class="value text-overflow" :title="item.content">{{ item.content || '-' }}</div>
                </div>
                <div class="notice-item">
                  <div class="label">处置方案：</div>
                  <div class="value text-overflow" :title="item.disposalConcept">{{ item.disposalConcept || '-' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-spin>
      <div class="bottom-control">
        <a-space>
          <a-button type="dashed" @click="cancel">取消</a-button>
        </a-space>
      </div>
    </a-form-model>
  </a-drawer>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import { mapGetters } from 'vuex'
  import { getEventPage } from '@/views/alarm/alarm-event/services'
  import { getGenerationWater } from '@/views/water-distribution-plan/generation-water-distribution/services'
  import { getSchemaPage } from '@/views/dispatch/dispatch-case/services'
  import { getSchemaInstructPage } from '@/views/dispatch/instruct/services'
  import { getOptions } from '@/api/common'

  export default {
    name: 'Notice',
    components: {
      AntModal,
    },
    data() {
      return {
        appId: this.GLOBAL.APPID,
        eventTypeOptions: [],
        warnLevelOptions: [],
        spinning: false,
        form: {},
        loading: false,
        formTitle: '',
        open: false,
        eventList: [],
        waterList: [],
        schemaList: [],
        instructList: [],
      }
    },
    filters: {},
    created() {
      // let appInfo = JSON.parse(localStorage.getItem('user'))
      getOptions('eventType').then(res => {
        this.eventTypeOptions = res.data
      })
      getOptions('warnLevel').then(res => {
        this.warnLevelOptions = res.data
      })
      this.init()
    },
    computed: {},
    watch: {},
    methods: {
      async init() {
        //已发布
        const event = await getEventPage({
          eventName: '',
          eventType: '',
          isIssue: 1,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          projectName: '',
          sort: [],
          statues: [],
          status: undefined,
          warnLevel: '',
        })
        //已下发
        const water = await getGenerationWater({
          isSend: 1,
          msgName: '',
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          planEndDate: '',
          planStartDate: '',
          planType: '',
          sort: [],
        })
        const schema = await getSchemaPage({
          dateRange: [],
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          sort: [],
          status: 1,
          state: null,
        })
        const instruct = await getSchemaInstructPage({
          dateRange: [],
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          sort: [],
          status: 1,
        })
        this.eventList = event.data.data
        this.waterList = water.data.data
        this.schemaList = schema.data.data
        this.instructList = instruct.data.data
        // console.log('event', event)
        // console.log('water', water)
        // console.log('schema', schema)
        // console.log('instruct', instruct)
      },

      viewDetail(item, type) {
        if (type == 'event') {
          // this.$route.push({
          //   name: 'NoticeDetail',
          // })
          this.$router.push({
            path: '/abnormal-alarm/alarm-event',
          })
        } else if (type == 'water') {
          this.$router.push({
            path: '/water-distribution-plan/generation-water-distribution',
          })
        } else if (type == 'schema') {
          this.$router.push({
            path: '/schedule/dispatch-case',
          })
        } else if (type == 'instruct') {
          this.$router.push({
            path: '/schedule/dispatch-instruct',
            // query: {
            //   id: item.id,
            // },
          })
        }
        this.open = false
        this.$emit('close')
      },
      cancel() {
        this.open = false
        this.$emit('close')
      },
      showVersion() {
        this.open = true
        this.formTitle = '消息中心'
      },
    },
  }
</script>
<style lang="less">
  ::v-deep(.ant-drawer-header) {
    padding: 5px 0 0 10px !important;
    background:
      linear-gradient(180deg, rgba(172, 206, 238, 0.44) 0%, rgba(195, 228, 245, 0) 98%),
      radial-gradient(NaN 113% at -21% 16%, rgba(0, 157, 255, 0.048) 0%, rgba(19, 164, 254, 0) 100%), #ffffff !important;
    border-radius: 0px 0px 0px 0px !important;
    border: 1px solid transparent !important;
  }
  .notice {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
  }
  .notice-content {
    display: flex;
    flex-direction: column;
    // height: 160px;
    width: 100%;
    padding: 10px;
    background: rgba(133, 194, 251, 0.4);
    border: 1px solid #85c1fb;
    margin: 10px 0;

    border-radius: 8px;
    background: #f7f8fa;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;
    .notice-item-title {
      display: flex;
      color: #3d3d3d;
      font-size: 16px;
      font-weight: 600;
      // margin-bottom: 10px;
      // margin-top: 8px;
    }

    .more {
      // border-radius: 6px;
      // padding: 6px 10px;
      // width: 60px;
      margin-left: auto;
      // text-align: center;
      // font-size: 14px;
      // color: #fff;
      cursor: pointer;
      // background: #1677ff;

      width: 52px;
      height: 26px;
      background: #e8f3ff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #94bfff;

      font-weight: 400;
      font-size: 14px;
      color: #1664ff;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
  .notice-item {
    display: flex;
    margin-top: 10px;
    .label {
      color: #4e5969;
    }
    .value {
      text-align: left;
      color: #1d2129;
    }
  }
  // .notice-item:nth-child(1) {
  //   margin-top: 0;
  // }
  .text-overflow {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1; //设置行数
    overflow: hidden; //超出隐藏
  }
  .icon {
    width: 20px;
    height: 20px;
    margin: 2px 10px 0 0;
  }
  .icon-schema {
    background: url('~@/assets/images/notice-schema.png') no-repeat;
    background-size: 100% 100%;
  }
  .icon-water {
    background: url('~@/assets/images/notice-water.png') no-repeat;
    background-size: 100% 100%;
  }
  .icon-event {
    background: url('~@/assets/images/notice-event.png') no-repeat;
    background-size: 100% 100%;
  }
</style>
