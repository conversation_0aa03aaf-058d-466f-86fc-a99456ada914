<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
  >
    <a-form :form="form" layout="vertical">
      <!-- 基本信息部分 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="年份">
              <a-date-picker
                mode="year"
                format="YYYY"
                v-decorator="[
                  'irriYear',
                  {
                    rules: [{ required: true, message: '请选择年份' }],
                  },
                ]"
                placeholder="请选择年份"
                style="width: 30%"
                :open="yearShow"
                @openChange="openChange"
                @panelChange="panelChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 灌溉轮次及作物部分 -->
      <div class="form-section">
        <h3 class="section-title">灌溉轮次及作物</h3>
        
        <!-- 春夏灌 -->
        <div class="irrigation-item">
          <h4 class="irrigation-title">春夏灌</h4>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="时间选择">
                <a-range-picker
                  v-decorator="[
                    'springSummerTimeRange',
                    {
                      rules: [
                        { required: true, message: '请选择春夏灌时间段' },
                        { validator: this.validateSpringSummerTimeRange }
                      ],
                    },
                  ]"
                  style="width: 100%"
                  :placeholder="['开始时间', '结束时间']"
                  :disabledDate="(current) => getSpringSummerDisabledDate(current)"
                  :disabled="isTimeRangeDisabled('springSummer')"
                />
                <div v-if="isTimeRangeDisabled('springSummer')" class="time-disabled-tip">
                  <a-icon type="info-circle" style="color: #faad14; margin-right: 4px;" />
                  开始时间早于当前时间，不可修改
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="种植作物">
                <a-select
                  mode="multiple"
                  v-decorator="[
                    'springSummerCrops',
                    {
                      rules: [{ required: true, message: '请选择种植作物' }],
                    },
                  ]"
                  placeholder="请选择种植作物"
                  style="width: 100%"
                >
                  <a-select-option v-for="crop in cropOptions" :key="crop.value" :value="crop.value">
                    {{ crop.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 秋灌 -->
        <div class="irrigation-item">
          <h4 class="irrigation-title">秋灌</h4>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="时间选择">
                <a-range-picker
                  v-decorator="[
                    'autumnIrrigationTimeRange',
                    {
                      rules: [
                        { required: true, message: '请选择秋灌时间段' },
                        { validator: this.validateAutumnIrrigationTimeRange }
                      ],
                    },
                  ]"
                  style="width: 100%"
                  :placeholder="['开始时间', '结束时间']"
                  :disabledDate="(current) => getAutumnIrrigationDisabledDate(current)"
                  :disabled="isTimeRangeDisabled('autumnIrrigation')"
                />
                <div v-if="isTimeRangeDisabled('autumnIrrigation')" class="time-disabled-tip">
                  <a-icon type="info-circle" style="color: #faad14; margin-right: 4px;" />
                  开始时间早于当前时间，不可修改
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="种植作物">
                <a-select
                  mode="multiple"
                  v-decorator="[
                    'autumnIrrigationCrops',
                    {
                      rules: [{ required: true, message: '请选择种植作物' }],
                    },
                  ]"
                  placeholder="请选择种植作物"
                  style="width: 100%"
                >
                  <a-select-option v-for="crop in cropOptions" :key="crop.value" :value="crop.value">
                    {{ crop.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 秋浇 -->
        <div class="irrigation-item">
          <h4 class="irrigation-title">秋浇</h4>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="时间选择">
                <a-range-picker
                  v-decorator="[
                    'autumnWateringTimeRange',
                    {
                      rules: [
                        { required: true, message: '请选择秋浇时间段' },
                        { validator: this.validateAutumnWateringTimeRange }
                      ],
                    },
                  ]"
                  style="width: 100%"
                  :placeholder="['开始时间', '结束时间']"
                  :disabledDate="(current) => getAutumnWateringDisabledDate(current)"
                  :disabled="isTimeRangeDisabled('autumnWatering')"
                />
                <div v-if="isTimeRangeDisabled('autumnWatering')" class="time-disabled-tip">
                  <a-icon type="info-circle" style="color: #faad14; margin-right: 4px;" />
                  开始时间早于当前时间，不可修改
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="种植作物">
                <a-select
                  mode="multiple"
                  v-decorator="[
                    'autumnWateringCrops',
                    {
                      rules: [{ required: true, message: '请选择种植作物' }],
                    },
                  ]"
                  placeholder="请选择种植作物"
                  style="width: 100%"
                >
                  <a-select-option v-for="crop in cropOptions" :key="crop.value" :value="crop.value">
                    {{ crop.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
    </a-form>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { addIrrigationSetting, updateIrrigationSetting, getIrrigationSettingById } from '../service'
import { getOptions } from '@/api/common'

export default {
  name: 'FormDrawer',
  data() {
    return {
      title: '新增',
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      yearShow: false,
      
      // 作物选项 - 从接口动态获取
      cropOptions: [],
      
      // 当前编辑的记录
      currentRecord: null,
      
      // 选择的年份，用于限制时间范围
      selectedYear: null,
    }
  },
  created() {
    this.loadCropOptions()
  },
  mounted() {
    // 监听年份字段的变化
    this.$watch(() => {
      return this.form.getFieldValue('irriYear')
    }, (newValue) => {
      if (newValue && moment.isMoment(newValue)) {
        const year = newValue.year()
        if (this.selectedYear !== year) {
          this.selectedYear = year
          // 清空已选择的时间范围
          this.form.setFieldsValue({
            springSummerTimeRange: undefined,
            autumnIrrigationTimeRange: undefined,
            autumnWateringTimeRange: undefined,
          })
        }
      } else if (!newValue) {
        this.selectedYear = null
      }
    }, { immediate: true })
  },
  methods: {
    // 加载作物选项
    loadCropOptions() {
      getOptions('crop').then(response => {
        if (response.code === 200 && response.data) {
          this.cropOptions = response.data.filter(
            item => item.key !== '13' && item.key !== '14' && item.key !== '15' && item.key !== '16' && item.key !== '17' && item.key !== '18' && item.key !== '19' && item.key !== '20'
          ).map(item => ({
            value: item.key,
            label: item.value
          }))
        } else {
          console.error('获取作物选项失败:', response.message)
          this.$message.error('获取作物选项失败')
        }
      }).catch(error => {
        console.error('获取作物选项失败:', error)
        this.$message.error('获取作物选项失败')
      })
    },
    
    // 新增
    handleAdd() {
      this.title = '新增'
      this.visible = true
      this.currentRecord = null
      this.selectedYear = null // 重置选择的年份
      this.$nextTick(() => {
        this.form.resetFields()
      })
    },
    
    // 编辑
    handleUpdate(record) {
      this.title = '编辑'
      this.visible = true
      this.currentRecord = record
      
      // 先重置表单
      this.$nextTick(() => {
        this.form.resetFields()
      })
      
      // 重置选择的年份
      this.selectedYear = null
      
      // 根据id查询详情
      if (record && record.id) {
        this.confirmLoading = true
        getIrrigationSettingById(record.id)
          .then(response => {
            if (response.code === 200 && response.data) {
              const detailData = response.data
              
              // 设置表单值
              const formData = {
                irriYear: moment(detailData.irriYear.toString(), 'YYYY'),
              }
              
              // 设置选择的年份，用于限制时间范围
              this.selectedYear = detailData.irriYear
              
              // 设置春夏灌时间和作物
              if (detailData.ssStart && detailData.ssEnd) {
                formData.springSummerTimeRange = [
                  moment(detailData.ssStart),
                  moment(detailData.ssEnd)
                ]
              }
              if (detailData.ssCropsCodes) {
                formData.springSummerCrops = detailData.ssCropsCodes.split(',')
              }
              
              // 设置秋灌时间和作物
              if (detailData.autStart && detailData.autEnd) {
                formData.autumnIrrigationTimeRange = [
                  moment(detailData.autStart),
                  moment(detailData.autEnd)
                ]
              }
              if (detailData.autCropsCodes) {
                formData.autumnIrrigationCrops = detailData.autCropsCodes.split(',')
              }
              
              // 设置秋浇时间和作物
              if (detailData.autPourStart && detailData.autPourEnd) {
                formData.autumnWateringTimeRange = [
                  moment(detailData.autPourStart),
                  moment(detailData.autPourEnd)
                ]
              }
              if (detailData.autPourCropsCodes) {
                formData.autumnWateringCrops = detailData.autPourCropsCodes.split(',')
              }
              
              this.form.setFieldsValue(formData)
            } else {
              this.$message.error(response.message || '获取详情失败')
            }
          })
          .catch(error => {
            console.error('获取详情失败:', error)
            this.$message.error('获取详情失败，请重试')
          })
          .finally(() => {
            this.confirmLoading = false
          })
      }
    },
    
    // 确定
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          
          // 处理提交数据，转换为接口需要的格式
          const submitData = {
            irriYear: parseInt(values.irriYear.format('YYYY')),
            ssStart: values.springSummerTimeRange[0].format('YYYY-MM-DD'),
            ssEnd: values.springSummerTimeRange[1].format('YYYY-MM-DD'),
            ssCropsCodes: values.springSummerCrops.join(','),
            autStart: values.autumnIrrigationTimeRange[0].format('YYYY-MM-DD'),
            autEnd: values.autumnIrrigationTimeRange[1].format('YYYY-MM-DD'),
            autCropsCodes: values.autumnIrrigationCrops.join(','),
            autPourStart: values.autumnWateringTimeRange[0].format('YYYY-MM-DD'),
            autPourEnd: values.autumnWateringTimeRange[1].format('YYYY-MM-DD'),
            autPourCropsCodes: values.autumnWateringCrops.join(','),
          }
          
          // 如果是编辑，添加 id
          if (this.currentRecord) {
            submitData.id = this.currentRecord.id
          }
          
          // 调用对应的接口
          const apiCall = this.currentRecord ? updateIrrigationSetting : addIrrigationSetting
          
          apiCall(submitData)
            .then(response => {
              if (response.code === 200) {
                this.$message.success(this.currentRecord ? '编辑成功' : '新增成功')
                this.handleCancel()
                this.$emit('ok')
              } else {
                this.$message.error(response.message || '操作失败')
              }
            })
            .catch(error => {
              console.error('提交失败:', error)
              this.$message.error('操作失败，请重试')
            })
            .finally(() => {
              this.confirmLoading = false
            })
        }
      })
    },
    
    // 取消
    handleCancel() {
      this.visible = false
      this.form.resetFields()
      this.$emit('close')
    },
    
    // 年份选择器相关方法
    openChange(status) {
      if (status) {
        this.yearShow = true
      }
    },
    
    panelChange(value) {
      this.form.setFieldsValue({
        irriYear: value
      })
      this.yearShow = false
      
      // 保存选择的年份，用于限制时间范围
      this.selectedYear = moment(value).year()
      
      // 清空已选择的时间范围，因为年份变了
      this.form.setFieldsValue({
        springSummerTimeRange: undefined,
        autumnIrrigationTimeRange: undefined,
        autumnWateringTimeRange: undefined,
      })
    },
    
    // 获取时间选择器的禁用日期函数
    getDisabledDate(current) {
      if (!this.selectedYear) {
        return false
      }

      // 只允许选择当前选择年份的日期
      const selectedYearStart = moment(`${this.selectedYear}-01-01`)
      const selectedYearEnd = moment(`${this.selectedYear}-12-31`)

      return current && (current < selectedYearStart || current > selectedYearEnd)
    },

    // 判断时间范围是否应该被禁用（编辑模式下，开始时间早于当前时间的不能修改）
    isTimeRangeDisabled(type) {
      // 只在编辑模式下进行限制
      if (!this.currentRecord) {
        return false
      }

      const now = moment()
      let startTime = null

      // 根据类型获取对应的开始时间
      if (type === 'springSummer') {
        const timeRange = this.form.getFieldValue('springSummerTimeRange')
        startTime = timeRange && timeRange[0] ? timeRange[0] : null
      } else if (type === 'autumnIrrigation') {
        const timeRange = this.form.getFieldValue('autumnIrrigationTimeRange')
        startTime = timeRange && timeRange[0] ? timeRange[0] : null
      } else if (type === 'autumnWatering') {
        const timeRange = this.form.getFieldValue('autumnWateringTimeRange')
        startTime = timeRange && timeRange[0] ? timeRange[0] : null
      }

      // 如果开始时间早于当前时间，则禁用
      return startTime && startTime.isBefore(now, 'day')
    },

    // 春夏灌时间选择器的禁用日期函数
    getSpringSummerDisabledDate(current) {
      // 基础年份限制
      const baseDisabled = this.getDisabledDate(current)
      if (baseDisabled) {
        return true
      }

      // 编辑模式下的额外限制
      if (this.currentRecord) {
        const now = moment()
        const timeRange = this.form.getFieldValue('springSummerTimeRange')
        const startTime = timeRange && timeRange[0] ? timeRange[0] : null

        // 如果当前时间范围的开始时间早于今天，则不允许修改任何日期
        if (startTime && startTime.isBefore(now, 'day')) {
          return true
        }
      }

      return false
    },

    // 秋灌时间选择器的禁用日期函数
    getAutumnIrrigationDisabledDate(current) {
      // 基础年份限制
      const baseDisabled = this.getDisabledDate(current)
      if (baseDisabled) {
        return true
      }

      // 编辑模式下的额外限制
      if (this.currentRecord) {
        const now = moment()
        const timeRange = this.form.getFieldValue('autumnIrrigationTimeRange')
        const startTime = timeRange && timeRange[0] ? timeRange[0] : null

        // 如果当前时间范围的开始时间早于今天，则不允许修改任何日期
        if (startTime && startTime.isBefore(now, 'day')) {
          return true
        }
      }

      return false
    },

    // 秋浇时间选择器的禁用日期函数
    getAutumnWateringDisabledDate(current) {
      // 基础年份限制
      const baseDisabled = this.getDisabledDate(current)
      if (baseDisabled) {
        return true
      }

      // 编辑模式下的额外限制
      if (this.currentRecord) {
        const now = moment()
        const timeRange = this.form.getFieldValue('autumnWateringTimeRange')
        const startTime = timeRange && timeRange[0] ? timeRange[0] : null

        // 如果当前时间范围的开始时间早于今天，则不允许修改任何日期
        if (startTime && startTime.isBefore(now, 'day')) {
          return true
        }
      }

      return false
    },

    // 检查时间段是否有交叉
    isRangeOverlap(range1, range2) {
      if (!range1 || !range2 || range1.length !== 2 || range2.length !== 2) return false;
      const [start1, end1] = range1;
      const [start2, end2] = range2;
      if (!start1 || !end1 || !start2 || !end2) return false;
      // 只要有一天重叠（包括端点），就算冲突
      return !(end1.isBefore(start2) || start1.isAfter(end2));
    },
    // 春夏灌时间校验
    validateSpringSummerTimeRange(rule, value, callback) {
      const autumnIrrigationTimeRange = this.form.getFieldValue('autumnIrrigationTimeRange');
      const autumnWateringTimeRange = this.form.getFieldValue('autumnWateringTimeRange');
      if (this.isRangeOverlap(value, autumnIrrigationTimeRange)) {
        callback('春夏灌时间不能与秋灌时间重叠');
        return;
      }
      if (this.isRangeOverlap(value, autumnWateringTimeRange)) {
        callback('春夏灌时间不能与秋浇时间重叠');
        return;
      }
      callback();
    },
    // 秋灌时间校验
    validateAutumnIrrigationTimeRange(rule, value, callback) {
      const springSummerTimeRange = this.form.getFieldValue('springSummerTimeRange');
      const autumnWateringTimeRange = this.form.getFieldValue('autumnWateringTimeRange');
      if (this.isRangeOverlap(value, springSummerTimeRange)) {
        callback('秋灌时间不能与春夏灌时间重叠');
        return;
      }
      if (this.isRangeOverlap(value, autumnWateringTimeRange)) {
        callback('秋灌时间不能与秋浇时间重叠');
        return;
      }
      callback();
    },
    // 秋浇时间校验
    validateAutumnWateringTimeRange(rule, value, callback) {
      const springSummerTimeRange = this.form.getFieldValue('springSummerTimeRange');
      const autumnIrrigationTimeRange = this.form.getFieldValue('autumnIrrigationTimeRange');
      if (this.isRangeOverlap(value, springSummerTimeRange)) {
        callback('秋浇时间不能与春夏灌时间重叠');
        return;
      }
      if (this.isRangeOverlap(value, autumnIrrigationTimeRange)) {
        callback('秋浇时间不能与秋灌时间重叠');
        return;
      }
      callback();
    },
  },
}
</script>

<style lang="less" scoped>
.form-section {
  // margin-bottom: 32px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    // margin-bottom: 16px;
    // padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.irrigation-item {
  margin-bottom: 24px;
  padding: 16px 16px 0 16px;
  background: #fafafa;
  border-radius: 6px;
  
  .irrigation-title {
    font-size: 14px;
    font-weight: 600;
    // color: #1890ff;
    margin-bottom: 16px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

/* Modal styles - no custom footer needed as a-modal handles this */

.time-disabled-tip {
  font-size: 12px;
  color: #faad14;
  margin-top: 4px;
  display: flex;
  align-items: center;
}
</style> 