
import request from '@/utils/request'


//新增
export function addIrrigationPlantIntent(data) {
    return request({
        url: '/custom/irrigationPlantIntent/add',
        method: 'post',
        data
    })
}

//更新
export function updateIrrigationPlantIntent(data) {
    return request({
        url: '/custom/irrigationPlantIntent/update',
        method: 'post',
        data
    })
}

//删除
export function deleteIrrigationPlantIntent(id) {
    return request({
        url: '/custom/irrigationPlantIntent/delete',
        method: 'post',
        params: { id }
    })
}

//详情
export function getIrrigationPlantIntentById(id) {
    return request({
        url: '/custom/irrigationPlantIntent/get',
        method: 'post',
        params: { id }
    })
}

//列表
export function getIrrigationPlantIntentList(data) {
    return request({
        url: '/custom/irrigationPlantIntent/page',  
        method: 'post',
        data
    })
}