<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <!-- <a-form-item label="预案名称">
        <a-input
          v-model="queryParam.emergencyPlanName"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item> -->

      <a-form-item label="事件类型">
        <a-select show-search placeholder="请输入" v-model="queryParam.emergencyPlanType" option-filter-prop="children">
          <a-select-option v-for="item in eventTypeOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="处理状态">
        <a-select show-search placeholder="请输入" v-model="queryParam.emergencyPlanType" option-filter-prop="children">
          <a-select-option v-for="item in eventStatusOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <!-- <a-form-item label="批复时间">
        <a-range-picker
          allow-clear
          :value="replyTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item> -->

      <!-- <a-form-model-item label="所属工程">
        <a-tree-select
          v-model="queryParam.projectId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-model-item> -->
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <!-- <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button> -->
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <!-- <FormDrawer
          v-if="showForm"
          ref="formRef"
          :emergencyTypeOptions="emergencyTypeOptions"
          :projectOptions="projectOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        /> -->
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :eventTypes="eventTypes"
          :eventStatus="eventStatus"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
        <FormPublish
          v-if="showFormPublish"
          ref="formPublishRef"
          @ok="onOperationComplete"
          @close="showFormPublish = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree } from '@/api/common'
  import { getEventPage, getEventDetail } from './services'
  // import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/DetailModal.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import FormPublish from './modules/FormPublish.vue'

  export default {
    name: 'EmergencyPlan',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormPublish,
      // FormDrawer,
    },
    data() {
      return {
        showFormPublish: false,
        eventTypeOptions: [],
        eventTypes: null,
        eventStatusOptions: [],
        eventStatus: null,

        unitList: [],
        unitArr: [],
        isChecked: false,
        emergencyTypeOptions: [],
        emergencyTypes: [],
        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        replyTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '事件管理',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          eventName: '',
          eventType: '',
          isIssue: 1,
          pageNum: 1,
          pageSize: 10,
          sort: [],
          status: null,
          warnLevel: '',

          // emergencyPlanName: '',
          // emergencyPlanType: null,
          // endTime: null,
          // pageNum: 1,
          // pageSize: 10,
          // projectId: null,
          // sort: [],
          // startTime: null,
        },
        columns: [
          // { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          // {
          //   type: 'seq',
          //   title: '序号',
          //   width: 50,
          //   slots: {
          //     default: ({ row, rowIndex }) => {
          //       return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
          //     },
          //   },
          // },
          {
            title: '事件名称',
            field: 'eventName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },

          {
            title: '事件类型',
            field: 'eventType',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.eventTypes[row.eventType]?.value || ''
              },
            },
          },
          {
            title: '异常点名称',
            field: 'cameraName',
            minWidth: 140,
            showOverflow: 'tooltip',
          },
          {
            title: '位置',
            field: 'location',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '发生时间',
            field: 'disposeDate',
            minWidth: 140,
            // sortable: true,
          },
          {
            title: '处理人',
            field: 'disposeUserName',
          },
          {
            title: '处理状态',
            field: 'status',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.eventStatus[row.status]?.value || ''
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 138,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    {row.status != 2 && <a-divider type='vertical' />}
                    {row.status != 2 && (
                      <a style={{ color: '#FF9A2E' }} onClick={() => this.handleIssue(row)}>
                        处理{' '}
                      </a>
                    )}

                    {/* <a-divider type='vertical' />
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a> */}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
      getOptions('eventType').then(res => {
        this.eventTypeOptions = res.data
        this.eventTypes = getFlatTreeMap(this.eventTypeOptions, 'key')
      })

      getOptions('event-status').then(res => {
        this.eventStatusOptions = res.data
        this.eventStatus = getFlatTreeMap(this.eventStatusOptions, 'key')
      })

      // 获取工程树
      // getProjectTree({ objectCategoryId: undefined }).then(res => {
      //   this.projectOptions = res.data
      //   this.projects = getFlatTreeMap(this.projectOptions, 'id')
      // })
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      onRangeChange(value, replyTimes) {
        this.replyTimes = value
        if (replyTimes.length == 0) {
          return
        }
        this.queryParam.startTime = replyTimes[0] ? moment(replyTimes[0]).format('YYYY-MM-DD') : undefined
        this.queryParam.endTime = replyTimes[1] ? moment(replyTimes[1]).format('YYYY-MM-DD') : undefined
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getEventPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.emergencyPlanId)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          levelResponse: null,
          pageNum: 1,
          projectId: null,
          sort: [],
          year: null,
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      }, // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      //事件处理
      handleIssue(row) {
        this.showFormPublish = true
        this.$nextTick(() => this.$refs.formPublishRef.handle(row))
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.handleDetail(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.emergencyPlanId ? [row?.emergencyPlanId] : this.selectIds
        getEventDetail({ id: ids[0] }).then(res => {})
        // this.$confirm({
        //   title: '确认删除所选中数据?',
        //   content: '请确认是否删除当前选中的数据',
        //   onOk() {
        //     deletePlan({ emergencyPlanIds: ids.join(',') }).then(res => {
        //       if (res.code == 200) {
        //         that.$message.success(`成功删除 ${res.data} 条数据`, 3)
        //         that.selectChange({ records: [] })
        //         that.onOperationComplete()
        //       }
        //     })
        //   },
        //   onCancel() {},
        // })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
