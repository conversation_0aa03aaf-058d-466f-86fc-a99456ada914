<template>
  <!-- 代表站配置 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :modalWidth="windowSize.width"
    @cancel="cancel"
    :modalHeight="windowSize.height"
    :footer="false"
  >
    <div slot="content">
      <!-- 筛选栏 -->
      <div class="common-table-page">
        <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
          <a-form-item label="工程编码">
            <a-input
              v-model="queryParam.projectCode"
              placeholder="请输入工程编码"
              allow-clear
              @keyup.enter.native="handleQuery"
            />
          </a-form-item>
          <a-form-item label="工程名称">
            <a-input
              v-model="queryParam.projectName"
              placeholder="请输入工程名称"
              allow-clear
              @keyup.enter.native="handleQuery"
            />
          </a-form-item>

          <template #table>
            <VxeTable
              ref="vxeTableRef"
              :tableTitle="tableTitle"
              :columns="columns"
              :tableData="list"
              :loading="loading"
              :isAdaptPageSize="true"
              @adaptPageSizeChange="adaptPageSizeChange"
              @refresh="getList"
              @selectChange="selectChange"
              @sortChange="sortChange"
              :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
              @handlePageChange="handlePageChange"
            >
              <div class="table-operations" slot="button">
                <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
                <a-button type="primary" @click="handleLeafAdd()">
                  <a-icon type="plus" />
                  新增
                </a-button>
                <a-button type="danger" v-if="isChecked" @click="handleDelete">
                  <a-icon type="delete" />
                  删除
                </a-button>
              </div>
            </VxeTable>
          </template>
        </VxeTableForm>
      </div>

      <FormDrawer
        v-if="showFormDrawer"
        :districtOptions="districtTreeOptions.dataSource"
        :projectOptions="treeOptions.dataSource"
        ref="formDrawerRef"
        @ok="onOperationComplete"
        @close="showFormDrawer = false"
      />
      <FormModal
        v-if="showFormModal"
        :siteTypeList="siteTypeList"
        ref="formModalRef"
        @ok="onOperationComplete"
        @close="showFormModal = false"
      />
      <MonitoringIndexModal
        v-if="showMonitoringIndexModal"
        ref="paramsMonitoringIndexModalRef"
        @ok="showMonitoringIndexModal = false"
      />
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import { deleteProject, getBaseProject } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTableForm from '@/components/VxeTableForm'
  import VxeTable from '@/components/VxeTable'
  import FormDrawer from './FormDrawer'
  import FormModal from './FormModal'
  import MonitoringIndexModal from '@/components/MonitoringIndex'

  export default {
    name: 'ChildrenProject',
    props: ['districtTreeOptions', 'treeOptions', 'siteTypeList', 'treeTabKey', 'objectType', 'districtTypes'],
    components: { AntModal, VxeTableForm, VxeTable, FormDrawer, FormModal, MonitoringIndexModal },
    data() {
      return {
        open: false,
        showFormDrawer: false,
        showFormModal: false,
        showMonitoringIndexModal: false,
        list: [],
        tableTitle: ' ',
        total: 0,
        isChecked: false,
        isExpandIcon: 1,
        isExpandId: null,
        formTitle: '',
        projectId: '',
        windowSize: {},
        ids: [],
        names: [],
        loading: false,
        modalLoading: false,
        exportLoading: false,
        queryParam: {
          districtCode: undefined,
          objectCategoryId: undefined,
          orgId: undefined,
          pageNum: 1,
          pageSize: 10,
          parentId: undefined,
          patrolType: undefined,
          projectCode: undefined,
          projectIds: [],
          projectName: undefined,
        },
        columns: [
          { type: 'checkbox', width: 40 },
          {
            title: '工程编码',
            field: 'projectCode',
            minWidth: 180,
            showOverflow: 'tooltip',
            treeNode: true,
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '工程类别',
            field: 'objectCategoryName',
            minWidth: 100,
          },
          {
            title: '行政区划',
            field: 'districtCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.$props.districtTypes[row.districtCode]?.districtName || ''
              },
            },
          },
          {
            title: '排序号',
            field: 'sort',
            minWidth: 80,
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 240,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleConfig(row)}>配置代表站</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                    <a-divider type='vertical' />
                    <a-dropdown>
                      <a>
                        更多
                        <a-icon type='down' />
                      </a>
                      <a-menu slot='overlay'>
                        <a-menu-item>
                          <a onClick={() => this.handleParamSet(row)}>监测指标</a>
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item>
                          <a onClick={() => this.handleAdd(row)}>添加子工程</a>
                        </a-menu-item>
                      </a-menu>
                    </a-dropdown>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    filters: {},
    created() {
      this.windowSize = {
        width: `${parseInt(window.innerWidth * 0.7)}`,
        height: `${parseInt(window.innerHeight * 0.85)}`,
      }
    },
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 修改按钮操作 */
      projectChildren(record) {
        this.open = true
        this.formTitle = record.projectName + '-子工程'
        this.queryParam.parentId = record.projectId
        this.queryParam.objectCategoryId = record.objectCategoryId

        this.getList()
      },
      /** 查询列表 */
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getBaseProject(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          districtCode: undefined,
          objectCategoryId: undefined,
          orgId: undefined,
          pageNum: 1,
          pageSize: 10,
          patrolType: undefined,
          projectCode: undefined,
          projectIds: [],
          projectName: undefined,
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.projectId)
        this.names = valObj.records.map(item => item.projectName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      handleLeafAdd(record) {
        this.showFormDrawer = true
        const type = 'leaf'
        const r = record
          ? record
          : {
              objectCategoryId: this.queryParam.objectCategoryId,
              parentId: this.queryParam.parentId,
              projectId: this.queryParam.parentId,
            }
        this.$nextTick(() => this.$refs.formDrawerRef.handleAdd(type, r, this.$props.treeTabKey))
      },
      /* 新增 */
      handleAdd(record) {
        this.showFormDrawer = true
        const type = record ? 'leaf' : 'root'
        this.$nextTick(() => this.$refs.formDrawerRef.handleAdd(type, record, this.$props.treeTabKey))
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },
      handleConfig(record) {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.handleUpdate(record))
      },
      // 参数配置
      handleParamSet(record) {
        this.showMonitoringIndexModal = true
        let obj = {
          ...record,
          objectType: this.$props.objectType,
          objectId: record?.projectId,
        }
        this.$nextTick(() => {
          this.$refs.paramsMonitoringIndexModalRef.handleShow(obj)
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this

        const projectIds = row.projectId ? [row.projectId] : this.ids
        const names = row.projectName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteProject({ projectIds: projectIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },

      // 导出
      handleExport() {
        this.exportLoading = true
        this.getList(null, true, (columns, data) => {
          this.exportLoading = false

          const dataList = getFlatTree(data).map(el => ({
            ...el,
            districtCode: this.$props.districtTypes[el.districtCode]?.districtName || '',
          }))

          const columnsList = [
            {
              title: '工程编码',
              field: 'projectCode',
              minWidth: 180,
            },
            {
              title: '工程名称',
              field: 'projectName',
              minWidth: 180,
            },
            {
              title: '工程简称',
              field: 'projectNameAbbr',
              minWidth: 110,
            },
            {
              title: '工程类别',
              field: 'objectCategoryName',
              minWidth: 100,
            },
            {
              title: '行政区划',
              field: 'districtCode',
              minWidth: 120,
            },
            {
              title: '所属上级工程',
              field: 'parentName',
              minWidth: 100,
            },
            {
              title: '经度',
              field: 'longitude',
              minWidth: 120,
            },
            {
              title: '纬度',
              field: 'latitude',
              minWidth: 120,
            },
            {
              title: '所在位置',
              field: 'location',
              minWidth: 200,
            },
          ]

          excelExport(columnsList, dataList, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .common-table-page {
    height: calc(100vh - 280px);
  }
</style>
