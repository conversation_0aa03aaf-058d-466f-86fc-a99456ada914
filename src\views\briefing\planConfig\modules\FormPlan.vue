<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      modalWidth="500"
      @cancel="cancel"
      modalHeight="560"
    >
      <div slot="content">
        <a-form-model ref="form" :model="planForm" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row>
            <a-form-model-item label="简报名称" prop="reportName">
              <a-input allowClear v-model="planForm.reportName" :maxLength="20" placeholder="请输入" />
            </a-form-model-item>
            <a-form-model-item label="简报模板" prop="reportTemplateId">
              <a-select allowClear v-model="planForm.reportTemplateId" placeholder="请选择">
                <a-select-option
                  v-for="(item, index) in briefTemplateList"
                  :key="item.reportTemplateId"
                  :value="item.reportTemplateId"
                >
                  {{ item.reportTemplateName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="发布频率" prop="genFrequency">
              <a-radio-group v-model="planForm.genFrequency" @change="changeFrequency">
                <a-radio value="1">日</a-radio>
                <a-radio value="2">旬</a-radio>
                <a-radio value="3">月</a-radio>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item label="发布时间" prop="genTime">
              <div class="form-item" v-if="planForm.genFrequency == 1">
                <label class="text">次日：</label>
                <a-select class="time-select" allowClear v-model="planForm.genTime" placeholder="请选择">
                  <a-select-option v-for="i in dayList" :key="i + 'day'" :value="i">
                    {{ i }}
                  </a-select-option>
                </a-select>
                <!-- <a-time-picker :default-open-value="moment('00:00:00', 'HH:mm:ss')" v-model="planForm.genTime" @change="onChangeHours" /> -->
                <span class="text marL10">时</span>
              </div>
              <div class="form-item" v-else-if="planForm.genFrequency == 2">
                <label class="text">次旬：</label>
                <span class="text marR10">第</span>
                <a-select class="time-select" allowClear v-model="planForm.genTime" placeholder="请选择">
                  <a-select-option v-for="i in tenDayList" :key="i + 'tenDay'" :value="i">
                    {{ i }}
                  </a-select-option>
                </a-select>
                <span class="text marL10">日</span>
              </div>
              <div class="form-item" v-else-if="planForm.genFrequency == 3">
                <label class="text">次月：</label>
                <span class="text marR10">第</span>
                <a-select class="time-select" allowClear v-model="planForm.genTime" placeholder="请选择">
                  <a-select-option v-for="m in monthList" :key="m + 'month'" :value="m">
                    {{ m }}
                  </a-select-option>
                </a-select>
                <span class="text marL10">日</span>
              </div>
            </a-form-model-item>
            <a-form-model-item label="到期循环">
              <a-radio-group v-model="planForm.isInfinite" @change="changeInfinite">
                <a-radio value="1">是</a-radio>
                <a-radio value="0">否</a-radio>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item prop="genStartDate" label="开始时间">
              <a-date-picker
                v-model="planForm.genStartDate"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                @change="changeTime"
              />
            </a-form-model-item>
            <div class="form-item date-picker marL10">
              <label>结束时间：</label>
              <a-date-picker
                v-model="planForm.genEndDate"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                :disabled="planForm.isInfinite == '1'"
                @change="changeTime"
              />
            </div>
            <!-- <a-checkbox class="marL10" v-model="planForm.isInfinite">到期循环</a-checkbox> -->
          </a-row>
        </a-form-model>
      </div>
      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
        <a-button type="primary" @click="generateTimeBtn" :loading="timeLoading">生成时间预测</a-button>
      </template>
    </ant-modal>
    <GenerateTime v-if="showGenerateTime" ref="generateTimeRef" />
  </div>
</template>
<script lang="jsx">
  import { addPlan, getPlanById, editPlan } from '../services'
  import { getTemplateList } from '@/views/briefing/templateConfig/services'
  import moment from 'moment'
  import GenerateTime from './FormGenerateTime.vue'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormPlan',
    components: { GenerateTime, AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        timeLoading: false,
        labelCol: { span: 4 },
        wrapperCol: { span: 18 },
        briefTemplateList: [],
        dayList: [
          '00',
          '01',
          '02',
          '03',
          '04',
          '05',
          '06',
          '07',
          '08',
          '09',
          '10',
          '11',
          '12',
          '13',
          '14',
          '15',
          '16',
          '17',
          '18',
          '19',
          '20',
          '22',
          '23',
        ],
        monthList: [
          '01',
          '02',
          '03',
          '04',
          '05',
          '06',
          '07',
          '08',
          '09',
          '10',
          '11',
          '12',
          '13',
          '14',
          '15',
          '16',
          '17',
          '18',
          '19',
          '20',
          '21',
          '22',
          '23',
          '24',
          '25',
          '26',
          '27',
          '28',
        ],
        tenDayList: ['01', '02', '03', '04', '05', '06', '07', '08'],
        formTitle: '',
        showGenerateTime: true,
        // 表单参数
        planForm: {
          reportName: null,
          reportTemplateId: null,
          genFrequency: '1',
          genTime: null,
          genStartDate: null,
          genEndDate: null,
          isInfinite: '0',
        },
        open: false,
        planId: null,
        rules: {
          reportName: [{ required: true, message: '简报名称不能为空', trigger: 'blur' }],
          reportTemplateId: [{ required: true, message: '简报模板不能为空', trigger: 'change' }],
          genFrequency: [{ required: true, message: '发布频率不能为空', trigger: 'blur' }],
          genTime: [{ required: true, message: '发布时间不能为空', trigger: 'change' }],
          genStartDate: [{ required: true, message: '开始时间不能为空', trigger: 'change' }],
        },
      }
    },
    created() {
      getTemplateList({ pageNum: 1, pageSize: 9999 }).then(res => {
        if (res.code == 200) {
          this.briefTemplateList = res.data?.data
        }
      })
    },
    methods: {
      changeInfinite() {
        this.planForm.genStartDate = null
        this.planForm.genEndDate = null
      },
      moment,
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      /** 新增按钮操作 */
      handlePlan(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.formTitle = '修改'
          this.modalLoading = true
          getPlanById({ reportConfigId: row.reportConfigId }).then(res => {
            this.modalLoading = false
            if (res.code == 200) {
              this.planForm = res.data
              this.planForm.genFrequency = row.genFrequency.toString()
              this.planForm.isInfinite = row.isInfinite.toString()
            }
          })
        }
      },
      editFormData() {
        if (this.planForm.isInfinite == 0 && this.planForm.genEndDate == null) {
          this.$message.error('结束日期不能为空')
          return
        }
        if (this.planForm.genFrequency == 1) {
          this.planForm.genTime =
            this.planForm.genTime && this.planForm.genTime.indexOf('00:00') == -1
              ? (this.planForm.genTime += ':00:00')
              : this.planForm.genTime
        }
        this.planForm.genStartDate =
          this.planForm?.genStartDate?.indexOf('00:00:00') == -1
            ? this.planForm.genStartDate + ' 00:00:00'
            : this.planForm.genStartDate
        this.planForm.genEndDate =
          this.planForm?.genEndDate?.indexOf('23:59') == -1
            ? this.planForm.genEndDate + ' 23:59:59'
            : this.planForm.genEndDate
      },
      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.editFormData()
            if (this.planForm.reportConfigId == null) {
              addPlan(this.planForm)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editPlan(this.planForm)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
      onChangeHours(time, timeString) {
        this.planForm.genTime = timeString
      },
      changeFrequency() {
        this.planForm.genTime = null
      },
      changeTime() {
        let startValue = this.planForm.genStartDate
        let endValue = this.planForm.genEndDate
        if (startValue && endValue) {
          if (new Date(startValue) > new Date(endValue)) {
            this.$message.error('结束时间必须大于开始时间')
            return
          }
        }
      },
      generateTimeBtn() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.timeLoading = true
            this.editFormData()
            this.showGenerateTime = true
            this.$nextTick(() => {
              this.$refs.generateTimeRef.handleShowTime(this.planForm)
              this.timeLoading = false
            })
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  .modal-content {
    .marL10 {
      margin-left: 10px;
    }
    .marR10 {
      margin-right: 10px;
    }
    .form-item {
      display: flex;
      .text {
        white-space: nowrap;
        line-height: 30px;
      }
    }
    .date-picker {
      margin-bottom: 10px;
    }
    .time-select {
      width: 100px;
    }
  }
</style>
