import request from '@/utils/request'

// 发布项维护
export function getManageList(data) {
  return request({
    url: '/war/admin/report/admin/page',
    method: 'post',
    data
  })
}
// 发布
export function releaseManage(params) {
  return request({
    url: '/war/admin/report/release/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 删除
export function deleteManage(params) {
  return request({
    url: '/war/admin/report/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}