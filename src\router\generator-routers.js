// eslint-disable-next-line
import { getMenuPermissions } from '@/api/login'
import { UserLayout, BasicLayout, RouteView } from '@/layouts'
import { indexRouterMap } from '@/config/router.config'
import cloneDeep from 'lodash.clonedeep'
import queryString from 'query-string'

// 前端路由表
const constantRouterComponents = {
  // 基础页面 layout 必须引入
  BasicLayout: BasicLayout,
  RouteView: RouteView,
  UserLayout: UserLayout, // 登陆注册页面的通用布局

  // 你需要动态引入的页面组件
  Workbench: () => import('@/views/workbench/index'),
}

// 前端未找到页面路由（固定不用改）
const notFoundRouter = {
  path: '*',
  redirect: '/404',
  hidden: true,
}
// 根级菜单
const rootRouter = {
  key: '',
  name: 'index',
  path: '',
  component: 'BasicLayout',
  redirect: '/index',
  meta: {},
  children: [],
}

/**
 * 动态生成菜单
 * @param token
 * @returns {Promise<Router>}
 */
export const generatorDynamicRouter = token => {
  return new Promise((resolve, reject) => {
    // 向后端请求路由数据
    getMenuPermissions()
      .then(res => {
        // localStorage.setItem('asyncRouters', JSON.stringify(res?.data || []))
        let oldRouter = cloneDeep(res.data)

        let [newRouter, btnArr] = changeValueState(oldRouter)
        rootRouter.children = newRouter
        const routers = generator([rootRouter])

        routers.push(notFoundRouter)
        //
        if (newRouter.length == 0 && newRouter[0]?.type != 3) {
          routers.push(noContent)
        }
        localStorage.setItem('asyncRouters', JSON.stringify(newRouter || []))

        resolve({ routers, permissionButtons: btnArr })
      })
      .catch(err => {
        reject(err)
      })
  })
}

/**格式化路由数据
 * @param 路由接口数据
 * **/
export const changeValueState = data => {
  let btnArr = []
  function dealData(data, pPath = '') {
    data.forEach((ele, i) => {
      ele.name = ele.menuCode
      // 下面兼容了用户中心里面的路由地址配置时前面是否加 / ，现在加不加都可以了
      ele.path =
        (pPath ? (pPath?.startsWith('/') ? pPath : `/${pPath}`) : '') +
        (ele.route?.startsWith('/') ? ele.route : `/${ele.route}`)

      ele.hidden = !!ele.isHidden
      // 只是目录
      if (ele.type == 2) {
        ele.redirect = getFirstRedirect(ele.children || [], ele.path) || 'noRedirect'
        ele.component = 'RouteView'
      }
      // 具体页面
      if (ele.type == 3) {
        ele.component = ele.component
        ele.children = ele.children.filter(el => {
          // 按钮
          if (el.type == 4) {
            btnArr.push(el)
          }

          return el.type != 4
        })
      }

      ele.isFrame = ele.isLink == 1 ? true : false
      ele.meta = {
        title: ele.menuName,
        icon: ele.icon,
        noCache: false,
        openWith: ele.openWith,
        remark: ele.remark,
        keepAlive: true,
        menuId: ele.menuId,
      }
      if (ele.children) {
        dealData(ele.children, ele.path)
      }
    })
  }
  dealData(data)

  return [data, btnArr]
}

/**
 * 格式化树形结构数据 生成 vue-router 层级路由表
 *
 * @param routerMap
 * @param parent
 * @returns {*}
 */
export const generator = (routerMap, parent) => {
  return routerMap.map(item => {
    const { title, show, hideChildren, hiddenHeaderContent, hidden, icon, noCache, keepAlive, openWith, menuId } =
      item.meta || {}

    if (item.isFrame === '0') {
      item.target = '_blank'
    } else {
      item.target = ''
    }

    let hasQuery = item.path.includes('?')
    let path
    let query
    if (hasQuery) {
      query = queryString.parse(item.path.split('?')[1])
      path = item.path.split('?')[0]
    } else {
      path = item.path
    }

    const currentRouter = {
      path: path || `${(parent && parent.path) || ''}/${path}`,
      // 路由名称，建议唯一
      name: item.name || item.key || '',
      openWith: openWith,
      // 该路由对应页面的 组件(动态加载)
      component: constantRouterComponents[item.component || item.key] || (() => import(`@/views/${item.component}`)),
      hidden: item.hidden,
      // meta: 页面标题, 菜单图标, 页面权限(供指令权限用，可去掉)
      meta: {
        title: title,
        icon: icon,
        iconStr: icon === null ? 'profile' : icon,
        hiddenHeaderContent: hiddenHeaderContent,
        // 目前只能通过判断path的http链接来判断是否外链，适配若依
        // target: validURL(item.path) ? '_blank' : '',
        target: item.target,
        permission: item.name,
        noCache: noCache,
        keepAlive: keepAlive,
        hidden: hidden,
        remark: item.remark, //item.meta.remark,
        openWith: openWith,
        query: hasQuery ? query : '',
        menuId: menuId,
      },
      redirect: item.redirect,
    }
    // 是否设置了隐藏菜单
    if (show === false) {
      currentRouter.hidden = true
    }
    // 是否设置了隐藏子菜单
    if (hideChildren) {
      currentRouter.hideChildrenInMenu = true
    }

    // 重定向
    item.redirect && (currentRouter.redirect = item.redirect)

    // 是否有子菜单，并递归处理
    if (item.children && item.children.length > 0) {
      currentRouter.children = generator(item.children, currentRouter)
    }
    return currentRouter
  })
}

// 获取第一个叶子菜单
export function getFirstRedirect(dataArr, pPath) {
  let rPath = ''
  let path = ''

  function dealArr(arr) {
    arr.forEach(ele => {
      if (!path && ele.type != 4) {
        if (!ele.children?.length) {
          path =
            (rPath ? (rPath?.startsWith('/') ? rPath : `/${rPath}`) : '') +
            (ele.route?.startsWith('/') ? ele.route : `/${ele.route}`)
        } else {
          rPath =
            (rPath ? (rPath?.startsWith('/') ? rPath : `/${rPath}`) : '') +
            (ele.route?.startsWith('/') ? ele.route : `/${ele.route}`)
          dealArr(ele.children || [])
        }
      }
    })
  }
  dealArr(dataArr)
  return pPath + path
}
