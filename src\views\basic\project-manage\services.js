import request from '@/utils/request'

// 水利对象分类-根据编码获取树
export function getTreeByCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'HP' },
  })
}

// 工程信息-获取排序号
export function getNextSort(params) {
  return request({
    url: '/base/project/getNextSort',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程信息-获取父节点树
export function getParentTree(params) {
  return request({
    url: '/base/project/getParentTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程信息-新增
export function addProject(data) {
  return request({
    url: '/base/project/add',
    method: 'post',
    data,
  })
}

// 工程信息-更新
export function updateProject(data) {
  return request({
    url: '/base/project/update',
    method: 'post',
    data,
  })
}

// 工程信息-详情
export function getProject(params) {
  return request({
    url: '/base/project/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程信息-删除
export function deleteProject(params) {
  return request({
    url: '/base/project/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取分类站点树
export function getNewCategoryTree(params) {
  return request({
    url: '/base/site/category/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params },
  })
}
// export function getCategoryTree(params) {
//   return request({
//     url: '/war/history/category/getTree',
//     method: 'post',
//     headers: {
//       'Content-Type': 'application/x-www-form-urlencoded',
//     },
//     params: { ...params, labels: '1' },
//   })
// }
// 工程信息-工程代表站详情
export function getProjectSite(params) {
  return request({
    url: '/base/project/getProjectSite',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 工程信息-工程代表站新增
export function addProjectSite(data) {
  return request({
    url: '/base/project/addProjectSite',
    method: 'post',
    data,
  })
}
//工程信息-分页查询
export function getBaseProject(data) {
  return request({
    url: '/base/project/page',
    method: 'post',
    data,
  })
}

// 机构树查询
export function getConfigTree(data) {
  return request({
    url: '/sys/dept/tree',
    method: 'post',
    data,
  })
}
