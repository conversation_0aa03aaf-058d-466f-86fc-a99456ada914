<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="640"
  >
    <div slot="content">
      <div style="display: flex">
        <img
          v-if="eventDetails?.evenAttachList"
          style="width: 70%; height: 100%"
          :src="eventDetails?.evenAttachList?.[0]"
        />
        <img v-else style="width: 70%; height: 100%" src="@/assets/images/camera-img.png" />
        <div class="event-box">
          <div>事件名称：{{ eventDetails.eventName || '--' }}</div>
          <!-- <div>事件类型：{{ eventDetails?.eventType || '环境异常' || '--' }}</div> -->
          <div>事件类型：{{ eventTypeOptions?.find(el => el.key == eventDetails.eventType)?.value || '--' }}</div>
          <div>告警级别：{{ warnLevelOptions?.find(el => el.key == eventDetails.warnLevel)?.value || '--' }}</div>
          <div>异常点：{{ eventDetails?.cameraName || '--' }}</div>
          <div>详细位置：{{ eventDetails?.location || '--' }}</div>
          <div>事件描述：{{ eventDetails?.content || '--' }}</div>
          <div>上报人：{{ eventDetails?.createdUserName || '--' }}</div>
          <div>上报时间：{{ eventDetails?.createdTime || '--' }}</div>
        </div>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'DetailsWaterScheme',
    components: { AntModal },
    props: ['warnLevelOptions', 'eventTypeOptions'],
    data() {
      return {
        formTitle: '事件详情',
        loading: false,
        modalLoading: false,
        open: false,

        eventDetails: {},
      }
    },
    filters: {},
    created() {},

    methods: {
      handleClose() {
        this.open = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 详情按钮操作 */
      handleEventDetails(item) {
        console.log('🚀~item~>弹窗数据', item)
        this.open = true
        this.eventDetails = item
        this.formTitle = `${item.cameraName}异常详情`
      },
    },
  }
</script>
<style lang="less" scoped>
  .event-box {
    margin-left: 20px;
    line-height: 30px;
  }
</style>
