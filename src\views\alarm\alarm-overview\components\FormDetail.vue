<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1240"
    @cancel="cancel"
    modalHeight="680"
  >
    <div slot="content">
      <div class="container">
        <div class="left">
          <!-- <div
            v-if="data?.evenAttachList"
            class="left-item"
            :style="{ backgroundImage: `url(${data.evenAttachList[0]})` }"
          ></div> -->
          <!-- <div
            v-if="data?.evenAttachList"
            class="left-item"
            :style="{ backgroundImage: `url(${data.evenAttachList[0]})` }"
          ></div> -->
          <a-carousel
            v-if="data?.evenAttachList?.length > 0"
            :dots="true"
            :autoplay="true"
            :autoplaySpeed="autoplaySpeed"
          >
            <div v-for="(img, index) in data?.evenAttachList" :key="index" class="left-item">
              <img :src="img" alt="轮播图" class="carousel-img" />
            </div>
          </a-carousel>

          <!-- 当只有一张图时不显示轮播器 -->
          <div v-else class="single-img">
            <!-- <img :src="imgList[0]" alt="单张图" class="carousel-img" /> -->
          </div>
          <!-- <div v-else class="left-none"></div> -->
          <!-- <div class="left-none"></div> -->
        </div>
        <div class="right">
          <a-row :gutter="32">
            <a-col :lg="24" :md="24" :sm="24">
              <div class="item">
                <label class="common-label-text">事件名称：</label>
                <span class="common-value-text">{{ data.eventName }}</span>
              </div>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24">
              <div class="item">
                <label class="common-label-text">事件类型：</label>
                <span class="common-value-text">{{ eventTypes[data.eventType]?.value || '-' }}</span>
              </div>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <div class="item">
                <label class="common-label-text">告警级别：</label>
                <span class="common-value-text">{{ warnLevels[data.warnLevel]?.value || '' }}</span>
              </div>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <div class="item">
                <label class="common-label-text">异常点名称：</label>
                <span class="common-value-text">{{ data.cameraName }}</span>
              </div>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="item">
                <label class="common-label-text">事件描述：</label>
                <span class="common-value-text">{{ data.content }}</span>
              </div>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <div class="item">
                <label class="common-label-text">上报人：</label>
                <span class="common-value-text">{{ data.createdUserName }}</span>
              </div>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <div class="item">
                <label class="common-label-text">上报时间：</label>
                <span class="common-value-text">{{ data.createdTime }}</span>
              </div>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <div class="item">
                <label class="common-label-text">详细位置：</label>
                <span :class="['common-value-text', 'text-overflow-ellipsis']" :title="data.location"></span>
              </div>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <div class="item">
                <span :class="['common-value-text']" :title="data.location">
                  {{ data.location }}
                </span>
              </div>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="item">
                <label class="common-label-text">附件：</label>
              </div>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div style="margin-bottom: 20px">
                <div class="file-item" v-for="(el, i) in data?.evenAttachList" :key="i" @click="() => downLoad(el)">
                  <a-icon type="paper-clip" />
                  <div class="file-name" :title="el" style="margin-left: 5px">{{ el }}</div>
                </div>
                <!-- <div
                  class="file-item"
                  v-for="(el, i) in data?.evenAttachList"
                  :key="i"
                  @click="() => downLoad(el?.attachUrl)"
                >
                  <a-icon type="paper-clip" />
                  <div class="file-name" :title="el?.attachName" style="margin-left: 5px">{{ el?.attachName }}</div>
                </div> -->
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getEventDetail } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'DetailModal',
    props: ['warnLevels', 'eventTypes'],
    components: { AntModal },
    data() {
      return {
        autoplaySpeed: 3000,
        open: false,
        modalTitle: '',
        modalLoading: false,
        data: {},
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 查看按钮操作 */
      handle(record) {
        this.open = true
        this.modalTitle = '异常详情'
        console.log('查看详情 111111111', record)
        this.modalLoading = true
        getEventDetail({ evenId: record.eventId }).then(res => {
          this.data = res.data
          this.modalLoading = false
        })
      },
      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    // background: red;
    width: 100%;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .text-overflow-ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .container {
    width: 100%;
    height: 100%;
    display: flex;
    .left {
      height: 520px;
      width: 660px;
      width: 920px;
      // background-color: red;
      // background-color: #f0f2f5;
      justify-content: center;
      .left-item {
        width: 100%;
        height: 100%;
        background-size: 100%;
        background-repeat: no-repeat;
        background-position: center;
      }
      .carousel-img {
        width: 100%;
        height: 100%;
        background-size: 100%;
        background-repeat: no-repeat;
        background-position: center;
        // justify-content: center;
      }
      .single-img {
        width: 100%;
        height: 100%;
        background: url('@/assets/images/patrol/patrol-satellite.png') no-repeat center/ 100% 100%;
      }
      .left-none {
        width: 100%;
        height: 100%;
        background: url('@/assets/images/patrol/patrol-satellite.png') no-repeat center/ 100% 100%;
      }
    }
    .right {
      height: 520px;
      width: 320px;
      padding-left: 20px;
      // background-color: red;
    }
  }
</style>
