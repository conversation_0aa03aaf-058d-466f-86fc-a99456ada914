<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="450"
    @cancel="cancel"
    modalHeight="400"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="方案编码" prop="dispatchCode">
              <a-input v-model="form.dispatchCode" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="方案时间" prop="dispatchDate">
              <a-date-picker
                showTime
                v-model="form.dispatchDate"
                format="YYYY-MM-DD HH:mm"
                placeholder="请选择"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="附件" prop="attachUrl">
              <UploadFile :fileUrl.sync="form.attachUrl" :multiple="false" listType="text" folderName="dispatch-case" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addDispatch, editDispatch, getDispatchById } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: [],
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        loading: false,
        modalLoading: false,
        formTitle: '',
        form: {
          dispatchCode: '',
          dispatchDate: null,
          dispatchId: null,
          attachUrl: null,
        },
        open: false,
        rules: {
          dispatchDate: [{ required: true, message: '方案时间不能为空', trigger: 'change' }],
          dispatchCode: [{ required: true, message: '方案编码不能为空', trigger: 'blur' }],
          attachUrl: [{ required: true, message: '附件不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getDispatchById({ dispatchId: row.dispatchId }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
              }
              this.modalLoading = false
            }
          })
        }
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.dispatchDate = moment(this.form.dispatchDate).format('YYYY-MM-DD HH:mm:ss')
            if (this.form.dispatchId == null) {
              addDispatch(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editDispatch(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }

  ::v-deep .ant-tabs-tabpane {
    // height: calc(100vh - 160px);
  }

  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }

  ::v-deep .attaches {
    // margin-top: 100px;
  }
</style>
