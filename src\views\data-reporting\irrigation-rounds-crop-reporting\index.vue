<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="年份">
        <a-date-picker
          mode="year"
          format="YYYY"
          v-model="queryParam.irriYear"
          placeholder="请选择年份"
          allow-clear
          :open="yearShowOne"
          style="width: 100%"
          @keyup.enter.native="handleQuery"
          @openChange="openChangeOne"
          @panelChange="panelChangeOne"
        ></a-date-picker>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="灌溉轮次作物上报"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="primary" @click="handleImport()">
              <a-icon type="import" />
              导入
            </a-button>
            <a-button type="primary" @click="handleExport()">
              <a-icon type="export" />
              导出
            </a-button>
          </div>
        </VxeTable>

        <FormDrawer v-if="showFormDrawer" ref="formDrawerRef" @ok="getList" @close="showFormDrawer = false" />
        <DetailsDrawer v-if="showDetails" ref="detailsRef" @ok="getList" @close="showDetails = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormDrawer from './modules/FormDrawer.vue'
  import DetailsDrawer from './modules/DetailsDrawer.vue'
  import moment from 'moment'
  import { getIrrigationSettingPage, deleteIrrigationSetting } from './service'

  export default {
    name: 'IrrigationRoundsCropReporting',
    components: {
      VxeTableForm,
      VxeTable,
      FormDrawer,
      DetailsDrawer,
    },
    data() {
      return {
        yearShowOne: false,
        showFormDrawer: false,
        showDetails: false,

        list: [],
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          pageNum: 1,
          pageSize: 10,
          irriYear: null,
          sort: [],
        },
        columns: [
          { type: 'seq', title: '序号', width: 50, align: 'center', headerAlign: 'center' },
          {
            title: '年份',
            field: 'irriYear',
            minWidth: 80,
            align: 'center',
            headerAlign: 'center',
          },
          {
            title: '春夏灌',
            field: 'springSummerIrrigation',
            minWidth: 120,
            showOverflow: 'tooltip',
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                if (row.ssStart && row.ssEnd) {
                  return `${row.ssStart} 至 ${row.ssEnd}`
                }
                return '-'
              }
            },
          },
          {
            title: '秋灌',
            field: 'autumnIrrigation',
            minWidth: 120,
            showOverflow: 'tooltip',
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                if (row.autStart && row.autEnd) {
                  return `${row.autStart} 至 ${row.autEnd}`
                }
                return '-'
              }
            },
          },
          {
            title: '秋浇',
            field: 'autumnWatering',
            minWidth: 120,
            showOverflow: 'tooltip',
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                if (row.autPourStart && row.autPourEnd) {
                  return `${row.autPourStart} 至 ${row.autPourEnd}`
                }
                return '-'
              }
            },
          },
          {
            title: '填报人',
            field: 'fillUserName',
            minWidth: 100,
            align: 'center',
            headerAlign: 'center',
          },
          {
            title: '填报时间',
            field: 'fillTime',
            minWidth: 150,
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                if (row.fillTime) {
                  return moment(row.fillTime).format('YYYY-MM-DD HH:mm:ss')
                }
                return '-'
              }
            },
          },
          {
            title: '操作',
            field: 'operate',
            minWidth: 160,
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleUpdate(row)}>编辑</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              }
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.queryParam.irriYear = parseInt(moment(value).format('YYYY'))
        this.yearShowOne = false
        this.handleQuery()
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        
        const params = {
          pageNum: this.queryParam.pageNum,
          pageSize: this.queryParam.pageSize,
          sort: this.queryParam.sort
        }
        
        // 只有选择了年份才添加年份参数
        if (this.queryParam.irriYear) {
          params.irriYear = this.queryParam.irriYear
        }
        
        getIrrigationSettingPage(params)
          .then(response => {
            if (response.code === 200) {
              this.list = response.data.data || []
              this.total = response.data.total || 0
            } else {
              this.$message.error(response.message || '查询失败')
              this.list = []
              this.total = 0
            }
          })
          .catch(error => {
            console.error('查询列表失败:', error)
            this.$message.error('查询失败，请重试')
            this.list = []
            this.total = 0
          })
          .finally(() => {
            this.loading = false
          })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          pageNum: 1,
          irriYear: null,
          sort: [],
        }
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.id)
        this.names = valObj.records.map(item => item.createBy || item.reporter)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      /* 新增 */
      handleAdd() {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleAdd())
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showDetails = true
        this.$nextTick(() => this.$refs.detailsRef.showDetails(record))
      },
      /* 导入 */
      handleImport() {
        // this.$message.info('导入功能待实现')
      },
      /* 导出 */
      handleExport() {
        // this.$message.info('导出功能待实现')
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row.id ? [row.id] : this.ids
        const names = row.createBy || row.reporter || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中填报人为"' + names + '"的数据',
          onOk() {
            // 调用删除接口
            const deletePromises = ids.map(id => deleteIrrigationSetting(id))
            
            Promise.all(deletePromises)
              .then(responses => {
                const allSuccess = responses.every(res => res.code === 200)
                if (allSuccess) {
                  that.$message.success(`成功删除数据`, 3)
                  that.selectChange({ records: [] })
                  that.getList()
                } else {
                  that.$message.error('删除失败，请重试')
                }
              })
              .catch(error => {
                console.error('删除失败:', error)
                that.$message.error('删除失败，请重试')
              })
          },
          onCancel() {},
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      margin-right: 8px;
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>