<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="时间维度">
        <a-radio-group v-model="queryParam.planType" @change="handleQuery">
          <a-radio-button v-for="item in radioOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-radio-button>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="计划时间">
        <a-range-picker
          allow-clear
          show-time
          style="width: 100%"
          v-model="rangeDate"
          :placeholder="['开始时间', '结束时间']"
          :format="queryParam.planType === 3 ? 'YYYY-MM' : 'YYYY-MM-DD'"
          :mode="queryParam.planType === 3 ? ['month', 'month'] : ['date', 'date']"
          @panelChange="handlePanelChange"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="用水计划信息"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleExport()" :loading="exportLoading">
              <a-icon type="download" />
              导出
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>

        <!-- <CenterFormModal
          v-if="showCenterFormModal"
          :radioOptions="radioOptions"
          ref="centerFormModalRef"
          @ok="getList"
          @close="showCenterFormModal = false"
        /> -->
        <OfficeFormModal
          v-if="showOfficeFormModal"
          :radioOptions="radioOptions"
          ref="officeFormModalRef"
          @ok="getList"
          @close="showOfficeFormModal = false"
        />
        <SegmentFormModal
          v-if="showSegmentFormModal"
          :radioOptions="radioOptions"
          ref="segmentFormModalRef"
          @ok="getList"
          @close="showSegmentFormModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getPlanMsgPage, deletePlanMsg } from './services'
  import { getValueByKey, getTreeByLoginOrgId } from '@/api/common'
  import CenterFormModal from './modules/CenterFormModal.vue'
  import OfficeFormModal from './modules/OfficeFormModal.vue'
  import SegmentFormModal from './modules/SegmentFormModal.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import excelExport from '@/utils/excelExport.js'

  export default {
    name: 'SupplyWaterPlan',
    components: {
      VxeTableForm,
      VxeTable,
      CenterFormModal,
      OfficeFormModal,
      SegmentFormModal,
    },
    data() {
      return {
        tableTitle: '',
        exportLoading: false,
        yearShowOne: false,
        showCenterFormModal: false,
        showOfficeFormModal: false,
        showDetails: false,
        showSegmentFormModal: false,
        radioOptions: [
          { label: '5日', value: 1 },
          { label: '旬', value: 2 },
          { label: '月', value: 3 },
        ],

        list: [],
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        rangeDate: [],
        queryParam: {
          planType: 1,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '计划名称',
            field: 'planName',
            minWidth: 300,
            showOverflow: 'tooltip',
          },
          {
            title: '计划时间',
            minWidth: 150,
            align: 'center',
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                return `${row.planStartDate} - ${row.planEndDate}`
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 160,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    {/* <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a> */}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    beforeDestroy() {},
    created() {
      getTreeByLoginOrgId().then(res => {
        this.deptOptions = res?.data
      })
    },
    methods: {
      handlePanelChange(value, mode) {
        this.rangeDate = value
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showCenterFormModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getPlanMsgPage({
          ...this.queryParam,
          planStartDate: this.rangeDate[0]?.format('YYYY-MM-DD'),
          planEndDate: this.rangeDate[1]?.format('YYYY-MM-DD'),
        }).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.rangeDate = []
        this.queryParam = {
          ...this.queryParam,
          pageNum: 1,
          year: null,
          supplyName: undefined,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.msgId)
        this.names = valObj.records.map(item => item.supplyName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      /* 查看 */
      handleDetails(record) {
        if (record.deptType === 2) {
          this.showOfficeFormModal = true
          this.$nextTick(() => this.$refs.officeFormModalRef.handleAdd(record))
        }
        // if (record.deptType === 1) {
        //   this.showCenterFormModal = true
        //   this.$nextTick(() => this.$refs.centerFormModalRef.handleAdd(record))
        // }
        if (record.deptType === 3) {
          this.showSegmentFormModal = true
          this.$nextTick(() => this.$refs.segmentFormModalRef.handleUpdate({ ...record, isDetail: true }))
        }
      },
      /** 导出按钮操作 */
      handleExport() {
        this.exportLoading = true
        getPlanMsgPage({
          ...this.queryParam,
          planStartDate: this.rangeDate[0]?.format('YYYY-MM-DD'),
          planEndDate: this.rangeDate[1]?.format('YYYY-MM-DD'),
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
        }).then(response => {
          this.exportLoading = false

          const columnsList = [
            {
              title: '计划名称',
              field: 'planName',
              minWidth: 300,
            },
            {
              title: '计划时间',
              field: 'planDateRange',
              minWidth: 150,
            },
          ]

          excelExport(
            columnsList,
            (response?.data?.data || []).map(el => ({
              ...el,
              planDateRange: `${el.planStartDate} - ${el.planEndDate}`,
            })),
            `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`,
          )
        })
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row.msgId ? [row.msgId] : this.ids
        const names = row.supplyName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中编号为"' + names + '"的数据',
          onOk() {
            return deletePlanMsg({ msgIds: ids.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.getList()
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
