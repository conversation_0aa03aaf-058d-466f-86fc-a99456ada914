<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="550"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="站点编码" prop="siteCode">
                <a-input allowClear v-model="form.siteCode" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="站点名称" prop="siteName">
                <a-input allowClear v-model="form.siteName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="对象简称" prop="siteNameAbbr">
                <a-input allowClear v-model="form.siteNameAbbr" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="站点类别" prop="objectCategoryId">
                <a-tree-select
                  v-model="form.objectCategoryId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="siteOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'objectCategoryName',
                    key: 'objectCategoryId',
                    value: 'objectCategoryId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="行政区划" prop="districtCode">
                <a-tree-select
                  v-model="form.districtCode"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="districtOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'districtName',
                    key: 'districtCode',
                    value: 'districtCode',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属工程" prop="projectId">
                <a-tree-select
                  v-model="form.projectId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="projectOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'projectName',
                    key: 'projectId',
                    value: 'projectId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="备注" prop="remark">
                <a-textarea v-model="form.remark" placeholder="请输入" allow-clear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
      <!-- <a-button type="primary" @click="submitForm">保存</a-button> -->
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import {} from '../services'
  import clickThrottle from '@/utils/clickThrottle'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormDrawer',
    props: ['districtOptions', 'siteOptions', 'projectOptions'],
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        // 默认密码
        formTitle: '',
        // 表单参数
        form: {
          siteId: undefined,
          siteName: undefined,
          siteCode: undefined,
          siteNameAbbr: undefined,
          objectCategoryId: undefined,
          districtCode: undefined,
          projectId: undefined,
          remark: undefined,
        },
        open: false,

        rules: {
          siteCode: [{ required: true, message: '站点编码不能为空', trigger: 'blur' }],
          siteName: [{ required: true, message: '站点名称不能为空', trigger: 'blur' }],
          objectCategoryId: [{ required: true, message: '站点类别不能为空', trigger: 'change' }],
          districtCode: [{ required: true, message: '行政区划不能为空', trigger: 'change' }],
        },
      }
    },
    filters: {},
    created() {
      // 字典获取下拉
    },
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd(r, treeTabType) {
        this.open = true
        this.formTitle = '新增'
        if (treeTabType === '1') {
          this.form.objectCategoryId = r.objectCategoryId || undefined
        }
        if (treeTabType === '2') {
          this.form.districtCode = r.districtCode || undefined
        }
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.modalLoading = true
        getSite({ siteId: record.siteId }).then(res => {
          this.modalLoading = false
          this.form = res.data
        })
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const saveForm = JSON.parse(JSON.stringify(this.form))

            if (this.form.siteId !== undefined) {
              updateSite(saveForm)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            } else {
              addSite(saveForm)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
