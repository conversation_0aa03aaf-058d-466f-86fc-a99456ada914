<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      :maskClosable="false"
      modalWidth="460"
      @cancel="cancel"
      modalHeight="660"
    >
      <div slot="content">
        <a-form-model ref="form" :model="form" :rules="rules">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="处置时间" prop="disposeDate">
                <a-date-picker
                  v-model="form.disposeDate"
                  :show-time="{ format: 'HH:mm:ss' }"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择"
                  style="width: 100%"
                />
                <!-- <a-radio-group v-model="form.wayType">
                  
                  <a-radio :value="1">系统内通知</a-radio>
                  <a-radio :value="2">短信</a-radio>
                  <a-radio :value="3">电子邮件</a-radio>
                </a-radio-group> -->
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="处理描述" prop="disposeContent">
                <!-- <a-input
                  allowClear
                  v-model="form.qrName"
                  :maxLength="8"
                  show-word-limit
                  @change="handleInputChange"
                  placeholder="请输入"
                />
                <div class="character-count">{{ currentLength }} / 8</div> -->
                <a-textarea v-model="form.disposeContent" placeholder="请输入" allowClear />

                <!-- <a-input allowClear v-model="form.externalLink" placeholder="请输入" /> -->
                <!-- <a-input-number v-model="inputLength" min="0" precision="0" /> -->
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="责任人" prop="disposeUserId">
                <a-select show-search placeholder="请输入" v-model="form.disposeUserId" option-filter-prop="children">
                  <a-select-option v-for="item in userOptions" :key="item.key" :value="item.key">
                    {{ item.value }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
              <a-col :lg="24" :md="24" :sm="24">
                <a-form-model-item label="附件">
                  <UploadFile
                    :fileUrl.sync="form.attaches"
                    :multiple="false"
                    listType="picture-card"
                    folderName="projectCover"
                    accept=".png,.jpg,.jpeg"
                  />
                </a-form-model-item>
              </a-col>
              <!-- <div class="upload-tip">仅限1张图片，png、jpg、jpeg格式，10M以内，建议图片比例：1:1。</div> -->
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="submitForm(1)" :loading="loading">保存</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script>
  import { disposeEvent } from '../services'
  // import { issueEvent, getQR } from '../services'
  import { getOptions, getComUserList, getDeptUserTree } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormQR',
    components: { AntModal, UploadFile },
    props: [],
    data() {
      return {
        updateInterval: null,

        loading: false,
        modalLoading: false,
        currentLength: 0,

        formTitle: '',
        type: 0,

        previewEditType: 0,
        form: {
          attaches: [],
          disposeContent: '',
          disposeDate: '',
          disposeUserId: null,
          evenId: null,
        },
        open: false,
        deptOptions: [],
        userOptions: [],
        rules: {
          disposeContent: [{ required: true, message: '处置描述不能为空', trigger: 'blur' }],
          disposeDate: [{ required: true, message: '处置时间不能为空', trigger: 'change' }],
          // deptId: [{ required: true, message: '单位不能为空', trigger: 'change' }],
          disposeUserId: [{ required: true, message: '处置人不能为空', trigger: 'change' }],
        },
      }
    },
    created() {
      getComUserList({ isOnlyOrg: true }).then(res => {
        this.userOptions = res?.data?.map(el => ({ value: el.name, key: el.userId }))
      })
      getDeptUserTree().then(res => {
        this.deptOptions = res?.data
        console.log('deptOptions', this.deptOptions)
      })
    },
    mounted() {},
    beforeDestroy() {
      // 组件销毁前清除定时器
      if (this.updateInterval) {
        clearInterval(this.updateInterval)
      }
    },
    methods: {
      handleInputChange(value) {
        // 更新当前输入的字符长度
        this.currentLength = this.form.qrName.length
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      remove() {
        this.$refs.tinyMceRef.remove()
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '事件处理'
        console.log('事件发布 666', row)
        // this.modalLoading = true
        this.form.evenId = row.eventId
        // getQR().then(res => {
        //   if (res.code == 200) {
        //     //附件显示
        //     // this.form = { ...res.data, attaches: [res.data.qrUrl] }
        //     this.form = { ...res.data }
        //     this.currentLength = this.form.qrName.length
        //     this.modalLoading = false
        //   }
        // })
      },

      /** 提交按钮 */
      submitForm(type) {
        this.$refs.form.validate(valid => {
          if (valid) {
            // const saveForm = { qrName: this.form.qrName, qrUrl: this.form.qrUrl }
            this.loading = true
            this.form.attaches = this.form.attaches.length>0 ? [this.form.attaches] : undefined

            console.log("this.form 188",this.form)
            disposeEvent(this.form)
              .then(response => {
                this.$message.success('事件发布成功', 3)
                this.open = false
                this.loading = false
                this.$emit('close')
                this.$emit('ok')
              })
              .catch(err => (this.loading = false))
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .character-count {
    position: relative;
    // display: block;
    width: 80px;
    float: right;
    text-align: right;
    margin-right: 20px;
    margin-top: -26px;
    // background: red;
  }

  .summary-tip {
    position: absolute;
    top: 5px;
    left: 86px;
    width: 330px;
    height: 16px;
  }
  .upload-tip {
    position: absolute;
    bottom: 5px;
    left: 16px;
    width: 420px;
    height: 16px;
    // background: yellowgreen;
  }
</style>
