<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <TreeGeneral
        style="width: 220px"
        ref="treeGeneralRef"
        :treeOptions="treeOptions"
        @onTreeMounted="onTreeMounted"
        @select="clickTreeNode"
      />
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="区划编码">
          <a-input
            v-model="queryParam.districtCode"
            placeholder="请输入区划编码"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="区划名称">
          <a-input
            v-model="queryParam.districtName"
            placeholder="请输入区划名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
    </div>

    <FormDrawer v-if="showFormDrawer" ref="formDrawerRef" @ok="onOperationComplete" @close="showFormDrawer = false" />
  </div>
</template>

<script lang="jsx">
  import { getDistrictPage, DeleteDistric } from './services'
  import { getDistrictTree } from '@/api/common'
  import FormDrawer from './modules/FormDrawer'
  import TreeGeneral from '@/components/TreeGeneral'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'District',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      FormDrawer,
    },
    data() {
      return {
        treeOptions: {
          getDataApi: getDistrictTree,
          replaceFields: {
            children: 'children',
            title: 'districtName',
            key: 'districtId',
            value: 'districtId',
          },
        },
        showFormDrawer: false,
        list: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          districtCode: undefined,
          districtName: undefined,
          parentId: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '区划编码',
            field: 'districtCode',
            minWidth: 130,
          },
          {
            title: '区划名称',
            field: 'districtName',
            minWidth: 180,
          },
          {
            title: '上级目录',
            field: 'parentName',
            minWidth: 180,
          },
          {
            title: '排序号',
            field: 'sort',
            minWidth: 80,
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        // 根据树默认选中id来判断
        if (this.queryParam.parentId) {
          this.getList()
        }
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getDistrictPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          districtCode: undefined,
          districtName: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },
      // 树加载完成后
      onTreeMounted(data) {
        this.queryParam.parentId = data[0].districtId
        this.tableTitle = data[0].districtName

        this.getList()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
        this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.districtId)
        this.names = valObj.records.map(item => item.districtName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      clickTreeNode(node) {
        this.queryParam.parentId = node.$options.propsData.eventKey
        this.tableTitle = node.$options.propsData.dataRef.title

        this.queryParam.pageNum = 1
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleAdd(this.queryParam.parentId))
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const districtIds = row.districtId ? [row.districtId] : this.ids
        const names = row.districtName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return DeleteDistric({ districtIds: districtIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .ant-avatar {
    font-size: 12px;
    border-radius: 4px;
    vertical-align: middle;
    margin-right: 8px;
  }
</style>
