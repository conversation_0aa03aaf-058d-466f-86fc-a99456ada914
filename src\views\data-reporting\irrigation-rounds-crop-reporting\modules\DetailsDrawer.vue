<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    @cancel="handleCancel"
    :maskClosable="false"
    :footer="null"
  >
    <a-spin :spinning="loading">
      <!-- 基本信息部分 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <a-row :gutter="16">
          <a-col :span="24">
            <div class="info-item">
              <span class="info-label">年份：</span>
              <span class="info-value">{{ currentRecord.irriYear || '-' }}</span>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 灌溉轮次及作物部分 -->
      <div class="form-section">
        <h3 class="section-title">灌溉轮次及作物</h3>
        
        <!-- 春夏灌 -->
        <div class="irrigation-item">
          <h4 class="irrigation-title">春夏灌</h4>
          <a-row :gutter="16">
            <a-col :span="12">
              <div class="info-item">
                <span class="info-label">时间选择：</span>
                <span class="info-value">
                  {{ currentRecord.ssStart && currentRecord.ssEnd ? `${currentRecord.ssStart} 至 ${currentRecord.ssEnd}` : '-' }}
                </span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="info-item">
                <span class="info-label">种植作物：</span>
                <span class="info-value">
                  <a-tag v-for="crop in currentRecord.ssCropsNameList" :key="crop" color="blue">
                    {{ crop }}
                  </a-tag>
                  <span v-if="!currentRecord.ssCropsNameList || !currentRecord.ssCropsNameList.length">-</span>
                </span>
              </div>
            </a-col>
          </a-row>
        </div>

        <!-- 秋灌 -->
        <div class="irrigation-item">
          <h4 class="irrigation-title">秋灌</h4>
          <a-row :gutter="16">
            <a-col :span="12">
              <div class="info-item">
                <span class="info-label">时间选择：</span>
                <span class="info-value">
                  {{ currentRecord.autStart && currentRecord.autEnd ? `${currentRecord.autStart} 至 ${currentRecord.autEnd}` : '-' }}
                </span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="info-item">
                <span class="info-label">种植作物：</span>
                <span class="info-value">
                  <a-tag v-for="crop in currentRecord.autCropsNameList" :key="crop" color="green">
                    {{ crop }}
                  </a-tag>
                  <span v-if="!currentRecord.autCropsNameList || !currentRecord.autCropsNameList.length">-</span>
                </span>
              </div>
            </a-col>
          </a-row>
        </div>

        <!-- 秋浇 -->
        <div class="irrigation-item">
          <h4 class="irrigation-title">秋浇</h4>
          <a-row :gutter="16">
            <a-col :span="12">
              <div class="info-item">
                <span class="info-label">时间选择：</span>
                <span class="info-value">
                  {{ currentRecord.autPourStart && currentRecord.autPourEnd ? `${currentRecord.autPourStart} 至 ${currentRecord.autPourEnd}` : '-' }}
                </span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="info-item">
                <span class="info-label">种植作物：</span>
                <span class="info-value">
                  <a-tag v-for="crop in currentRecord.autPourCropsNameList" :key="crop" color="orange">
                    {{ crop }}
                  </a-tag>
                  <span v-if="!currentRecord.autPourCropsNameList || !currentRecord.autPourCropsNameList.length">-</span>
                </span>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 其他信息 -->
      <!-- <div class="form-section">
        <h3 class="section-title">其他信息</h3>
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="info-item">
              <span class="info-label">填报人：</span>
              <span class="info-value">{{ currentRecord.fillUserName || currentRecord.createBy || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="info-item">
              <span class="info-label">填报时间：</span>
              <span class="info-value">{{ formatTime(currentRecord.fillTime || currentRecord.createTime) }}</span>
            </div>
          </a-col>
        </a-row>
      </div> -->

      <div class="modal-footer">
        <a-button type="primary" @click="handleCancel">
          关闭
        </a-button>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import { getIrrigationSettingById } from '../service'

export default {
  name: 'DetailsDrawer',
  data() {
    return {
      title: '查看详情',
      visible: false,
      currentRecord: {},
      loading: false,
    }
  },
      methods: {
    // 显示详情
    showDetails(record) {
      this.visible = true
      this.loading = true
      // 立即设置基本记录信息，确保弹窗可以显示
      this.currentRecord = record
      
      // 调用详情接口获取完整数据
      getIrrigationSettingById(record.id)
        .then(response => {
          if (response.code === 200) {
            this.currentRecord = response.data || record
          } else {
            this.$message.error(response.message || '获取详情失败')
            this.currentRecord = record
          }
        })
        .catch(error => {
          console.error('获取详情失败:', error)
          this.$message.error('获取详情失败，显示基本信息')
          this.currentRecord = record
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return '-'
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },
    
    // 关闭
    handleCancel() {
      this.visible = false
      this.currentRecord = {}
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
.form-section {
  margin-bottom: 32px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.irrigation-item {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  
  .irrigation-title {
    font-size: 14px;
    font-weight: 600;
    // color: #1890ff;
    margin-bottom: 16px;
  }
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  
  .info-label {
    min-width: 80px;
    color: #666;
    font-weight: 500;
  }
  
  .info-value {
    flex: 1;
    color: #333;
    
    .ant-tag {
      margin-right: 8px;
      margin-bottom: 4px;
    }
  }
}

.modal-footer {
  text-align: right;
}
</style> 