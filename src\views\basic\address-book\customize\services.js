import request from '@/utils/request'

// 组织架构-新增
export function addContact(data) {
  return request({
    url: '/base/contact/group/addGroup',
    method: 'post',
    data
  })
}
// 组织架构-删除
export function deleteContact(params) {
  return request({
    url: '/base/contact/group/deleteGroup',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 组织架构-查询
export function getContactPage(data) {
  return request({
    url: '/base/contact/page',
    method: 'post',
    data
  })
}
export function getContactList(params) {
  return request({
    url: '/base/contact/group/getContactList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
