import request from '@/utils/request'

//事件管理-列表分页查询
export function getEventPage(data) {
  return request({
    url: '/patrol/reuse/even/page',
    method: 'post',
    data,
  })
}

// 事件管理-删除
export function deleteEvent(params) {
  return request({
    url: '/patrol/reuse/even/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 事件管理-新增
export function addEvent(data) {
  return request({
    url: '/patrol/reuse/even/add',
    method: 'post',
    data,
  })
}

// 事件管理-事件处理 更新
export function disposeEvent(data) {
  return request({
    url: '/patrol/reuse/even/dispose',
    method: 'post',
    data,
  })
}
// 事件管理-事件发布
export function issueEvent(data) {
  return request({
    url: '/patrol/reuse/even/issue',
    method: 'post',
    data,
  })
}

// 事件管理-详情
export function getEventDetail(params) {
  return request({
    url: '/patrol/reuse/even/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 事件管理-取消
export function eliminateEvent(params) {
  return request({
    url: '/patrol/reuse/even/eliminate',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
