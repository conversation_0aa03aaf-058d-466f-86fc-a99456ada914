import request from '@/utils/request'

export function getDispatchProjectList(params) {
  return request({
    url: '/custom/dispatch-project/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

export function getProjectOption(params) {
  return request({
    url: '/custom/dispatch-project/project/option',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

export function addDispatchProject(params) {
  return request({
    url: '/custom/dispatch-project/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 删除工程
export function deleteDispatchProject(params) {
  return request({
    url: '/custom/dispatch-project/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

export function getUserOption(params) {
  return request({
    url: '/custom/dispatch-project/user/option',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

export function getUserList(params) {
  return request({
    url: '/custom/dispatch-project/user/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 添加工程
export function addUser(params) {
  return request({
    url: '/custom/dispatch-project/user/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 删除工程
export function deleteUser(params) {
  return request({
    url: '/custom/dispatch-project/user/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
