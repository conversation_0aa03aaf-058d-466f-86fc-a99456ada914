import request from '@/utils/request'

// 水雨情简报-计划配置
export function getPlanList(data) {
  return request({
    url: '/war/reportConfig/page',
    method: 'post',
    data
  })
}
// 增加
export function addPlan(data) {
  return request({
    url: '/war/reportConfig/add',
    method: 'post',
    data
  })
}
// 详情
export function getPlanById(params) {
  return request({
    url: '/war/reportConfig/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 更新
export function editPlan(data) {
  return request({
    url: '/war/reportConfig/update',
    method: 'post',
    data
  })
}
// 生成时间预测
export function generateTimeForecastApi(data) {
  return request({
    url: '/war/reportConfig/forecasting/get',
    method: 'post',
    data
  })
}
// 删除
export function deletePlan(params) {
  return request({
    url: '/war/reportConfig/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 作废
export function cancelPlan(params) {
  return request({
    url: '/war/reportConfig/cancel/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 启用
export function enablePlan(params) {
  return request({
    url: '/war/reportConfig/start/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 停用
export function deactivatePlan(params) {
  return request({
    url: '/war/reportConfig/stop/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 执行一次
export function triggerOncePlan(params) {
  return request({
    url: '/war/reportConfig/triggerOnce',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}