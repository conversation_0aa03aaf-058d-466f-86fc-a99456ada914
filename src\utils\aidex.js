/**
 * 通用js方法封装处理
 * Copyright (c) 2023 Scaffold-Admin
 */

const baseURL = process.env.VUE_APP_BASE_API

import moment from 'moment'

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/')
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const timeStr = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return timeStr
}

// 表单重置
export function resetForm(refName) {
  if (this[refName]) {
    this[refName].resetFields()
  }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  var search = params
  search.params = {}
  if (dateRange !== null && dateRange !== '' && dateRange.length === 2) {
    if (typeof propName === 'undefined') {
      search.params['beginTime'] = dateRange[0]
      search.params['endTime'] = dateRange[1]
    } else {
      search.params[propName + 'BeginTime'] = dateRange[0]
      search.params[propName + 'EndTime'] = dateRange[1]
    }
  }
  return search
}

// 回显系统数据字典
export function selectSysDictLabel(datas, value) {
  var actions = []
  Object.keys(datas).some(key => {
    if (datas[key].dictKey === '' + value) {
      actions.push(datas[key].dictValue)
      return true
    }
  })
  return actions.join('')
}

// 回显数据字典
export function selectDictLabel(datas, value) {
  var actions = []
  Object.keys(datas).some(key => {
    if (datas[key].dictValue === '' + value) {
      actions.push(datas[key].dictLabel)
      return true
    }
  })
  return actions.join('')
}

// 回显数据字典（字符串数组）
export function selectDictLabels(datas, value, separator) {
  var actions = []
  var currentSeparator = undefined === separator ? ',' : separator
  var temp = value.split(currentSeparator)
  Object.keys(value.split(currentSeparator)).some(val => {
    Object.keys(datas).some(key => {
      if (datas[key].dictValue === '' + temp[val]) {
        actions.push(datas[key].dictLabel + currentSeparator)
      }
    })
  })
  return actions.join('').substring(0, actions.join('').length - 1)
}

//回显应用名称
export function selectMenuLabel(datas, value) {
  var actions = []
  Object.keys(datas).some(key => {
    if (datas[key].menuCode == '' + value) {
      actions.push(datas[key].menuName)
      return true
    }
  })
  return actions.join('')
}

// 通用下载方法
export function download(fileName, delFlag) {
  if (delFlag === undefined) {
    delFlag = true
  }
  window.location.href = baseURL + '/common/download?fileName=' + encodeURI(fileName) + '&delete=' + delFlag
}
// 通用下载方法
export function downloadByPath(filePath, delFlag) {
  if (delFlag === undefined) {
    delFlag = true
  }
  window.location.href = baseURL + '/common/downloadByPath?filePath=' + encodeURI(filePath) + '&delete=' + delFlag
}
// 通用下载到导出任务中心
export function downloadTask() {
  this.$router.push({
    name: 'SysDownloadFiles',
    params: {
      key: new Date().toLocaleString(),
    },
  })
}

// 字符串格式化(%s )
export function sprintf(str) {
  var args = arguments
  var flag = true
  var i = 1
  str = str.replace(/%s/g, function () {
    var arg = args[i++]
    if (typeof arg === 'undefined') {
      flag = false
      return ''
    }
    return arg
  })
  return flag ? str : ''
}

// 转换字符串，undefined,null等转化为''
export function praseStrEmpty(str) {
  if (!str || str === 'undefined' || str === 'null') {
    return ''
  }
  return str
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 * @param {*} rootId 根Id 默认 0
 */
export function handleTree(data, id, parentId, children, rootId) {
  id = id || 'id'
  parentId = parentId || 'parentId'
  children = children || 'children'
  rootId = rootId || '0'
  // 对源数据深度克隆
  const cloneData = JSON.parse(JSON.stringify(data))
  // 循环所有项
  const treeData = cloneData.filter(father => {
    var branchArr = cloneData.filter(child => {
      // 返回每一项的子级数组
      return father[id] === child[parentId]
    })

    if (branchArr.length > 0) {
      father.children = branchArr
    } else {
      father.children = ''
    }
    // 返回第一层
    return father[parentId] === rootId
  })
  return treeData !== '' && treeData == null ? treeData : data
}
/**
 * 从树中移除指定节点
 * @param {Object} list
 * @param {Object} node
 */
export function removeTreeNode(list, node) {
  const parentList = list
  const parentIds = node.parentIds.split('/')
  const currentNodeId = node.id
  deleteTreeNode(parentList, list, parentIds, currentNodeId)
}
export function deleteTreeNode(parentList, list, parentIds, currentNodeId) {
  for (let s = 0; s < list.length; s++) {
    if (list[s].id === currentNodeId) {
      list.splice(s, 1)
      // 当删除节点后，没有子节点时需要修改父节点为叶子节点
      if (list.length === 0) {
        parentList.treeLeaf = 'y'
      }
      return
    } else if (list[s].children && list[s].children.length > 0) {
      // 递归条件
      parentIds.splice(0, 1)
      deleteTreeNode(list[s], list[s].children, parentIds, currentNodeId)
    } else {
      continue
    }
  }
}
export function appendTreeNode(node, data) {
  if (node.treeLeaf === 'y') {
    // 如果节点是叶子节点则直接改为非叶子节点
    node.treeLeaf = 'n'
    node.children.push(data)
  } else {
    const children = node.children
    if (children.length > 0) {
      // 有子节点则直接push数据，否则不做操作等待异步加载
      node.children.push(data)
    }
  }
}
/**
 * 按展开几层展开树
 * @param {Object} nodes
 * @param {Object} expandLevel
 * @param {Object} expandedRowKeys 记录展开key
 */
export function expandTree(nodes, expandLevel, expandedRowKeys) {
  if (expandLevel > 1) {
    // 最后一层不展开
    nodes.forEach(node => {
      expandedRowKeys.push(node.id)
      expandLevel = expandLevel - 1
      return expandTree(node.children, expandLevel, expandedRowKeys)
    })
  }
}

//取日期月份
export function getMonthsBetweenDates(startDate, endDate) {
  let months = []
  let currentDate = moment(startDate).startOf('month')
  let lastDate = moment(endDate).startOf('month')

  while (currentDate <= lastDate) {
    months.push(currentDate.format('M月'))
    currentDate = currentDate.clone().add(1, 'month')
  }

  return months
}
