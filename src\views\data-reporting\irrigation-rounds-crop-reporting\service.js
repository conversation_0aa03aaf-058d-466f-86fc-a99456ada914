import request from '@/utils/request'

// 灌溉轮次作物上报

/**
 * 新增灌溉轮次作物上报
 * @param {Object} data 新增参数
 * @param {string} data.autCropsCodes 秋灌农作物code列表，多个用逗号分隔
 * @param {string} data.autEnd 秋灌结束时间
 * @param {string} data.autPourCropsCodes 秋浇农作物code列表，多个用逗号分隔
 * @param {string} data.autPourEnd 秋浇结束
 * @param {string} data.autPourStart 秋浇开始
 * @param {string} data.autStart 秋灌开始
 * @param {number} data.irriYear 填报年份
 * @param {string} data.ssCropsCodes 春夏农作物code列表，多个用逗号分隔
 * @param {string} data.ssEnd 春夏灌结束
 * @param {string} data.ssStart 春夏灌开始
 */
export function addIrrigationSetting(data) {
    return request({
        url: '/custom/irrigationSetting/add',
        method: 'post',
        data
    })
}

/**
 * 删除灌溉轮次作物上报
 * @param {number} id 记录ID
 */
export function deleteIrrigationSetting(id) {
    return request({
        url: '/custom/irrigationSetting/delete',
        method: 'post',
        params: { id }
    })
}

/**
 * 修改灌溉轮次作物上报
 * @param {Object} data 修改参数
 * @param {string} data.autCropsCodes 秋灌农作物code列表，多个用逗号分隔
 * @param {string} data.autEnd 秋灌结束时间
 * @param {string} data.autPourCropsCodes 秋浇农作物code列表，多个用逗号分隔
 * @param {string} data.autPourEnd 秋浇结束
 * @param {string} data.autPourStart 秋浇开始
 * @param {string} data.autStart 秋灌开始
 * @param {number} data.id 记录ID
 * @param {number} data.irriYear 填报年份
 * @param {string} data.ssCropsCodes 春夏农作物code列表，多个用逗号分隔
 * @param {string} data.ssEnd 春夏灌结束
 * @param {string} data.ssStart 春夏灌开始
 */
export function updateIrrigationSetting(data) {
    return request({
        url: '/custom/irrigationSetting/update',
        method: 'post',
        data
    })
}

/**
 * 查看灌溉轮次作物上报详情
 * @param {number} id 记录ID
 */
export function getIrrigationSettingById(id) {
    return request({
        url: '/custom/irrigationSetting/get',
        method: 'post',
        params: { id }
    })
}

/**
 * 分页查询灌溉轮次作物上报列表
 * @param {Object} data 查询参数
 * @param {number} data.irriYear 填报年份
 * @param {number} data.pageNum 页码，默认1
 * @param {number} data.pageSize 每页条数，默认10
 * @param {Array} data.sort 排序字段
 */
export function getIrrigationSettingPage(data) {
    return request({
        url: '/custom/irrigationSetting/page',
        method: 'post',
        data
    })
}