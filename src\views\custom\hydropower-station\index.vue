<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="调度日期">
        <a-range-picker
          allow-clear
          :value="takeEffect"
          format="YYYY-MM-DD"
          formatValue="YYYY-MM-DD"
          :placeholder="['开始日期', '结束日期']"
          @change="onRangeChange"
        />
      </a-form-item>

      <a-form-item label="调度类型">
        <a-radio-group v-model="queryParam.dispatchType">
          <a-radio value="1">日常调度</a-radio>
          <a-radio value="2">临时调度</a-radio>
        </a-radio-group>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="水电站调度管理"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>

        <FormHydropower
          v-if="showFormHydropower"
          ref="formHydropowerRef"
          @ok="getList"
          @close="showFormHydropower = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getHydropowerStation, deleteHydropowerStation } from './services'
  import FormHydropower from './modules/FormHydropower'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'HydropowerStation',
    components: {
      VxeTableForm,
      VxeTable,
      FormHydropower,
    },
    data() {
      return {
        showFormHydropower: false,
        takeEffect: [],
        list: [],
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          dispatchType: undefined,
          endTime: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
          startTime: undefined,
        },
        columns: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '调度单编号',
            field: 'serialNumber',
            minWidth: 120,
          },
          {
            title: '调度日期',
            field: 'dispatchDate',
            minWidth: 60,
          },
          {
            title: '调度类型',
            field: 'dispatchType',
            minWidth: 60,
            slots: {
              default: ({ row }) => {
                return row.dispatchType === 1 ? '日常调度' : '临时调度'
              },
            },
          },
          {
            title: '开启设备',
            field: 'deviceNames',
            minWidth: 120,
          },
          {
            title: '总计开启时长(h)',
            field: 'openHour',
            minWidth: 100,
          },
          {
            title: '预计发电量(kw·h)',
            field: 'warnFlow',
            minWidth: 90,
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 100,
          },
          {
            title: '操作',
            field: 'operate',
            width: 96,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    created() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showFormHydropower = false
        this.loading = true
        this.selectChange({ records: [] })
        getHydropowerStation(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.takeEffect = []
        this.queryParam = {
          ...this.queryParam,
          dispatchType: undefined,
          endTime: undefined,
          pageNum: 1,
          sort: [],
          startTime: undefined,
        }
        this.handleQuery()
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.startTime = dateString[0]
        this.queryParam.endTime = dateString[1]
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.id)
        this.names = valObj.records.map(item => item.serialNumber)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      /* 新增 */
      handleAdd() {
        this.showFormHydropower = true
        this.$nextTick(() => this.$refs.formHydropowerRef.handleAdd(this.queryParam.parentId))
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormHydropower = true
        this.$nextTick(() => this.$refs.formHydropowerRef.handleUpdate(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row.id ? [row.id] : this.ids
        const names = row.serialNumber || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中编号为"' + names + '"的数据',
          onOk() {
            return deleteHydropowerStation({ ids: ids.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.getList()
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
