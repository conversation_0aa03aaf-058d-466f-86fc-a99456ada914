<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <div class="flex row-pad">
        <a-form-item label="年份">
          <a-date-picker
            :allowClear="false"
            mode="year"
            format="YYYY"
            v-model="yearValue"
            placeholder="请选择"
            allow-clear
            :open="yearShowOne"
            style="width: 120px"
            @keyup.enter.native="handleQuery"
            @openChange="openChangeOne"
            @panelChange="panelChangeOne"
          ></a-date-picker>
        </a-form-item>
      </div>

      <template #tab>
        <div
          class="row-pad"
          style="padding-top: 15px; display: flex; justify-content: space-between; align-items: center"
        >
          <a-radio-group
            default-value="1"
            v-model="queryParam.irrigationRound"
            button-style="solid"
            @change="onClickType"
          >
            <a-radio-button value="1">{{ irrigationDate1 }}水量决算</a-radio-button>
            <a-radio-button value="2">{{ irrigationDate2 }}水量决算</a-radio-button>
            <a-radio-button value="3">{{ irrigationDate3 }}水量决算</a-radio-button>
            <a-radio-button value="4">全年水量决算</a-radio-button>
            <!-- <a-radio-button value="5">总局汇总表</a-radio-button> -->
          </a-radio-group>
          <div class="table-operations">
            <a-button type="primary" @click="handleImport" icon="import" :loading="importLoading">导入</a-button>
            <a-button type="primary" @click="handleExport" icon="export" :loading="exportLoading">导出</a-button>
          </div>
        </div>
      </template>

      <template #table>
        <VxeGrid
          v-if="queryParam.type == '5'"
          ref="vxeTableRef"
          :border="true"
          :columns="columns"
          :tableData="tableData"
          :tableTitle="tableTitle"
          :loading="loading"
          :isAdaptPageSize="false"
          :tablePage="false"
          :show-footer="true"
          :footer-data="footerData"
          :mergeCells="mergeCells"
        ></VxeGrid>

        <div class="table-box" v-else>
          <div v-for="el in waterVolumeList" :key="el.id">
            <div class="table-title">
              河套灌区{{
                el?.channelName +
                queryParam.accountYear +
                '年' +
                (queryParam.irrigationRound
                  ? irrigationDate1
                  : queryParam.irrigationRound == 2
                    ? irrigationDate2
                    : queryParam.irrigationRound == 3
                      ? irrigationDate3
                      : queryParam.irrigationRound == 4
                        ? irrigationDate4
                        : '')
              }}用水量决算表
            </div>
            <vxe-table border style="min-height: 300px" :data="el.tableList" :mergeCells="monthsMergeCells">
              <vxe-colgroup title="单位：万立米" header-align="right">
                <vxe-column title="单位" minWidth="60" align="center">
                  <template #default="{ row }">
                    <div>
                      {{
                        el.depId == -1
                          ? '永济渠'
                          : el.depId == -2
                            ? '永济灌域'
                            : deptOptions.find(item => item.deptId == el.depId)?.deptName
                      }}
                    </div>
                  </template>
                </vxe-column>
                <vxe-column field="canalHeadName" title="渠首水量" minWidth="100" align="center"></vxe-column>
                <vxe-column field="canalHeadVal" title="" minWidth="60" align="center"></vxe-column>

                <vxe-column field="channelName" title="直口级别" minWidth="70" align="center">
                  <template #default="{ row }">
                    <div>
                      {{
                        row.channelType == 1
                          ? '支'
                          : row.channelType == 2
                            ? '斗'
                            : row.channelType == 3
                              ? '农'
                              : row.channelType == 4
                                ? '毛'
                                : row.channelType == '合计'
                                  ? '合计'
                                  : ''
                      }}
                    </div>
                  </template>
                </vxe-column>

                <vxe-column
                  field="irrArea"
                  title="直口面积(亩)"
                  minWidth="80"
                  :formatter="formatDecimal"
                  align="center"
                ></vxe-column>
                <vxe-column
                  field="contractedVolume"
                  title="直口包干水量"
                  minWidth="90"
                  :formatter="formatDecimal"
                ></vxe-column>

                <vxe-colgroup title="直口渠实用水量" header-align="center">
                  <vxe-column
                    field="totalInQuotaWater"
                    title="指标内"
                    minWidth="60"
                    :formatter="formatDecimal"
                  ></vxe-column>
                  <vxe-column
                    field="totalActualUsage"
                    title="实用水量"
                    minWidth="70"
                    :formatter="formatDecimal"
                  ></vxe-column>
                  <vxe-colgroup title="超水" header-align="center">
                    <vxe-column
                      field="totalOver20Water"
                      title="≤20%"
                      minWidth="60"
                      :formatter="formatDecimal"
                    ></vxe-column>
                    <vxe-column
                      field="totalOver20To50Water"
                      title="20%<超水水量≤50%"
                      minWidth="146"
                      :formatter="formatDecimal"
                      align="center"
                    ></vxe-column>
                  </vxe-colgroup>
                  <vxe-column
                    field="totalSavedWater"
                    title="节水"
                    minWidth="50"
                    :formatter="formatDecimal"
                    align="center"
                  ></vxe-column>
                  <vxe-column
                    field="multipleSupply"
                    title="多元化供水"
                    minWidth="80"
                    :formatter="formatDecimal"
                    align="center"
                  ></vxe-column>
                  <vxe-column title="水量合计" minWidth="90" align="center">
                    <template #default="{ row }">
                      <div v-if="row?.totalActualUsage || row?.multipleSupply">
                        {{ (row?.totalActualUsage || 0 + row?.multipleSupply || 0).toFixed(2) }}
                      </div>
                    </template>
                  </vxe-column>
                </vxe-colgroup>

                <vxe-colgroup title="直口渠水费" header-align="center">
                  <vxe-column
                    field="inQuotaFee"
                    title="指标内水费"
                    minWidth="80"
                    :formatter="formatDecimal"
                  ></vxe-column>
                  <vxe-colgroup title="超水水费" header-align="center">
                    <vxe-column
                      field="feeLeTwenty"
                      title="≤20%"
                      minWidth="60"
                      :formatter="formatDecimal"
                      align="center"
                    ></vxe-column>
                    <vxe-column
                      field="feeGeTwenty"
                      title="20%<水费≤50%"
                      minWidth="120"
                      :formatter="formatDecimal"
                      align="center"
                    ></vxe-column>
                  </vxe-colgroup>
                  <vxe-column title="水费合计(元)" minWidth="90" align="center">
                    <template #default="{ row }">
                      <div v-if="row?.inQuotaFee || row?.feeLeTwenty || row?.feeGeTwenty">
                        {{ (row?.inQuotaFee || 0 + row?.feeLeTwenty || 0 + row?.feeGeTwenty || 0).toFixed(2) }}
                      </div>
                    </template>
                  </vxe-column>
                </vxe-colgroup>
              </vxe-colgroup>
            </vxe-table>
          </div>
        </div>
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import VxeGrid from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import { getWaterVolumeStatistics } from './services'
  import { getIrrigationDate } from '../water-rate-water-rent/services'
  import moment from 'moment'
  import { getTreeByLoginOrgId } from '@/api/common.js'
  import { getDeptList } from '../water-rate-water-rent/services.js'

  export default {
    name: 'WaterVolumeSummary',
    components: {
      VxeTableForm,
      VxeGrid,
    },
    data() {
      return {
        importLoading: false,
        exportLoading: false,
        loading: false,
        yearShowOne: false,
        tableTitle: '',

        deptOptions: [],

        exportLoading: false,
        queryParam: {
          irrigationRound: '1',
          accountYear: moment().year(),
        },
        yearValue: moment(),
        mergeCells: [],
        waterVolumeList: [],

        monthsMergeCells: [],
        irrigationDate1: '',
        irrigationDate2: '',
        irrigationDate3: '',

        columns: [
          {
            title: '',
            field: 'name',
            align: 'center',
            width: 150,
            fixed: 'left',
            slots: {
              header: ({ column }) => {
                return (
                  <div class='first-col'>
                    <div class='first-col-top'>水量</div>
                    <div class='first-col-bottom'>项目 &nbsp;时段</div>
                  </div>
                )
              },
            },
          },
          {
            title: '',
            field: 'deptName',
            width: 120,
          },
          {
            title: '管理所(万m³)',
            field: 'group1',
            headerAlign: 'center',
            children: [
              { field: 'sex', title: '合济渠供水所' },
              { field: 'age', title: '南边渠供水所' },
              { field: 'age', title: '北边渠供水所' },
              { field: 'age', title: '永兰渠供水所' },
              { field: 'age', title: '永刚渠供水所' },
              { field: 'age', title: '西乐渠供水所' },
              { field: 'age', title: '新华渠供水所' },
              { field: 'age', title: '正稍渠供水所' },
            ],
          },
          {
            title: '永济干渠渠损(万m³)',
            field: 'sex',
            sex: '0',
            age: 0,
          },
          {
            title: '灌域合计(万m³)',
            field: 'sex',
            sex: '0',
            age: 0,
          },
          // { field: 'sex', title: '北边扬水', sex: '0', age: 0 },
        ],
        tableData: [],
        footerData: [{ name: '国管渠道效率%', value: '5' }],
      }
    },
    computed: {},
    watch: {},

    created() {
      getTreeByLoginOrgId().then(treeRes => {
        this.deptOptions.push(treeRes?.data[0])
        getDeptList({ parentId: 10020 }).then(res => {
          this.deptOptions = this.deptOptions.concat(res?.data)
        })
      })
      this.getMonths()
    },
    methods: {
      getMonths() {
        Promise.allSettled([
          getIrrigationDate({ fillYear: this.queryParam.accountYear, irrigationRound: 1 }),
          getIrrigationDate({ fillYear: this.queryParam.accountYear, irrigationRound: 2 }),
          getIrrigationDate({ fillYear: this.queryParam.accountYear, irrigationRound: 3 }),
        ]).then(results => {
          const res1 = results[0]?.value?.data
          const res2 = results[1]?.value?.data
          const res3 = results[2]?.value?.data

          this.irrigationDate1 = moment(res1?.ssStart).format('M月') + '-' + moment(res1?.ssEnd).format('M月')
          this.irrigationDate2 = moment(res2?.ssStart).format('M月') + '-' + moment(res2?.ssEnd).format('M月')
          this.irrigationDate3 = moment(res3?.ssStart).format('M月') + '-' + moment(res3?.ssEnd).format('M月')

          this.getList()
        })
      },
      onClickType() {
        if (this.queryParam.irrigationRound == '5') {
          this.$nextTick(() => {
            this.mergeCells = [
              { row: 0, col: 0, rowspan: 4, colspan: 1 },
              { row: 4, col: 0, rowspan: 4, colspan: 1 },
              { row: 8, col: 0, rowspan: 4, colspan: 1 },
              { row: 12, col: 0, rowspan: 4, colspan: 1 },
              { row: 16, col: 0, rowspan: 4, colspan: 1 },
              { row: 20, col: 0, rowspan: 4, colspan: 1 },
            ]
          })
        }
        this.getList()
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        this.yearShowOne = status
      },

      // 得到年份选择器的值
      panelChangeOne(value) {
        this.yearValue = value
        this.queryParam.accountYear = moment(value).format('YYYY')
        this.yearShowOne = false

        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.loading = true

        getWaterVolumeStatistics(this.queryParam).then(response => {
          this.waterVolumeList = response?.data?.cstIrrigationWaterAccountDetail1VOList

          if (this.queryParam.irrigationRound != '5') {
            this.waterVolumeList.forEach(el => {
              el.canalHead = [
                { canalHeadName: '包干', canalHeadVal: el.allocatedVolume },
                { canalHeadName: '实引', canalHeadVal: el.actualVolume },
                { canalHeadName: '超水', canalHeadVal: el.overVolume },
                { canalHeadName: '节水', canalHeadVal: el.savedVolume },
                { canalHeadName: '国管渠道有效利用系数', canalHeadVal: el.efficiencyCoefficient },
              ]
              el.tableList = []
              const maxLength = Math.max(el.canalHead.length, el.waterStatsList.length)
              for (let i = 0; i < maxLength; i++) {
                const row1 = el.canalHead[i]
                const row2 = el.waterStatsList[i]

                el.tableList.push({
                  ...row1,
                  ...row2,
                })
              }
              this.$nextTick(() => {
                this.monthsMergeCells = [
                  { row: 0, col: 0, rowspan: 8, colspan: 0 },
                  // { row: 4, col: 1, rowspan: 1, colspan: 3 },
                ]
              })
            })

            for (let i = 0; i < this.waterVolumeList.length; i++) {
              const el = this.waterVolumeList[i]

              el.irrAreaSum = 0
              el.contractedVolumeSum = 0
              el.totalInQuotaWaterSum = 0
              el.totalActualUsageSum = 0
              el.totalOver20WaterSum = 0
              el.totalOver20To50WaterSum = 0
              el.totalSavedWaterSum = 0
              el.multipleSupplySum = 0
              el.inQuotaFeeSum = 0
              el.feeLeTwentySum = 0
              el.feeGeTwentySum = 0

              if (el?.tableList?.length > 0) {
                el.tableList.forEach(item => {
                  el.irrAreaSum += item.irrArea || 0
                  el.contractedVolumeSum += item.contractedVolume || 0
                  el.totalInQuotaWaterSum += item.totalInQuotaWater || 0
                  el.totalActualUsageSum += item.totalActualUsage || 0
                  el.totalOver20WaterSum += item.totalOver20Water || 0
                  el.totalOver20To50WaterSum += item.totalOver20To50Water || 0
                  el.totalSavedWaterSum += item.totalSavedWater || 0
                  el.multipleSupplySum += item.multipleSupply || 0
                  el.inQuotaFeeSum += item.inQuotaFee || 0
                  el.feeLeTwentySum += item.feeLeTwenty || 0
                  el.feeGeTwentySum += item.feeGeTwenty || 0
                })
              }
              el.tableList.push({
                canalHeadName: '合计',
                irrArea: el.irrAreaSum,
                contractedVolume: el.contractedVolumeSum,
                totalInQuotaWater: el.totalInQuotaWaterSum,
                totalActualUsage: el.totalActualUsageSum,
                totalOver20Water: el.totalOver20WaterSum,
                totalOver20To50Water: el.totalOver20To50WaterSum,
                totalSavedWater: el.totalSavedWaterSum,
                multipleSupply: el.multipleSupplySum,
                inQuotaFee: el.inQuotaFeeSum,
                feeLeTwenty: el.feeLeTwentySum,
                feeGeTwenty: el.feeGeTwentySum,
              })
            }
          }

          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.handleQuery()
      },

      formatDecimal({ cellValue }) {
        return cellValue ? Number(cellValue).toFixed(2) : ''
      },

      handleImport() {},
      handleExport() {},
    },
  }
</script>
<style lang="less" scoped>
  .common-table-page {
    background: #fff;
    .flex,
    .ant-form-item {
      display: flex;
    }
    .row-pad {
      padding: 0 15px;
    }
  }
  .table-title {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    margin: 15px 0;
  }
  .table-box {
    width: 98%;
    margin: 20px auto;
    overflow-y: auto;
    overflow-x: hidden;
    :deep(.vxe-table--render-default .vxe-cell) {
      padding: 6px 6px !important;
      min-height: 30px !important;
    }
  }
</style>
