<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="方案编码">
        <a-input v-model="queryParam.dispatchCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="方案时间">
        <a-range-picker
          allow-clear
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          v-model="queryParam.dateRange"
          @openChange="() => (disabledDealDate = null)"
          @change="onRangeChange"
          @calendarChange="calendarChange"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          :isInModal="isDetail"
          :modalHeight="modalHeight"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer v-if="showForm" ref="formRef" @ok="onOperationComplete" @close="showForm = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getDispatchPage, deleteDispatch } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'DispatchCase',
    components: {
      VxeTable,
      VxeTableForm,
      FormDrawer,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      type: {
        type: Number,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        projects: [],
        projectOptions: [],
        isChecked: false,
        showForm: false,
        showFormDetails: false,
        archivesOptions: [],

        list: [],
        tableTitle: '调度方案管理',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          dispatchCode: '',
          startDate: null,
          endDate: null,
          dateRange: [],
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 40 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '方案编码',
            field: 'dispatchCode',
            minWidth: 120,
          },
          {
            title: '方案时间',
            field: 'dispatchDate',
            minWidth: 180,
          },
          {
            title: '附件',
            field: 'attachUrl',
            width: 60,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.openAttach(row)}>查看</a>
                  </span>
                )
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 138,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {},
    methods: {
      openAttach(row) {
        window.open(row.attachUrl)
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.startDate = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') + ' 00:00:00' : null
        this.queryParam.endDate = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') + ' 23:59:59' : null
      },

      calendarChange(dates) {
        if (dates?.length == 1) {
          this.disabledDealDate = dates[0]
        }
      },

      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.yearShowOne = false
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getDispatchPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.dispatchId)
        this.names = valObj.records.map(item => item.dispatchCode)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          dispatchCode: '',
          startDate: null,
          endDate: null,
          dateRange: [],
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.dispatchId ? [row?.dispatchId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteDispatch({ dispatchIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
