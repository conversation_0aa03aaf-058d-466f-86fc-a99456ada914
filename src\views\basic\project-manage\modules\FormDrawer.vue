<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="550"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="工程编码" prop="projectCode">
                <a-input allowClear v-model="form.projectCode" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="工程名称" prop="projectName">
                <a-input allowClear v-model="form.projectName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="工程简称" prop="projectNameAbbr">
                <a-input allowClear v-model="form.projectNameAbbr" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="工程类别" prop="objectCategoryId">
                <a-tree-select
                  v-model="form.objectCategoryId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="projectOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'objectCategoryName',
                    key: 'objectCategoryId',
                    value: 'objectCategoryId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="行政区划" prop="districtCode">
                <a-tree-select
                  v-model="form.districtCode"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="districtOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'districtName',
                    key: 'districtCode',
                    value: 'districtCode',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属上级工程" prop="parentId">
                <a-tree-select
                  v-model="form.parentId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="parentOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'projectName',
                    key: 'projectId',
                    value: 'projectId',
                  }"
                  tree-default-expand-all
                  @select="onProjectSelect"
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="工程类型">
                <a-select
                  allowClear
                  :maxTagCount="2"
                  v-model="form.projectType"
                  placeholder="请选择"
                  option-filter-prop="children"
                >
                  <a-select-option v-for="(d, index) in projectTypeOptions" :key="index" :value="d.key">
                    {{ d.value }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

            <!-- <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属单位" prop="deptId">
                <a-select
                  allowClear
                  v-model="form.deptId"
                  placeholder="请选择"
                  option-filter-prop="children"
                >
                  <a-select-option v-for="(d, index) in deptOptions" :key="index" :value="d.value">
                    {{ d.label }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col> -->
             <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属单位" prop="deptId">
                <a-tree-select
                  allowClear
                  v-model="form.deptId"
                  placeholder="请选择"
                  option-filter-prop="children"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="deptOptions"
                  show-search
                  treeNodeFilterProp="title"
                  :replaceFields="{
                    children: 'children',
                    title: 'deptName',
                    key: 'deptId',
                    value: 'deptId',
                  }"
                  tree-default-expand-all
                  @select="onDeptSelect"
                >
                </a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="经度" prop="longitude">
                <a-input-number
                  :min="73.66"
                  :max="135.05"
                  style="width: 100%"
                  v-model="form.longitude"
                  placeholder="请输入"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="纬度" prop="latitude">
                <a-input-number
                  :min="3.86"
                  :max="53.55"
                  style="width: 100%"
                  v-model="form.latitude"
                  placeholder="请输入"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="所在位置" prop="location">
                <a-input allowClear v-model="form.location" placeholder="请输入">
                  <a-icon
                    style="font-size: 20px; cursor: pointer"
                    @click="onMapOpen"
                    slot="addonAfter"
                    type="environment"
                    theme="twoTone"
                  />
                </a-input>
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="封面图">
                <UploadFile
                  :fileUrl.sync="form.projectImgUrl"
                  :multiple="false"
                  listType="picture-card"
                  folderName="projectCover"
                  accept=".png,.jpg,.jpeg"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="排序号" prop="sort">
                <a-input-number v-model="form.sort" style="width: 100%" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="备注" prop="remark">
                <a-textarea v-model="form.remark" placeholder="请输入" allow-clear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <MapModal ref="mapModalRef" @close="showMapModal = false" @confirm="onMapModalConfirm" />
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getNextSort, getParentTree, addProject, updateProject, getProject, getConfigTree } from '../services'
  import MapModal from '@/components/MapBox/MapboxModal.vue'
  import UploadFile from '@/components/UploadFile/index.vue'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormDrawer',
    props: ['projectOptions', 'districtOptions', 'projectTypeOptions'],
    components: { MapModal, UploadFile, AntModal },
    data() {
      return {
        showMapModal: false,
        parentOptions: [],
        deptOptions: [],

        loading: false,
        modalLoading: false,
        formTitle: '',
        open: false,
        rowInfo: null,

        // 表单参数
        form: {
          projectCode: undefined,
          projectName: undefined,
          projectNameAbbr: undefined,
          objectCategoryId: undefined,
          districtCode: undefined,
          projectId: undefined,
          parentId: undefined,
          deptId: undefined,
          longitude: undefined,
          latitude: undefined,
          location: undefined,
          projectImgUrl: undefined,
          sort: 0,
          remark: undefined,
        },

        rules: {
          projectName: [{ required: true, message: '工程名称不能为空', trigger: 'blur' }],
          projectCode: [{ required: true, message: '工程编码不能为空', trigger: 'blur' }],
          objectCategoryId: [{ required: true, message: '工程类别不能为空', trigger: 'change' }],
          districtCode: [{ required: true, message: '行政区划不能为空', trigger: 'change' }],
          // deptId: [{ required: true, message: '所属单位不能为空', trigger: 'change' }],
          // parentId: [{ required: true, message: '上级工程不能为空', trigger: 'change' }],
          sort: [{ required: true, message: '排序号不能为空', trigger: 'blur' }],
        },
      }
    },
    filters: {},
    created() {
      // 字典获取下拉
      this.getDeptOptions()
    },
    computed: {},
    watch: {},
    methods: {
      // 获取部门选项
      getDeptOptions() {
        getConfigTree({
          deptName: null,
          // type: '1',  
        }).then(res => {
          this.deptOptions = res?.data || []
          console.log('工程 单位 数据 ', this.deptOptions)
        })
      },
      // 打开地图
      onMapOpen() {
        this.showMapModal = true
        const mapInfo = {
          longitude: this.form.longitude,
          latitude: this.form.latitude,
          location: this.form.location,
        }
        this.$nextTick(() => this.$refs.mapModalRef.handleOpen(mapInfo))
      },
      onMapModalConfirm(mapInfo) {
        this.form.longitude = mapInfo.longitude
        this.form.latitude = mapInfo.latitude
        this.form.location = mapInfo.location
        this.showMapModal = false
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd(type, r, treeTabType) {
        this.open = true
        this.formTitle = '新增'
        if (treeTabType === '1') {
          this.form.objectCategoryId = r.objectCategoryId || undefined
        }
        if (treeTabType === '2') {
          this.form.districtCode = r.districtCode || undefined
        }
        this.getParentTree()
        if (type === 'root') {
          this.rules.parentId[0].required = false
        } else if (type === 'leaf') {
          this.rowInfo = r

          getNextSort({ parentId: r.projectId }).then(res => {
            this.form.sort = res.data
          })
          this.form.parentId = r.projectId
        }
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        // this.getParentTree(record)
        this.rowInfo = record

        this.getParentTree(record)

        this.modalLoading = true
        getProject({ projectId: record.projectId }).then(res => {
          this.modalLoading = false
          const item = res.data
          this.form = item
          if (item.parentId === 0) {
            this.form.parentId = undefined
            this.rules.parentId[0].required = false
          }
        })
      },
      // 获取父节点树
      getParentTree(record) {
        getParentTree({ projectId: record?.projectId }).then(res => {
          this.parentOptions = res?.data || []
        })
      },

      onProjectSelect(selectedKeys) {
        getNextSort({ parentId: selectedKeys }).then(res => {
          this.form.sort = res.data
        })
      },

      onDeptSelect(selectedKeys) {
        this.form.deptId = selectedKeys
      },
      
      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            const saveForm = this.form
            const params = { ...saveForm, parentId: saveForm.parentId || 0 }
            if (this.form.projectId !== undefined) {
              updateProject(params)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok', this.rowInfo, params)
                })
                .catch(err => (this.loading = false))
            } else {
              addProject(params)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok', this.rowInfo, params)
                })
                .catch(err => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
