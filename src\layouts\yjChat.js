import axios from 'axios'

const aiUrl = 'http://192.168.18.226:8399'

// 创建axios实例
const api = axios.create({
  baseURL: aiUrl,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  },
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    return Promise.reject(error)
  },
)

// 聊天服务
export const chatService = {
  // 同步聊天
  chat(data) {
    return api.post('/chat/completions', data)
  },

  // 流式聊天 - 使用SSE (Server-Sent Events)
  chatStreamSSE(data, onMessage, onError, onComplete) {
    // 确保请求参数中stream设置为true
    const requestData = { ...data, stream: true }

    // 准备请求参数
    const params = new URLSearchParams()
    params.append('data', JSON.stringify(requestData))

    // 创建SSE连接
    const eventSource = new EventSource(aiUrl + `/api/chat/stream?${params.toString()}`, {
      withCredentials: false,
    })

    // 监听消息事件
    eventSource.onmessage = event => {
      if (onMessage && event.data) {
        onMessage(event.data)
      }
    }

    // 监听错误事件
    eventSource.onerror = error => {
      if (onError) {
        onError(error)
      }
      eventSource.close()
    }

    // 返回清理函数
    return {
      close: () => {
        if (eventSource) {
          eventSource.close()
          if (onComplete) {
            onComplete()
          }
        }
      },
    }
  },

  // POST方式的流式聊天
  chatStream(data, onProgress) {
    console.log('发起流式请求:', data)

    // 确保stream参数为true
    const requestData = { ...data, stream: true }

    const xhr = new XMLHttpRequest()
    let url = process.env.VUE_APP_ENV
    xhr.open('POST', aiUrl + '/api/chat/stream', true)
    xhr.setRequestHeader('Content-Type', 'application/json')
    xhr.setRequestHeader('Accept', 'text/event-stream')
    xhr.onreadystatechange = function () {
      // readyState 4 代表请求完成
      if (xhr.readyState === 4) {
        // 这里不做事，交给外部处理
      }
    }
    xhr.onprogress = onProgress
    xhr.send(JSON.stringify(requestData))
    return xhr // 返回xhr实例
  },
}

export default chatService
