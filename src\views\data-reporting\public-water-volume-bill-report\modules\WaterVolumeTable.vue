<template>
  <div class="water-volume-section">
    <div class="section-header" @click="toggleCollapse">
      <h3 class="section-title">
        <a-icon :type="collapsed ? 'right' : 'down'" style="margin-right: 8px;" />
        {{ title }}
      </h3>
      <a-button v-if="!readonly" type="primary" @click.stop="handleSelectChannel">选择渠道</a-button>
    </div>
    
    <div v-show="!collapsed" class="section-content">
      <!-- 折算率输入框 -->
      <div class="conversion-rate-section">
        <label>折算率：</label>
        <a-input-number
          v-model="conversionRatePercent"
          :precision="0"
          :min="0"
          :max="100"
          style="width: 150px"
          placeholder="请输入折算率"
          :disabled="readonly"
          @change="handleConversionRatePercentChange"
        />
        <span style="margin-left: 4px;">%</span>
      </div>

      <!-- 复杂表格 -->
      <vxe-table
        :ref="tableRef"
        :data="tableData"
        border
        stripe
        :row-config="{ keyField: 'id' }"
        :column-config="{ resizable: true }"
        :edit-config="{ trigger: 'manual', mode: 'cell', showStatus: true }"
        height="400"
        class="water-volume-table"
        width="100%"
        header-align="center"
        align="center"
        show-footer
        :footer-method="footerMethod"
      >
        <!-- 直口渠名列 -->
        <vxe-column field="channelName" title="" width="120" fixed="left">
          <template #header>
            <div class="first-col">
              <div class="first-col-top">项目</div>
              <div class="first-col-bottom">直口渠名</div>
            </div>
          </template>
          <template #default="{ row }">
            <span v-if="row.channelName" :title="row.channelCode">
              {{ row.channelName }}
            </span>
            <span v-else style="color: #999; font-style: italic;">
              请选择渠道
            </span>
          </template>
        </vxe-column>

        <!-- 水资源费价格列 -->
        <vxe-column field="waterFeePrice20" title="超水20%内的水资源费价格（元）" width="180">
          <template #default="{ row }">
            <a-input-number
              v-model="row.waterFeePrice20"
              :precision="2"
              :min="0"
              style="width: 100%"
              :disabled="readonly"
              @change="handleDataChange(row, 'waterFeePrice20')"
              @input="handleDataChange(row, 'waterFeePrice20')"
            />
          </template>
        </vxe-column>

        <vxe-column field="waterFeePrice50" title="超水20%-50%的水资源费价格（元）" width="200">
          <template #default="{ row }">
            <a-input-number
              v-model="row.waterFeePrice50"
              :precision="2"
              :min="0"
              style="width: 100%"
              :disabled="readonly"
              @change="handleDataChange(row, 'waterFeePrice50')"
              @input="handleDataChange(row, 'waterFeePrice50')"
            />
          </template>
        </vxe-column>

        <!-- 动态渲染季度列 -->
        <template v-for="season in currentSeasons">
          <vxe-colgroup :key="season.key" :title="season.title">
            <!-- 包干水量 -->
            <vxe-column :field="`${season.key}_contractWater`" title="包干水量（万m³）" width="140">
              <template #default="{ row }">
                <a-input-number
                  v-model="row[`${season.key}_contractWater`]"
                  :precision="2"
                  :min="0"
                  style="width: 100%"
                  :disabled="readonly"
                  @change="handleDataChange(row, `${season.key}_contractWater`)"
                  @input="handleDataChange(row, `${season.key}_contractWater`)"
                />
              </template>
            </vxe-column>

            <!-- 实用水量分组 -->
            <vxe-colgroup :title="'实用水量（万m³）'">
              <!-- 月份列 -->
              <template v-for="month in season.months">
                <vxe-column  :key="month" :field="`${season.key}_month${month}`" :title="`${month}月`" width="80">
                  <template #default="{ row }">
                    <a-input-number
                      v-model="row[`${season.key}_month${month}`]"
                      :precision="2"
                      :min="0"
                      style="width: 100%"
                      :disabled="readonly"
                      @change="handleDataChange(row, `${season.key}_month${month}`)"
                      @input="handleDataChange(row, `${season.key}_month${month}`)"
                    />
                  </template>
                </vxe-column>
              </template>

              <!-- 合计水量（自动计算） -->
              <vxe-column :field="`${season.key}_totalWater`" title="合计水量" width="80">
                <template #default="{ row }">
                  <span style="font-weight: bold; color: #1890ff;">
                    {{ formatNumber(calculateSeasonTotal(row, season.key)) }}
                  </span>
                </template>
              </vxe-column>
            </vxe-colgroup>

            <!-- 多元化供水 -->
            <vxe-column :field="`${season.key}_diversifiedWater`" title="多元化供水（万m³）" width="170">
              <template #default="{ row }">
                <a-input-number
                  v-model="row[`${season.key}_diversifiedWater`]"
                  :precision="2"
                  :min="0"
                  style="width: 100%"
                  :disabled="readonly"
                  @change="handleDataChange(row, `${season.key}_diversifiedWater`)"
                  @input="handleDataChange(row, `${season.key}_diversifiedWater`)"
                />
              </template>
            </vxe-column>
          </vxe-colgroup>
        </template>
      </vxe-table>
    </div>

    <!-- 选择渠道弹窗 -->
    <ChannelSelectModal
      ref="channelSelectModal"
      @ok="handleChannelSelectOk"
    />
  </div>
</template>

<script>
import ChannelSelectModal from './ChannelSelectModal.vue'

export default {
  name: 'WaterVolumeTable',
  components: {
    ChannelSelectModal
  },
  props: {
    title: {
      type: String,
      required: true
    },
    tableData: {
      type: Array,
      default: () => []
    },
    tableRef: {
      type: String,
      default: 'waterVolumeTableRef'
    },
    readonly: {
      type: Boolean,
      default: false
    },
    seasons: {
      type: Array,
      default: () => []
    },
    conversionRateValue: {
      type: Number,
      default: 0
    },
    reportUnit: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      collapsed: false,
      // 本地折算率值，用于处理输入框的双向绑定
      localConversionRate: 0,
      // 防抖计时器
      updateTimer: null,
      // 默认季度配置（当没有传入seasons时使用）
      defaultSeasons: [
        {
          key: 'spring',
          title: '4月-6月',
          months: [4, 5, 6]
        },
        {
          key: 'summer',
          title: '7月-9月',
          months: [7, 8, 9]
        },
        {
          key: 'autumn',
          title: '10月-11月',
          months: [10, 11]
        }
      ]
    }
  },
  watch: {
    // 监听父组件传递的折算率变化
    conversionRateValue: {
      handler(newVal) {
        this.localConversionRate = (newVal || 0) * 100
      },
      immediate: true
    },

    // 深度监听表格数据变化，确保合计行实时更新
    tableData: {
      handler(newVal, oldVal) {
        // 当表格数据发生变化时，强制更新表格
        this.$nextTick(() => {
          if (this.$refs[this.tableRef]) {
            this.$refs[this.tableRef].updateData()
          }
        })
      },
      deep: true,
      immediate: false
    }
  },
  computed: {
    // 使用传入的seasons或默认的seasons
    currentSeasons() {
      return this.seasons && this.seasons.length > 0 ? this.seasons : this.defaultSeasons
    },

    // 检查表格是否有数据
    hasTableData() {
      if (!this.tableData || this.tableData.length === 0) {
        return false
      }

      // 检查是否有任何一行有实际数据
      return this.tableData.some(row => this.checkRowHasData(row))
    },

    // 计算属性：折算率百分数显示，用于输入框绑定
    conversionRatePercent: {
      get() {
        return this.localConversionRate
      },
      set(value) {
        this.localConversionRate = value
        // 将百分数转换为小数传递给父组件
        this.handleConversionRateChange(value / 100)
      }
    },

    // 计算属性：折算率小数值，用于内部计算和传参
    conversionRate: {
      get() {
        return this.localConversionRate / 100
      },
      set(value) {
        this.localConversionRate = value * 100
        this.handleConversionRateChange(value)
      }
    },



    // 计算属性：动态计算合计数据
    footerData() {
      if (!this.tableData || this.tableData.length === 0) {
        return []
      }

      const summaryRow = ['合计'] // 第一列显示"合计"

      // 水资源费价格列
      const waterFeePrice20Sum = this.tableData.reduce((sum, row) => {
        const value = Number(row.waterFeePrice20 || 0)
        return sum + (isNaN(value) ? 0 : value)
      }, 0)
      const waterFeePrice50Sum = this.tableData.reduce((sum, row) => {
        const value = Number(row.waterFeePrice50 || 0)
        return sum + (isNaN(value) ? 0 : value)
      }, 0)
      summaryRow.push(this.formatNumber(waterFeePrice20Sum))
      summaryRow.push(this.formatNumber(waterFeePrice50Sum))

      // 动态季度列的合计
      this.currentSeasons.forEach(season => {
        // 包干水量合计
        const contractWaterSum = this.tableData.reduce((sum, row) => {
          const value = Number(row[`${season.key}_contractWater`] || 0)
          return sum + (isNaN(value) ? 0 : value)
        }, 0)
        summaryRow.push(this.formatNumber(contractWaterSum))

        // 月份实用水量合计
        season.months.forEach(month => {
          const monthSum = this.tableData.reduce((sum, row) => {
            const value = Number(row[`${season.key}_month${month}`] || 0)
            return sum + (isNaN(value) ? 0 : value)
          }, 0)
          summaryRow.push(this.formatNumber(monthSum))
        })

        // 季度合计水量（计算所有月份的总和）
        let seasonTotalSum = 0
        season.months.forEach(month => {
          const monthSum = this.tableData.reduce((sum, row) => {
            const value = Number(row[`${season.key}_month${month}`] || 0)
            return sum + (isNaN(value) ? 0 : value)
          }, 0)
          seasonTotalSum += monthSum
        })
        summaryRow.push(this.formatNumber(seasonTotalSum))

        // 多元化供水合计
        const diversifiedWaterSum = this.tableData.reduce((sum, row) => {
          const value = Number(row[`${season.key}_diversifiedWater`] || 0)
          return sum + (isNaN(value) ? 0 : value)
        }, 0)
        summaryRow.push(this.formatNumber(diversifiedWaterSum))
      })

      return [summaryRow]
    }
  },
  methods: {
    // 切换折叠状态
    toggleCollapse() {
      this.collapsed = !this.collapsed
    },
    
    // 选择渠道
    handleSelectChannel() {
      if (this.readonly) {
        return
      }

      // 检查填报单位是否已选择
      if (!this.reportUnit) {
        this.$message.warning('请先选择填报单位')
        return
      }

      // 将当前已选择的渠道数据传递给弹窗
      const currentSelectedChannels = this.tableData.map(row => ({
        projectId: row.projectId || row.id || row.channelCode, // 优先使用原始projectId
        projectCode: row.channelCode,
        projectName: row.channelName,
        id: row.projectId || row.id || row.channelCode
      }))

      console.log('打开渠道选择弹窗，当前已选择的渠道:', currentSelectedChannels)
      console.log('传递给弹窗的填报单位:', this.reportUnit)
      this.$refs.channelSelectModal.show(currentSelectedChannels, this.reportUnit)
    },
    
    // 渠道选择确认
    handleChannelSelectOk(selectedChannels) {
      // 保存现有数据的映射，以便保持已有渠道的数据
      const existingDataMap = {}
      this.tableData.forEach(row => {
        if (row.channelCode) {
          existingDataMap[row.channelCode] = { ...row }
        }
      })

      // 清空现有数据
      this.tableData.splice(0)

      // 添加选中的渠道
      selectedChannels.forEach((channel, index) => {
        const channelCode = channel.projectCode
        const existingData = existingDataMap[channelCode]

        let rowData
        if (existingData) {
          // 如果是已有渠道，保持原有数据
          rowData = { ...existingData }
          console.log(`保持已有渠道${channelCode}的数据:`, rowData)
        } else {
          // 如果是新渠道，初始化数据
          // 根据响应示例，初始化一些基础值
          rowData = {
            id: channel.projectId || channel.id || `${this.title}_${index}`,
            channelName: channel.projectName,
            channelCode: channelCode,
            projectId: channel.projectId || channel.id,
            waterFeePrice20: 0, // 初始化为0，让用户自己填写
            waterFeePrice50: 0  // 初始化为0，让用户自己填写
          }

          // 初始化季度数据，根据示例数据设置一些初始值
          this.currentSeasons.forEach((season, seasonIndex) => {
            // 包干水量初始化为0，让用户自己填写
            rowData[`${season.key}_contractWater`] = 0
            
            // 多元化供水初始化为0，让用户自己填写
            rowData[`${season.key}_diversifiedWater`] = 0
            
            // 月份实用水量初始化为0，用户可以根据需要修改
            season.months.forEach((month, monthIndex) => {
              // 所有月份初始化为0，让用户自己填写
              rowData[`${season.key}_month${month}`] = 0
            })
          })

          console.log(`新增渠道${channelCode}，初始化数据:`, rowData)
        }

        this.tableData.push(rowData)
      })

      this.$emit('table-data-change', this.tableData)
    },
    
    // 折算率百分数变化处理
    handleConversionRatePercentChange(value) {
      if (this.readonly) {
        return
      }
      // 将百分数转换为小数传递给父组件
      const decimalValue = (value || 0) / 100
      this.$emit('conversion-rate-change', { title: this.title, rate: decimalValue })
    },

    // 折算率变化（内部使用，已经是小数）
    handleConversionRateChange(value) {
      if (this.readonly) {
        return
      }
      this.$emit('conversion-rate-change', { title: this.title, rate: value })
    },

    // 检查行是否有数据
    checkRowHasData(row) {
      if (!row) return false

      // 检查是否有包干水量、实用水量或多元化供水数据
      let hasData = false

      // 检查所有季度的数据
      this.currentSeasons.forEach(season => {
        // 检查包干水量
        const contractWater = row[`${season.key}_contractWater`]
        if (contractWater !== null && contractWater !== undefined && Number(contractWater) > 0) {
          hasData = true
        }

        // 检查月份实用水量
        season.months.forEach(month => {
          const monthWater = row[`${season.key}_month${month}`]
          if (monthWater !== null && monthWater !== undefined && Number(monthWater) > 0) {
            hasData = true
          }
        })

        // 检查多元化供水
        const diversifiedWater = row[`${season.key}_diversifiedWater`]
        if (diversifiedWater !== null && diversifiedWater !== undefined && Number(diversifiedWater) > 0) {
          hasData = true
        }
      })

      return hasData
    },

    // 数据变化
    handleDataChange(row, field) {
      if (this.readonly) {
        return
      }
      
      // 确保数值类型正确
      if (row[field] !== null && row[field] !== undefined) {
        const numValue = Number(row[field])
        if (!isNaN(numValue)) {
          // 使用Vue.set确保响应式更新
          this.$set(row, field, numValue)
        }
      }
      
      // 立即触发响应式更新（用于实时显示合计）
      this.$forceUpdate()
      
      // 防抖处理表格更新和事件发送
      if (this.updateTimer) {
        clearTimeout(this.updateTimer)
      }
      
      this.updateTimer = setTimeout(() => {
        // 确保数据变化时触发响应式更新
        this.$nextTick(() => {
          // 强制更新表格
          if (this.$refs[this.tableRef]) {
            this.$refs[this.tableRef].updateData()
          }
        })
        
        this.$emit('row-change', { row, field, title: this.title })
        this.$emit('table-data-change', this.tableData)
      }, 100) // 100ms防抖延迟
    },
    
    // 计算季度合计
    calculateSeasonTotal(row, seasonKey) {
      const season = this.currentSeasons.find(s => s.key === seasonKey)
      if (!season || !row) return 0
      
      let total = 0
      season.months.forEach(month => {
        const value = row[`${seasonKey}_month${month}`] || 0
        const numValue = Number(value)
        if (!isNaN(numValue)) {
          total += numValue
        }
      })
      
      return total
    },
    
    // 格式化数字
    formatNumber(value) {
      if (value === null || value === undefined || value === '' || isNaN(value)) {
        return '0.00'
      }
      const numValue = Number(value)
      if (isNaN(numValue)) {
        return '0.00'
      }
      return numValue.toFixed(2)
    },





    // 表格底部合计方法（简化版，主要使用计算属性footerData）
    footerMethod() {
      // 使用计算属性footerData来提供合计数据
      return this.footerData
    }
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer)
      this.updateTimer = null
    }
  }
}
</script>

<style lang="less" scoped>
.water-volume-section {
  margin-bottom: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  
  .section-header {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    gap: 50px;
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    cursor: pointer;
    user-select: none;
    
    &:hover {
      background: #f0f0f0;
    }
    
    .section-title {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      display: flex;
      align-items: center;
    }
  }
  
  .section-content {
    padding: 16px;
    
    .conversion-rate-section {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      label {
        margin-right: 8px;
        font-weight: 500;
        color: #333;
      }
    }
    
    .water-volume-table {
      /deep/ .vxe-table--border-line {
        border-color: #e8e8e8;
      }
      
      /deep/ .vxe-header--column {
        background-color: #fafafa;
        font-weight: 600;
        font-size: 12px;
      }
      
      /deep/ .vxe-body--row {
        &:nth-child(even) {
          background-color: #fafafa;
        }
      }
      
      /deep/ .vxe-table {
        width: 100% !important;
        font-size: 12px;
      }
      
      /deep/ .vxe-cell {
        padding: 4px 8px;
      }

      // 底部合计行样式
      /deep/ .vxe-table--footer-wrapper {
        .vxe-footer--row {
          background-color: #f0f8ff !important;
          font-weight: bold;
          color: #1890ff;

          .vxe-footer--column {
            background-color: #f0f8ff !important;
            border-top: 2px solid #1890ff;
            text-align: center;
          }
        }
      }
    }
  }

  // 表头分割样式
  ::v-deep(.vxe-table--header) {
    .first-col {
      position: relative;
      height: 20px;
      &:before {
        content: '';
        position: absolute;
        left: -32px;
        top: 0px;
        width: 148px;
        height: 1px;
        transform: rotate(50deg);
        background-color: #e8eaec;
      }
      .first-col-top {
        position: absolute;
        right: 4px;
        top: -40px;
      }
      .first-col-bottom {
        position: absolute;
        left: 4px;
        bottom: -40px;
      }
    }
  }
}
</style>
