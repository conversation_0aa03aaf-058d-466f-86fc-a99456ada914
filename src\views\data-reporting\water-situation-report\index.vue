<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <!-- @ok="handleQuery" -->
      <a-form-item label="日期">
        <a-date-picker
          v-model="queryParam.fillDate"
          placeholder="请选择"
          allow-clear
          style="width: 100%"
          @keyup.enter.native="handleQuery"
          @change="onDateChange"
        ></a-date-picker>
      </a-form-item>

      <a-form-item label="所属轮次">
        <div class="round-display">
          <a-spin :spinning="roundLoading" size="small">
            <span class="round-text">{{ roundDisplayText || '请先选择日期' }}</span>
          </a-spin>
        </div>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="水情填报"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
          align="center"
          header-align="center"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="primary" @click="handleImport()">
              <a-icon type="import" />
              导入
            </a-button>
            <a-button type="primary" @click="handleExport()">
              <a-icon type="export" />
              导出
            </a-button>
          </div>
        </VxeTable>

        <FormDrawer v-if="showFormDrawer" ref="formDrawerRef" @ok="getList" @close="showFormDrawer = false" />
        <DetailsDrawer v-if="showDetails" ref="detailsRef" @ok="getList" @close="showDetails = false" />
        <ImportDrawer v-if="showImport" ref="importRef" @ok="getList" @close="showImport = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormDrawer from './modules/FormDrawer.vue'
  import DetailsDrawer from './modules/DetailsDrawer.vue'
  import ImportDrawer from './modules/ImportDrawer.vue'
  import moment from 'moment'
  import { getIrrigationWaterSituationPage, deleteIrrigationWaterSituation, getIrrigationRound } from './service'

  export default {
    name: 'WaterSituationReport',
    components: {
      VxeTableForm,
      VxeTable,
      FormDrawer,
      DetailsDrawer,
      ImportDrawer,
    },
    data() {
      return {
        showFormDrawer: false,
        showDetails: false,
        showImport: false,
        loading: false,
        roundLoading: false,
        total: 0,
        isChecked: false,
        ids: [],

        // 轮次显示文本
        roundDisplayText: '',

        queryParam: {
          pageNum: 1,
          pageSize: 10,
          fillDate: '',
          round: undefined,
          sort: [],
        },

        list: [],

        columns: [
          { type: 'seq', title: '序号', width: 50, align: 'center', headerAlign: 'center' },
          {
            title: '日期',
            field: 'fillDate',
            minWidth: 120,
            align: 'center',
            headerAlign: 'center',
          },
          {
            title: '所属轮次',
            field: 'roundType',
            minWidth: 120,
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                const roundMap = { 1: '春夏灌', 2: '秋灌', 3: '秋浇' }
                return roundMap[row.roundType] || ''
              }
            }
          },
          {
            title: '8时流量合计(m³/s)',
            field: 'eightFlowRateTotal',
            minWidth: 150,
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                return row.eightFlowRateTotal || '--'
              }
            }
          },
          {
            title: '18时流量合计(m³/s)',
            field: 'eighteenFlowRateTotal',
            minWidth: 150,
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                return row.eighteenFlowRateTotal || '--'
              }
            }
          },
          {
            title: '灌域日均流量(m³/s)',
            field: 'dailyFlowRateSum',
            minWidth: 150,
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                return row.dailyFlowRateSum || '--'
              }
            }
          },
          {
            title: '灌域累计流量(m³)',
            field: 'dailyFlowRateTotalSum',
            minWidth: 150,
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                return row.dailyFlowRateTotalSum || '--'
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            minWidth: 180,
            align: 'center',
            headerAlign: 'center',
            slots: {
              default: ({ row }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleUpdate(row)}>编辑</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 日期变化处理 */
      onDateChange(date) {
        // 清空当前轮次信息
        this.queryParam.round = undefined
        this.roundDisplayText = ''
        
        if (date) {
          this.getRoundOptions(date)
        } else {
          this.roundDisplayText = ''
        }
      },
      /** 根据日期获取轮次信息 */
      async getRoundOptions(date) {
        this.roundLoading = true
        try {
          const formatDate = moment(date).format('YYYY-MM-DD')
          const response = await getIrrigationRound(formatDate, formatDate)
          
          if (response.success && response.data && response.data.irrigationRound) {
            // 根据irrigationRound的值来设置轮次显示文本
            const roundValue = response.data.irrigationRound
            const roundMap = {
              1: '春夏灌',
              2: '秋灌',
              3: '秋浇'
            }
            
            // 设置显示文本和查询参数
            if (roundMap[roundValue]) {
              this.roundDisplayText = roundMap[roundValue]
              this.queryParam.round = roundValue.toString()
            } else {
              this.roundDisplayText = '未知轮次'
              this.queryParam.round = undefined
              this.$message.warning('该日期没有对应的轮次信息')
            }
          } else {
            this.roundDisplayText = '无轮次信息'
            this.queryParam.round = undefined
            this.$message.warning('该日期没有对应的轮次信息')
          }
        } catch (error) {
          console.error('获取轮次失败:', error)
          this.roundDisplayText = '获取失败'
          this.queryParam.round = undefined
          this.$message.error('获取轮次信息失败')
        } finally {
          this.roundLoading = false
        }
      },
      /** 查询列表 */
      async getList() {
        this.loading = true
        try {
          const params = {
            fillDate: this.queryParam.fillDate ? moment(this.queryParam.fillDate).format('YYYY-MM-DD') : '',
            roundType: this.queryParam.round,
            pageNum: this.queryParam.pageNum,
            pageSize: this.queryParam.pageSize,
            sort: this.queryParam.sort
          }
          
          const response = await getIrrigationWaterSituationPage(params)
          
          if (response.success) {
            this.list = response.data.data || []
            this.total = response.data.total || 0
          } else {
            this.$message.error(response.message || '获取数据失败')
          }
        } catch (error) {
          console.error('获取数据失败:', error)
          this.$message.error('获取数据失败')
        } finally {
          this.loading = false
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          pageNum: 1,
          fillDate: '',
          round: undefined,
          sort: [],
        }
        this.roundDisplayText = ''
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.id)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      /* 新增 */
      handleAdd() {
        // 获取列表第一条数据的日期
        const firstRecordDate = this.list.length > 0 ? this.list[0].fillDate : null
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleAdd(firstRecordDate))
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showDetails = true
        this.$nextTick(() => this.$refs.detailsRef.showDetails(record))
      },
      /* 导入 */
      handleImport() {
        // this.showImport = true
      },
      /* 导出 */
      handleExport() {
        // this.$message.success('导出成功')
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const id = row.id || this.ids[0]
        const fillDate = row.fillDate || this.list.find(item => item.id === this.ids[0])?.fillDate

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中日期为"' + fillDate + '"的数据',
          async onOk() {
            try {
              const response = await deleteIrrigationWaterSituation(id)
              
              if (response.success) {
            that.$message.success('删除成功')
                // 删除成功后重新加载数据
            that.getList()
              } else {
                that.$message.error(response.message || '删除失败')
              }
            } catch (error) {
              console.error('删除失败:', error)
              that.$message.error('删除失败')
            }
          },
          onCancel() {},
        })
      },
    },
    created() {
      this.getList()
    },
  }
</script>

<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      margin-right: 8px;
      &:last-child {
        margin-right: 0px;
      }
    }
  }

  .round-display {
    height: 32px;
    line-height: 32px;
    padding: 0 11px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;
    color: #666;
    
    .round-text {
      display: inline-block;
      width: 100%;
    }
  }

  /deep/ .vxe-header--column,
  /deep/ .vxe-body--column {
    text-align: center !important;
    justify-content: center !important;
    align-items: center !important;
  }
</style>
