import request from '@/utils/request'

// 列表分页查询
export function getOperateCmdPage(data) {
  return request({ url: '/custom/operateCmd/page', method: 'post', data })
}

// 详情
export function getOperateCmdById(params) {
  return request({
    url: '/custom/operateCmd/get',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    params,
  })
}

export function syncOperateCmd() {
  return request({ url: '/dw/timer/history/insertDispatchOperateCmd ', method: 'post' })
}
