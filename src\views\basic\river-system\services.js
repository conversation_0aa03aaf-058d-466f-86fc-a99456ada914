import request from '@/utils/request'

// 水利对象分类-根据编码获取树
export function getTreeByCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params: { ...params, objectCategoryCode: 'RL' }
  })
}

// 水系-列表分页查询
export function getRiverSystemPage(data) {
  return request({
    url: '/base/riverSystem/page',
    method: 'post',
    data
  })
}

//水系-删除
export function deleteRiverSystem(params) {
  return request({
    url: '/base/riverSystem/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 水系-新增
export function addRiverSystem(data) {
  return request({
    url: '/base/riverSystem/add',
    method: 'post',
    data
  })
}

// 水系-更新
export function updateRiverSystem(data) {
  return request({
    url: '/base/riverSystem/update',
    method: 'post',
    data
  })
}

// 水系-详情
export function getRiverSystem(params) {
  return request({
    url: '/base/riverSystem/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
