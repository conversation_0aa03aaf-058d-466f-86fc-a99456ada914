<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="区划编码" prop="districtCode">
                <a-input allowClear v-model="form.districtCode" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="区划名称" prop="districtName">
                <a-input allowClear v-model="form.districtName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="上级目录" prop="parentId">
                <a-tree-select
                  v-model="form.parentId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="parentCategoryOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'districtName',
                    key: 'districtId',
                    value: 'districtId'
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="排序号" prop="sort">
                <a-input-number v-model="form.sort" style="width: 100%" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="备注" prop="remark">
                <a-textarea v-model="form.remark" placeholder="请输入" allow-clear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getDistrictParentTree, addDistrict, getDistrictNextSort, updateDistrict, getDistrict } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormDrawer',
    props: {},
    components: { AntModal },
    data() {
      return {
        parentCategoryOptions: [],

        loading: false,
        modalLoading: false,
        formTitle: '',
        // 表单参数
        form: {
          districtName: undefined,
          districtCode: undefined,
          districtId: undefined,
          parentId: undefined,
          sort: undefined,
          remark: undefined
        },
        open: false,

        rules: {
          districtName: [{ required: true, message: '区划名称不能为空', trigger: 'blur' }],
          parentId: [{ required: true, message: '上级目录不能为空', trigger: 'change' }],
          districtCode: [{ required: true, message: '区划编码不能为空', trigger: 'blur' }],
          sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
        }
      }
    },
    filters: {},
    created() {
      // 字典获取下拉
    },
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd(parentId) {
        this.open = true
        this.formTitle = '新增'
        this.form.parentId = parentId || undefined
        this.getParentTree()

        getDistrictNextSort({ parentId }).then(res => {
          this.form.sort = res.data
        })
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.getParentTree(record)

        this.modalLoading = true
        getDistrict({ districtId: record.districtId }).then(res => {
          this.form = res.data
          this.modalLoading = false
        })
      },
      // 获取父节点树
      getParentTree(record) {
        getDistrictParentTree({ districtId: record?.districtId }).then(res => {
          this.parentCategoryOptions = res?.data || []
        })
      },
      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            const saveForm = JSON.parse(JSON.stringify(this.form))
            this.loading = true

            if (this.form.districtId !== undefined) {
              updateDistrict(saveForm)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            } else {
              addDistrict(saveForm)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            }
          } else {
            return false
          }
        })
      }
    }
  }
</script>
