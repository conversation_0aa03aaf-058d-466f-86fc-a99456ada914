import request from '@/utils/request'

// 行政区划-列表分页查询
export function getDistrictPage(data) {
  return request({
    url: '/base/district/page',
    method: 'post',
    data
  })
}

// 行政区划-删除
export function DeleteDistric(params) {
  return request({
    url: '/base/district/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 行政区划-获取父节点树
export function getDistrictParentTree(params) {
  return request({
    url: '/base/district/getParentTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 行政区划-获取排序号
export function getDistrictNextSort(params) {
  return request({
    url: '/base/district/getNextSort',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 行政区划-新增
export function addDistrict(data) {
  return request({
    url: '/base/district/add',
    method: 'post',
    data
  })
}

// 行政区划-更新
export function updateDistrict(data) {
  return request({
    url: '/base/district/update',
    method: 'post',
    data
  })
}

// 行政区划-详情
export function getDistrict(params) {
  return request({
    url: '/base/district/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
